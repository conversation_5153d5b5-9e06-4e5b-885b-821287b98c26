buildscript {
    ext {
        compose_ui_version = '1.5.0'
        room_version = '2.5.2'
        work_version = '2.8.1'
        hilt_version = '1.0.0'
        nav_version = '2.7.0'
        coroutine_version = '1.6.4'
        data_store = '1.0.0'
        apollo = '3.8.2'
        coil = '2.4.0'
        kotlin_version = '1.8.21'
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.

plugins {
    id 'com.android.application' version '8.0.1' apply false
    id 'com.android.library' version '8.0.1' apply false
    id 'org.jetbrains.kotlin.android' version "$kotlin_version" apply false
    id("com.google.dagger.hilt.android") version "2.51.1" apply false
    id "org.jetbrains.kotlin.plugin.serialization" version "$kotlin_version"
    id "com.apollographql.apollo3" version "$apollo" apply false
    id 'com.google.gms.google-services' version '4.4.0' apply false
    id("com.google.firebase.crashlytics") version "2.9.9" apply false
    id 'com.google.firebase.firebase-perf' version '1.4.2' apply false
}


