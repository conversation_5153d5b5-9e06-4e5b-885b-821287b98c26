name: Build and Tag Release APK

on:
  pull_request:
    branches: 
      - master
    types: [closed]
    
jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: gradle

    - name: Set up Android SDK
      uses: android-actions/setup-android@v2
    - name: Install NDK
      run: sdkmanager "ndk;25.2.9519653"

    - name: Generate release tag
      id: generate_release_tag
      uses: alexvingg/next-release-tag@v1.0.4
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        tag_prefix: "SWP"
    - name: Bump version
      uses: chkfung/android-version-actions@v1.2.1
      with:
          gradlePath: app/build.gradle
          versionCode: ${{github.run_number}}
          versionName: ${{ steps.generate_release_tag.outputs.release_tag }}
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    - name: Decode Keystore
      id: decode_keystore
      uses: timheuer/base64-to-file@v1.2
      with:
        fileName: 'android_keystore.jks'
        fileDir: '/home/<USER>/work/swiftpos-native/swiftpos-native/app/keystore/'
        encodedString: ${{ secrets.KEYSTORE }}
    - name: Build APK
      run: ./gradlew assembleRelease
      env:
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          
    - name: Create Release
      id: create_release
      uses: comnoco/create-release-action@v2.0.5
      env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
          tag_name: ${{ steps.generate_release_tag.outputs.release_tag }} 
          release_name: ${{ steps.generate_release_tag.outputs.release_tag }}
          body: |
            APK built from commit: ${{ github.sha }}
          draft: false
          prerelease: false

    - name: Upload APK to Release
      uses: svenstaro/upload-release-action@v2
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: ./app/build/outputs/apk/release/app-release.apk
        asset_name: ${{ format('app-release-{0}.apk', steps.generate_release_tag.outputs.release_tag) }}
        tag: ${{ steps.generate_release_tag.outputs.release_tag }} 
        
    # - name: Upload APK to Release
    #   uses: actions/upload-release-asset@v1
    #   env:
    #       GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    #   with:
    #       upload_url: ${{ steps.create_release.outputs.upload_url }}
    #       asset_path: ./app/build/outputs/apk/release/app-release.apk
    #       asset_name: ${{ format('app-release-{0}.apk', steps.generate_release_tag.outputs.release_tag) }}
    #       asset_content_type: application/vnd.android.package-archive
