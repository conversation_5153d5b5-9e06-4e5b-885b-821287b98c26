query GetRewards($storeCode: String!, $transaction: String!, $loyaltyAccountId: String!) {
    getRewards(storeCode: $storeCode, transaction: $transaction, loyaltyAccountId: $loyaltyAccountId) {
        status
        msg
        data {
            rewards {
                transactionItemId
                loyaltyAccountNumber
                originalQuantity
                discountQuantity
                discount {
                    discountId
                    discountText
                    discountAmount
                }
            }
        }
    }
}
