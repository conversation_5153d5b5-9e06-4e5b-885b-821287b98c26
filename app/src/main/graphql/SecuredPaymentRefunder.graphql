mutation securedPaymentRefunder(
    $eventEpoch: Int!
    $tdata: RIM!
    $uid: String!
) {
    securedPaymentRefunder(
        eventEpoch: $eventEpoch
        tdata: $tdata
        uid: $uid
    ) {
        responseMessage {
            eventid
            msg
            status
            data {
                mid
                amount
                txnId
                txnTime
                gatewayProcessTime
            }
            details
            payfacInfo {
                applicationLabel
                approvalCode
                cardBrand
                cardEntryMode
                cardHolderName
                cardNumber
                errCode
                errMsg
                failureCode
                failureMessage
                ok
                paymentType
                pinVerified
                infoMsg
            }
        }
    }
}
