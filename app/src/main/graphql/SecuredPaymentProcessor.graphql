mutation securedPaymentProcessor(
    $eventEpoch: Int!
    $tdata: TIM!
    $uid: String!
    $processorType: String = "transfer"
) {
    securedPaymentProcessor(
        eventEpoch: $eventEpoch
        tdata: $tdata
        uid: $uid
        processorType: $processorType
    ) {
        responseMessage {
            eventid
            msg
            status
            data {
                mid
                amount
                txnId
                txnTime
                gatewayProcessTime
            }
            details
            payfacInfo {
                applicationLabel
                approvalCode
                cardBrand
                cardEntryMode
                cardHolderName
                cardNumber
                errCode
                errMsg
                failureCode
                failureMessage
                ok
                paymentType
                pinVerified
                infoMsg
            }
        }
    }
}
