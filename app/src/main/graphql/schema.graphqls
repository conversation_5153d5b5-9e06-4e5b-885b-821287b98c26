type MasterQuery {
    fetchSgCreds(posid: String!): SGCredsResponse

    fetchTxn(txnId: String!): TxnResponse

    getRewards(storeCode: String!, transaction: String!, loyaltyAccountId: String!): LoyaltyGetRewardsResponse

    nodeGlobal("The ID of the object" id: ID!): RelayNode

    salesSummaryData(
        year: Int
        fmt: String
        terminal: String
        storeCode: String!
        endStamp: String
        startStamp: String
        drawerAmount: Float
        preset: String
        shiftId: String
        batchId: String
    ): SalesSummaryDataResponse

    historicalShifts(
        storeCode: String!
        startDate: String
        endDate: String
        terminalId: String
        cashierId: String
    ): HistoricalShiftReportsResponse

    historicalBatches(
        storeCode: String!
        startDate: String
        endDate: String
        terminalId: String
    ): HistoricalBatchReportsResponse

    fetchTerminalConfig(
        posid: String!
    ): TerminalCfgResponse

    pricebook(
        storeCode: String!
        searchTerm: String!
    ): PricebookQueryResponse
}

input PricebookQueryFilter {
    key: String!
    val: String!
}

type PricebookQueryResponse {
    status: Boolean
    msg: String
    data: [Pricebook]
}

type Pricebook {
    description: String!
    pluId: String!
    pluModifier: String!
    price: Float!
}

type SGCredsResponse {
    status: String

    msg: String

    data: Creds
}

type SalesSummaryStatsData {
    terminalData: String!
    terminalId: String!
}

type SalesSummaryDataResponse {
    msg: String
    period: String
    reportType: String
    stats: [SalesSummaryStatsData]
    status: Boolean!
    store: String
    storeCode: String!
    preset: String
}

type HistoricalShiftReport {
    shiftId: String!
    terminal: String!
    cashierId: String!
    shiftStart: String!
    shiftStartCash: Float!
    shiftEnd: String!
    shiftEndCash: Float!
}

type HistoricalBatchReport {
    batchEnd: String!
    batchEndCashier: String
    batchId: String!
    batchStart: String!
    batchStartCashier: String
    terminal: String!
}

type HistoricalShiftReportsResponse {
    data: [HistoricalShiftReport]
    terminals: [String]
    msg: String
    status: Boolean
}

type HistoricalBatchReportsResponse {
    status: Boolean
    msg: String
    terminals: [String]
    data: [HistoricalBatchReport]
}

type LoyaltyGetRewardsResponse {
    status: Boolean
    msg: String
    data: LoyaltyRewards
}

type LoyaltyRewards {
    rewards: [LoyaltyReward]
}

type LoyaltyReward {
    transactionItemId: String
    loyaltyAccountNumber: String
    originalQuantity: Int
    discountQuantity: Int
    discount: LoyaltyDiscount
}

type LoyaltyDiscount {
    discountId: String
    discountText: String
    discountAmount: Float
}

"""
The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.
"""
scalar String

type Creds {
    uid: String

    pwd: String
}

type TxnResponse {
    status: String

    msg: String

    refundInfo: JSONString

    paymentInfo: JSONString
}

input RIM {
    attempt: Int = 1
    ebtAmount: Int = 0
    fee: Int = 0
    genAmount: Int = 0
    token: String!
    txnId: String
}

"""
Allows use of a JSON String for input / output from the GraphQL schema.

Use of this type is *not recommended* as you lose the benefits of having a defined, static
schema (one of the key benefits of GraphQL).
"""
scalar JSONString

interface RelayNode {
    """
    The ID of the object
    """
    id: ID!
}

"""
The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.
"""
scalar ID

type Mutation {

    refundProcessor(eventEpoch: Int!, tdata: RIM!, uid: String!): RefundProcessor

    genSampleTxnId(posid: String, scode: String): GenSampleTxnId

    initCbsSync(docids: [String]!, scopeCollection: String!): InitCbsSync


    securedPaymentProcessor(
        eventEpoch: Int!,
        tdata: TIM!,
        uid: String!,
        processorType: String
    ): SecuredPaymentProcessor

    securedPaymentRefunder(
        eventEpoch: Int!
        tdata: RIM!
        uid: String!
    ): SecuredPaymentRefunder


}

type TerminalCfgResponse {
    data: Config
    msg: String
    status: String
}

type Config {
    sgCreds: SGCreds
}

type FuelConfig {
    boardIp: String
    commPriority: String
    enabled: Boolean
    fdcIp: String
}

type FeeConfig {
    applicationType: String!
    type: String!
    enabled: Boolean!
    feeFixed: Int
    feePercentage: Float
}

type SGCreds {
    pwd: String
    uid: String
}

type SecuredPaymentProcessor {
    responseMessage: PaymentResponse
}

type SecuredPaymentRefunder{
    responseMessage: PaymentResponse
}

type RefundProcessor {
    responseMessage: RefundResponse
}

type RefundResponse {
    ebt: RefundInfo
    err: Boolean
    eventid: String
    gen: RefundInfo
    note: String
    txnId: String
}

type RefundInfo {
    cardInfo: CardInfo
    ecode: String
    msg: String
    ok: Boolean
    payfacId: String
    prt: Float
}

type CardInfo {
    applicationLabel: String
    approvalCode: String
    bal: Int
    brand: String
    entryMode: String
    expiry: String
    holderName: String
    number: String
    pinVerified: String
    type: String
}

type InitCbsSync {
    responseMessage: CbsSyncResponse
}

type CbsSyncResponse {
    data: [CbsSyncDocResponse]
    msg: String
}

type CbsSyncDocResponse {
    id: String
    ok: Boolean
    rev: String
}

type ProcessRefund {
    responseMessage: PaymentResponse
}

type PaymentResponse {
    eventid: String

    status: Boolean

    msg: String

    details: String

    data: TxnInsights

    payfacInfo: PaymentInfo
}

"""
The `Boolean` scalar type represents `true` or `false`.
"""
scalar Boolean

type TxnInsights {
    amount: Int
    gatewayProcessTime: Float
    mid: String
    txnId: String
    txnTime: Float
}

"""
The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.
"""
scalar Int

"""
The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).
"""
scalar Float

type PaymentInfo {
    applicationLabel: String
    approvalCode: String
    cardBrand: String
    cardEntryMode: String
    cardHolderName: String
    cardNumber: String
    errCode: String
    errMsg: String
    failureCode: String
    failureMessage: String
    ok: Boolean
    paymentType: String
    pinVerified: String
    infoMsg: String
}

type ProcessPayment {
    responseMessage: PaymentResponse
}

"""
Txn Input Model
"""
input TIM {
    amount: Int
    txnId: String
    token: String
    txnType: String
    attempt: Int = 1
}

type GenSampleTxnId {
    responseMessage: SampeTxnIdResponse
}

type SampeTxnIdResponse {
    status: String

    msg: String

    data: String
}

"""
A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.
"""
type __Schema {
    description: String

    """
    A list of all types supported by this server.
    """
    types: [__Type!]!

    """
    The type that query operations will be rooted at.
    """
    queryType: __Type!

    """
    If this server supports mutation, the type that mutation operations will be rooted at.
    """
    mutationType: __Type

    """
    If this server support subscription, the type that subscription operations will be rooted at.
    """
    subscriptionType: __Type

    """
    A list of all directives supported by this server.
    """
    directives: [__Directive!]!
}

"""
The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.

Depending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.
"""
type __Type {
    kind: __TypeKind!

    name: String

    description: String

    specifiedByURL: String

    fields(includeDeprecated: Boolean = false): [__Field!]

    interfaces: [__Type!]

    possibleTypes: [__Type!]

    enumValues(includeDeprecated: Boolean = false): [__EnumValue!]

    inputFields(includeDeprecated: Boolean = false): [__InputValue!]

    ofType: __Type
}

"""
An enum describing what kind of type a given `__Type` is.
"""
enum __TypeKind {
    """
    Indicates this type is a scalar.
    """
    SCALAR

    """
    Indicates this type is an object. `fields` and `interfaces` are valid fields.
    """
    OBJECT

    """
    Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.
    """
    INTERFACE

    """
    Indicates this type is a union. `possibleTypes` is a valid field.
    """
    UNION

    """
    Indicates this type is an enum. `enumValues` is a valid field.
    """
    ENUM

    """
    Indicates this type is an input object. `inputFields` is a valid field.
    """
    INPUT_OBJECT

    """
    Indicates this type is a list. `ofType` is a valid field.
    """
    LIST

    """
    Indicates this type is a non-null. `ofType` is a valid field.
    """
    NON_NULL
}

"""
Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.
"""
type __Field {
    name: String!

    description: String

    args(includeDeprecated: Boolean = false): [__InputValue!]!

    type: __Type!

    isDeprecated: Boolean!

    deprecationReason: String
}

"""
Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.
"""
type __InputValue {
    name: String!

    description: String

    type: __Type!

    """
    A GraphQL-formatted string representing the default value for this input value.
    """
    defaultValue: String

    isDeprecated: Boolean!

    deprecationReason: String
}

"""
One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.
"""
type __EnumValue {
    name: String!

    description: String

    isDeprecated: Boolean!

    deprecationReason: String
}

"""
A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.

In some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.
"""
type __Directive {
    name: String!

    description: String

    isRepeatable: Boolean!

    locations: [__DirectiveLocation!]!

    args(includeDeprecated: Boolean = false): [__InputValue!]!
}

"""
A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.
"""
enum __DirectiveLocation {
    """
    Location adjacent to a query operation.
    """
    QUERY

    """
    Location adjacent to a mutation operation.
    """
    MUTATION

    """
    Location adjacent to a subscription operation.
    """
    SUBSCRIPTION

    """
    Location adjacent to a field.
    """
    FIELD

    """
    Location adjacent to a fragment definition.
    """
    FRAGMENT_DEFINITION

    """
    Location adjacent to a fragment spread.
    """
    FRAGMENT_SPREAD

    """
    Location adjacent to an inline fragment.
    """
    INLINE_FRAGMENT

    """
    Location adjacent to a variable definition.
    """
    VARIABLE_DEFINITION

    """
    Location adjacent to a schema definition.
    """
    SCHEMA

    """
    Location adjacent to a scalar definition.
    """
    SCALAR

    """
    Location adjacent to an object type definition.
    """
    OBJECT

    """
    Location adjacent to a field definition.
    """
    FIELD_DEFINITION

    """
    Location adjacent to an argument definition.
    """
    ARGUMENT_DEFINITION

    """
    Location adjacent to an interface definition.
    """
    INTERFACE

    """
    Location adjacent to a union definition.
    """
    UNION

    """
    Location adjacent to an enum definition.
    """
    ENUM

    """
    Location adjacent to an enum value definition.
    """
    ENUM_VALUE

    """
    Location adjacent to an input object type definition.
    """
    INPUT_OBJECT

    """
    Location adjacent to an input object field definition.
    """
    INPUT_FIELD_DEFINITION
}

"""
Directs the executor to include this field or fragment only when the `if` argument is true.
"""
directive @include ("Included when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Directs the executor to skip this field or fragment when the `if` argument is true.
"""
directive @skip ("Skipped when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Marks an element of a GraphQL schema as no longer supported.
"""
directive @deprecated ("Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https:\/\/commonmark.org\/)." reason: String = "No longer supported") on FIELD_DEFINITION|ARGUMENT_DEFINITION|INPUT_FIELD_DEFINITION|ENUM_VALUE

"""
Exposes a URL that specifies the behaviour of this scalar.
"""
directive @specifiedBy ("The URL that specifies the behaviour of this scalar." url: String!) on SCALAR

schema {
    query: MasterQuery
    mutation: Mutation
}
