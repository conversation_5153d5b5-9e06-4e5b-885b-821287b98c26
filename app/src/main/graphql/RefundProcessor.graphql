mutation refundProcessor(
    $eventEpoch: Int!
    $tdata: RIM!
    $uid: String!
) {
    refundProcessor(
        eventEpoch: $eventEpoch
        tdata: $tdata
        uid: $uid
    ) {
        responseMessage {
            err
            note
            eventid
            ebt {
                ok
                msg
                payfacId
                ecode
                prt
                cardInfo {
                    bal
                    type
                    brand
                    expiry
                    number
                    entryMode
                    holderName
                    pinVerified
                    approvalCode
                    applicationLabel
                }
            }
            gen {
                ok
                msg
                payfacId
                ecode
                prt
                cardInfo {
                    bal
                    type
                    brand
                    expiry
                    number
                    entryMode
                    holderName
                    pinVerified
                    approvalCode
                    applicationLabel
                }
            }
        }
    }
}
