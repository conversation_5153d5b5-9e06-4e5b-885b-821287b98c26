query SalesSummaryReport(
    $year: Int
    $fmt: String
    $terminal: String
    $storeCode: String!
    $endStamp: String
    $startStamp: String
    $drawerAmount: Float
    $preset: String
    $shiftId: String
    $batchId: String
) {
    salesSummaryData(
        year: $year
        fmt: $fmt
        terminal: $terminal
        storeCode: $storeCode
        endStamp: $endStamp
        startStamp: $startStamp
        drawerAmount: $drawerAmount
        preset: $preset
        shiftId: $shiftId
        batchId: $batchId
    ) {
        status
        msg
        storeCode
        stats {
            terminalId
            terminalData
        }
    }
}
