query HistoricalShiftReports(
    $storeCode: String!
    $startDate: String
    $endDate: String
    $terminalId: String
    $cashierId: String
) {
	historicalShifts(
        storeCode: $storeCode
        startDate: $startDate
        endDate: $endDate
        terminalId: $terminalId
        cashierId: $cashierId
	) {
		status
		msg
		data {
			shiftId
			terminal
			cashierId
			shiftStart
			shiftEnd
		}
		terminals
	}
}
