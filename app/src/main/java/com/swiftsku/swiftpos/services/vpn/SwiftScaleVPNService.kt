package com.swiftsku.swiftpos.services.vpn

import android.content.Context
import android.content.Intent
import com.swiftsku.swiftpos.extension.TAIL_SCALE_APP_PACKAGE
import com.swiftsku.swiftpos.extension.TAIL_SCALE_RECEIVER
import com.swiftsku.swiftpos.extension.TAIL_SCALE_VPN_START
import com.swiftsku.swiftpos.extension.TAIL_SCALE_VPN_STOP
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import io.sentry.Sentry
import javax.inject.Inject

class SwiftScaleVPNService @Inject constructor(
    @ApplicationContext private val context: Context
) :
    VPNService {
    override fun connect() {
        try {
            context.sendBroadcast(Intent(TAIL_SCALE_VPN_START).apply {
                setClassName(
                    TAIL_SCALE_APP_PACKAGE,
                    TAIL_SCALE_RECEIVER
                )
            })
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    override fun disconnect() {
        try {
            context.sendBroadcast(Intent(TAIL_SCALE_VPN_STOP).apply {
                setClassName(
                    TAIL_SCALE_APP_PACKAGE,
                    TAIL_SCALE_RECEIVER
                )
            })
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }
}