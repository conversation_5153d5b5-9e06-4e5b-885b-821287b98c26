package com.swiftsku.swiftpos.services.replicator


import com.couchbase.lite.BasicAuthenticator
import com.couchbase.lite.Collection
import com.couchbase.lite.ListenerToken
import com.couchbase.lite.Replicator
import com.couchbase.lite.ReplicatorActivityLevel
import com.couchbase.lite.ReplicatorChangeListener
import com.couchbase.lite.ReplicatorConfiguration
import com.couchbase.lite.newConfig
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.TransactionCollection
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.PENDING_TXN_QUERY_DELAY
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CouchbaseReplicatorService @Inject constructor(
    private val replicatorConfiguration: ReplicatorConfiguration,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    @TransactionCollection private val transactionCollection: Collection
) : ReplicatorChangeListener {

    private val _replicator = MutableStateFlow<Replicator?>(null)
    val replicator = _replicator
    private val _replicatorStatus = MutableStateFlow<ReplicatorStatus>(ReplicatorStatus.Unknown)
    val replicatorStatus = _replicatorStatus
    private val _replicatorChange = MutableStateFlow(ReplicatorActivityLevel.OFFLINE)
    val replicatorChange = _replicatorChange
    private val _unSyncedTxns = MutableStateFlow(0)
    val unSyncedTxns = _unSyncedTxns
    private val _message = MutableStateFlow<ToastMessage?>(null)
    val message = _message
    private var _replicatorChangeListenerToken: ListenerToken? = null
    private val initialised: AtomicBoolean = AtomicBoolean(false)
    private var pendingTxnUpdateJob: Job? = null

    suspend fun initReplicator(sgCredentials: SgCreds) = withContext(dispatcher) {
        if (initialised.get()) {
            return@withContext
        }
        initialised.set(true)
        val replicator = Replicator(
            replicatorConfiguration.newConfig(
                authenticator = BasicAuthenticator(
                    sgCredentials.uid,
                    sgCredentials.pwd.toCharArray()
                )
            )
        )

        _replicator.emit(replicator)
        _replicatorStatus.emit(ReplicatorStatus.Stopped)

        pendingTxnUpdateJob = CoroutineScope(dispatcher).launch {
            while (isActive && initialised.get()) {
                val pendingTxn = replicator.getPendingDocumentIds(transactionCollection).size
                val posSerialNo = DeviceIdentifier.getSerialNumber().orEmpty()
                if (pendingTxn > 0) {
                    Sentry.setTag("${posSerialNo}_pendingTxn", "$pendingTxn")
                    EventUtils.logEvent(
                        "${posSerialNo}_pendingTxn",
                        mapOf(EventUtils.EventProp.PENDING_TXN to pendingTxn)
                    )
                }
                _unSyncedTxns.emit(pendingTxn)
                delay(PENDING_TXN_QUERY_DELAY)
            }
        }

        _replicatorChangeListenerToken?.remove()
        _replicatorChangeListenerToken =
            replicator.addChangeListener(this@CouchbaseReplicatorService)
    }

    suspend fun start() = withContext(dispatcher) {
        val replicator = _replicator.value
        if (replicator == null) {
            _message.emit(ToastMessage("Replicator not initialized"))
            return@withContext
        }

        if (_replicatorStatus.value.isConnected()) {
            _message.emit(ToastMessage("Replicator already started"))
            return@withContext
        }

        try {
            replicator.start()
            _replicatorStatus.emit(ReplicatorStatus.Started)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    suspend fun stop(clearReplicator: Boolean = false) = withContext(dispatcher) {
        val replicator = _replicator.value
        if (replicator == null) {
            _message.emit(ToastMessage("Replicator not initialized"))
            return@withContext
        }

        if (_replicatorStatus.value is ReplicatorStatus.Stopped) {
            _message.emit(ToastMessage("Replicator already stopped"))
            return@withContext
        }

        try {
            pendingTxnUpdateJob?.cancel()
            replicator.stop()
            if (clearReplicator) {
                _replicator.emit(null)
                _replicatorChange.emit(ReplicatorActivityLevel.OFFLINE)
            }
            _replicatorStatus.emit(if (clearReplicator) ReplicatorStatus.Unknown else ReplicatorStatus.Stopped)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    suspend fun reset() = withContext(dispatcher) {
        _replicatorChangeListenerToken?.remove()
        _replicatorChangeListenerToken?.close()
        _replicatorChangeListenerToken = null
        _replicatorStatus.emit(ReplicatorStatus.Unknown)
        initialised.set(false)
        stop(true)
    }

    override fun changed(change: com.couchbase.lite.ReplicatorChange) {
        CoroutineScope(dispatcher).launch {
            _replicatorChange.tryEmit(change.status.activityLevel)
        }
    }
}