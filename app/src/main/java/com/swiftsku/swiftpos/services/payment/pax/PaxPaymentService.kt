package com.swiftsku.swiftpos.services.payment.pax

import android.content.Context
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.orhanobut.logger.Logger
import com.pax.poscore.LogSetting
import com.pax.poscore.commsetting.CommunicationSetting
import com.pax.poslinkadmin.ExecutionResult
import com.pax.poslinkadmin.form.InputTextRequest
import com.pax.poslinkadmin.form.InputTextResponse
import com.pax.poslinkadmin.manage.InitResponse
import com.pax.poslinkadmin.manage.UpdateResourceFileRequest
import com.pax.poslinkadmin.manage.UpdateResourceFileResponse
import com.pax.poslinksemiintegration.POSLinkSemi
import com.pax.poslinksemiintegration.Terminal
import com.pax.poslinksemiintegration.batch.BatchCloseRequest
import com.pax.poslinksemiintegration.batch.BatchCloseResponse
import com.pax.poslinksemiintegration.transaction.DoCreditRequest
import com.pax.poslinksemiintegration.transaction.DoCreditResponse
import com.pax.poslinksemiintegration.transaction.DoEbtRequest
import com.pax.poslinksemiintegration.transaction.DoEbtResponse
import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.data.model.PinpadConfig
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import com.swiftsku.swiftpos.services.workers.BatchCloseWorker
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject


class PaxPaymentService @Inject constructor(
    @ApplicationContext private val context: Context,
    @IODispatcher private val ioDispatcher: CoroutineDispatcher,
    @QualifierCouchbaseConfigRepository private val storeRepository: StoreRepository
) {
    companion object {
        const val DEFAULT_PORT = "10009"
        const val DEFAULT_TIMEOUT = 60000

        fun startPaxLogging() {
            val logSetting = LogSetting().apply {
                isEnable = true
                level = LogSetting.LogLevel.DEBUG
                days = 30
                fileName = "pos_pax_logs"
            }
            POSLinkSemi.getInstance().logSetting = logSetting
        }

        fun scheduleBatchClose(
            context: Context,
            pinpadConfig: PinpadConfig,
            storeCode: String,
            posNo: String,
            posId: String,
            cashierId: String
        ) {
            val timeParts = pinpadConfig.batchCloseTime?.split(":")?.map { it.toInt() }
            if (timeParts == null || timeParts.size < 2) {
                return
            }
            val hour = timeParts[0]
            val minute = timeParts[1]

            val now = LocalDateTime.now()
            val nextRunTime = now.withHour(hour).withMinute(minute).withSecond(0)
                .let { if (it.isBefore(now)) it.plusDays(1) else it }

            val delay = Duration.between(now, nextRunTime).toMillis()

            val inputData = BatchCloseWorker.getWorkData(
                pinpadConfig.ctype,
                pinpadConfig.ip,
                pinpadConfig.port,
                storeCode,
                posNo,
                posId,
                cashierId
            )

            val workRequest =
                PeriodicWorkRequestBuilder<BatchCloseWorker>(1, TimeUnit.DAYS).setInputData(
                    inputData
                ).setInitialDelay(delay, TimeUnit.MILLISECONDS).build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                BatchCloseWorker.WORKER_ID, ExistingPeriodicWorkPolicy.UPDATE, workRequest
            )
        }
    }

    var terminal: Terminal? = null
    var initResponse: InitResponse? = null
    private var currentBatchId: String = ""
    private var commSettings: CommunicationSetting? = null

    /**
     * If there is no current batch ID, create a new one and assign it to a local variable.
     * If there is an existing batch ID, assign it to a local variable.
     */
    suspend fun initCurrentBatch(storeCode: String, posNo: String, posId: String) {
        val existingBatchId = storeRepository.getCurrentBatchId(posId)
        existingBatchId?.let {
            currentBatchId = existingBatchId
        } ?: run {
            // Create a new batchId
            getNextBatchId(storeCode, posNo, posId, null)
        }
    }

    suspend fun getNextBatchId(
        storeCode: String, posNo: String, posId: String, lastBatchId: String?
    ): String {
        val newBatchId = BatchIdUtils.generateBatchId("$storeCode:$posNo", lastBatchId)
        currentBatchId = storeRepository.updateCurrentBatchId(posId, newBatchId)
        return currentBatchId
    }

    fun getCurrentBatchId() = currentBatchId

    fun connectToDevice(commSettings: CommunicationSetting): Flow<PaxConnectionResult> {
        this.commSettings = commSettings
        return flow {
            terminal = POSLinkSemi.getInstance().getTerminal(context, commSettings)
            terminal?.let {
                val initResult = it.manage.init()
                if (initResult.isSuccessful) {
                    initResponse = initResult.response()
                    emit(PaxConnectionResult.ConnectionSuccess(initResult.response()))
                } else {
                    emit(
                        PaxConnectionResult.ConnectionError(
                            "Couldn't initialize PAX terminal. Error: ${initResult.message()}"
                        )
                    )
                }
            } ?: run {
                emit(PaxConnectionResult.ConnectionError("Couldn't connect to PAX terminal!"))
            }
        }.flowOn(ioDispatcher).catch {
            it.printStackTrace()
            Logger.d("ERROR : ${it.message}")
            emit(
                PaxConnectionResult.ConnectionError(
                    it.message ?: "Failed to connect to PAX terminal"
                )
            )
        }
    }

    private suspend fun connectToDeviceAndWait(): Boolean {
        val settings = commSettings
        if (settings == null) {
            EventUtils.recordException(Exception("EPX communication settings are not set."))
            return false
        }

        val result = connectToDevice(settings).firstOrNull()
        return when (result) {
            is PaxConnectionResult.ConnectionSuccess -> true
            else -> false
        }
    }

    suspend fun sendCreditRequest(request: DoCreditRequest): Result<DoCreditResponse?> =
        withContext(ioDispatcher) {
            var executionResult = terminal?.transaction?.doCredit(request)
            if (executionResult == null) {
                val reconnected = connectToDeviceAndWait()
                if (reconnected) {
                    executionResult = terminal?.transaction?.doCredit(request)
                } else {
                    return@withContext Result.Error(
                        errorMessage = "Couldn't connect to device.",
                        errorCode = "RECONNECT_FAILED"
                    )
                }
            }

            val response = executionResult?.response()

            if (executionResult?.isSuccessful == true && response != null) {
                return@withContext Result.Success(response)
            } else {
                return@withContext Result.Error(
                    errorMessage = executionResult?.message().toString(),
                    errorCode = executionResult?.code().toString()
                )
            }
        }

    suspend fun sendEbtRequest(request: DoEbtRequest): Result<DoEbtResponse?> =
        withContext(ioDispatcher) {
            var executionResult = terminal?.transaction?.doEbt(request)
            if (executionResult == null) {
                val reconnected = connectToDeviceAndWait()
                if (reconnected) {
                    executionResult = terminal?.transaction?.doEbt(request)
                } else {
                    return@withContext Result.Error(
                        errorMessage = "Couldn't connect to device.",
                        errorCode = "RECONNECT_FAILED"
                    )
                }
            }

            val response = executionResult?.response()

            if (executionResult?.isSuccessful == true && response != null) {
                return@withContext Result.Success(response)
            } else {
                return@withContext Result.Error(
                    errorMessage = executionResult?.message().toString(),
                    errorCode = executionResult?.code().toString()
                )
            }
        }

    suspend fun sendEpxBatchClose(request: BatchCloseRequest): Result<BatchCloseResponse?> =
        withContext(ioDispatcher) {
            val executionResult: ExecutionResult<BatchCloseResponse>? =
                terminal?.batch?.batchClose(request)
            val response = executionResult?.response()

            if (executionResult?.isSuccessful == true && response != null) {
                return@withContext Result.Success(response)
            } else {
                return@withContext Result.Error(
                    errorMessage = executionResult?.message().toString(),
                    errorCode = executionResult?.code().toString()
                )
            }
        }

    suspend fun requestTextInput(request: InputTextRequest): Result<InputTextResponse?> =
        withContext(ioDispatcher) {
            val executionResult = terminal?.form?.inputText(request)
            val response = executionResult?.response()

            if (executionResult?.isSuccessful == true && response != null) {
                return@withContext Result.Success(response)
            } else {
                return@withContext Result.Error(
                    errorMessage = executionResult?.message().toString(),
                    errorCode = executionResult?.code().toString()
                )
            }
        }

    suspend fun setIdleImage(request: UpdateResourceFileRequest): Result<UpdateResourceFileResponse?> =
        withContext(ioDispatcher) {
            val executionResult = terminal?.manage?.updateResourceFile(request)
            val response = executionResult?.response()

            if (executionResult?.isSuccessful == true && response != null) {
                return@withContext Result.Success(response)
            } else {
                return@withContext Result.Error(
                    errorMessage = executionResult?.message().toString(),
                    errorCode = executionResult?.code().toString()
                )
            }
        }
}