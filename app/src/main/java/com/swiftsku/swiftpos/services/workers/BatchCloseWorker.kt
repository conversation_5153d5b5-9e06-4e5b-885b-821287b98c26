package com.swiftsku.swiftpos.services.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.pax.poscore.commsetting.TcpSetting
import com.pax.poscore.commsetting.UsbSetting
import com.swiftsku.swiftpos.data.model.PinPadConnectionType
import com.swiftsku.swiftpos.data.model.toPinPadConnectionType
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.DayCloseInputDto
import com.swiftsku.swiftpos.services.payment.pax.PaxConnectionResult
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject


@HiltWorker
class BatchCloseWorker @AssistedInject constructor(
    @Assisted val context: Context,
    @Assisted workerParams: WorkerParameters,
    private val paxPaymentService: PaxPaymentService,
    private val paymentUseCase: PaymentUseCase
) : CoroutineWorker(context, workerParams) {

    companion object {
        const val WORKER_ID = "epx_batch_closer"
        const val C_TYPE = "ctype"
        const val IP = "ip"
        const val PORT = "port"
        const val STORE_CODE = "store_code"
        const val POS_NO = "pos_no"
        const val POS_ID = "pos_id"
        const val CASHIER_ID = "cashier_id"

        fun getWorkData(
            cType: PinPadConnectionType,
            ip: String,
            port: String?,
            storeCode: String,
            posNo: String,
            posId: String,
            cashierId: String
        ) = workDataOf(
            C_TYPE to cType.name,
            IP to ip,
            PORT to (port ?: PaxPaymentService.DEFAULT_PORT),
            STORE_CODE to storeCode,
            POS_NO to posNo,
            POS_ID to posId,
            CASHIER_ID to cashierId
        )
    }

    override suspend fun doWork(): Result {
        val connectionTypeStr = inputData.getString(C_TYPE) ?: return Result.failure()
        val connectionType = connectionTypeStr.toPinPadConnectionType()
        val ip = inputData.getString(IP) ?: return Result.failure()
        val port = inputData.getString(PORT) ?: return Result.failure()
        val storeCode = inputData.getString(STORE_CODE) ?: return Result.failure()
        val posNo = inputData.getString(POS_NO) ?: return Result.failure()
        val posId = inputData.getString(POS_ID) ?: return Result.failure()
        val cashierId = inputData.getString(CASHIER_ID) ?: return Result.failure()
        triggerBatchClose(connectionType, ip, port, storeCode, posNo, posId, cashierId)
        return Result.success()
    }

    private suspend fun triggerBatchClose(
        cType: PinPadConnectionType,
        ip: String,
        port: String,
        storeCode: String,
        posNo: String,
        posId: String,
        cashierId: String
    ) {
        val commSetting = when (cType) {
            PinPadConnectionType.USB -> UsbSetting()
            PinPadConnectionType.TCP -> TcpSetting(ip, port, PaxPaymentService.DEFAULT_TIMEOUT)
        }
        paxPaymentService.connectToDevice(commSetting).collect {
            if (it is PaxConnectionResult.ConnectionSuccess) {
                val input = DayCloseInputDto(
                    storeCode = storeCode,
                    posNo = posNo,
                    posId = posId,
                    cashierId = cashierId
                )
                paymentUseCase.closeEpxBatch(input).collect { result ->
                    (result as? com.swiftsku.swiftpos.utils.Result.Error)?.let {
                        EventUtils.recordException(
                            Exception("Failed to close batch error: ${result.errorMessage} code: ${result.errorCode}")
                        )
                    }
                }
            }
        }
    }
}
