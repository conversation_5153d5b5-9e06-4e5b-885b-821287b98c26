package com.swiftsku.swiftpos.services.payment.pax

import java.time.LocalDate
import java.time.format.DateTimeFormatter


object BatchIdUtils {

    /**
     * Creates a new batch ID in $storeCode:$posNo:yyMMdd[0-9,a-z,A-Z] format.
     * Expecting storeCode parameter to contain posNo.
     */
    fun generateBatchId(storeCode: String, lastBatchId: String?): String {
        val currentDate = LocalDate.now()
        val dateFormatter = DateTimeFormatter.ofPattern("yyMMdd")
        val currentDateString = currentDate.format(dateFormatter)

        val characterSet = ('0'..'9') + ('a'..'z') + ('A'..'Z')

        val newChar =
            if (lastBatchId != null && lastBatchId.startsWith("$storeCode:$currentDateString")) {
                val lastChar = lastBatchId.last()
                val nextIndex = characterSet.indexOf(lastChar) + 1
                if (nextIndex < characterSet.size) characterSet[nextIndex] else characterSet[0]
            } else {
                // Start with '0' if no batch ID exists or it's a new day
                characterSet[0]
            }

        return "$storeCode:$currentDateString$newChar"
    }
}