package com.swiftsku.swiftpos.lifecycle.observer.printer

import android.graphics.Bitmap
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.swiftsku.swiftpos.di.qualifiers.BitmapRegistry
import com.swiftsku.swiftpos.di.qualifiers.LegacyPrinter
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.modules.registry.Registry
import javax.inject.Inject

class PrinterObserver @Inject constructor(
    @PrinterX private val printerXManager: ISunMiPrinter,
    @LegacyPrinter private val legacyPrinterManager: ISunMiPrinter,
    @BitmapRegistry private val bitmapRegistry: Registry<String, Bitmap>
) : DefaultLifecycleObserver {


    fun registerLifecycle(lifecycle: Lifecycle) {
        lifecycle.addObserver(this)
    }

    override fun onStart(owner: LifecycleOwner) {
        printerXManager.register()
        legacyPrinterManager.register()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        printerXManager.unregister()
        legacyPrinterManager.unregister()
        bitmapRegistry.clear()
    }

}
