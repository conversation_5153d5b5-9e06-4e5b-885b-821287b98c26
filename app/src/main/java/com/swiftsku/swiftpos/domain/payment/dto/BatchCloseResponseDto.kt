package com.swiftsku.swiftpos.domain.payment.dto

import com.pax.poslinksemiintegration.batch.BatchCloseResponse
import kotlinx.serialization.Serializable


/**
 * A serializable class that mimics DoCreditRequest class from PAX SDK.
 * This has only the relevant variables from the original class.
 */
@Serializable
data class BatchCloseResponseDto(
    val hostInformation: HostResponse? = null,
    val totalAmount: TotalAmount? = null,
    val totalCount: TotalCount? = null,
    val responseMessage: String? = null,
    val responseCode: String? = null,
    val failedTransactionNumber: String? = null,
    val failedCount: String? = null,
    val safFailedCount: String? = null,
    val safFailedTotal: String? = null,
)

@Serializable
data class TotalAmount(
    val creditAmount: String? = null,
    val debitAmount: String? = null,
    val ebtAmount: String? = null
)

@Serializable
data class TotalCount(
    val creditCount: String? = null,
    val debitCount: String? = null,
    val ebtCount: String? = null
)

fun BatchCloseResponse.toDto(): BatchCloseResponseDto {
    return BatchCloseResponseDto(
        hostInformation = HostResponse(
            hostResponseCode = this.hostInformation()?.hostResponseCode(),
            hostResponseMessage = this.hostInformation()?.hostResponseMessage(),
            authorizationCode = this.hostInformation()?.authorizationCode(),
            hostReferenceNumber = this.hostInformation()?.hostReferenceNumber(),
            traceNumber = this.hostInformation()?.traceNumber(),
            batchNumber = this.hostInformation()?.batchNumber(),
            transactionIdentifier = this.hostInformation()?.transactionIdentifier(),
            gatewayTransactionId = this.hostInformation()?.gatewayTransactionId(),
            hostDetailedMessage = this.hostInformation()?.hostDetailedMessage(),
            transactionIntegrityClass = this.hostInformation()?.transactionIntegrityClass(),
            retrievalReferenceNumber = this.hostInformation()?.retrievalReferenceNumber(),
            issuerResponseCode = this.hostInformation()?.issuerResponseCode(),
            paymentAccountReferenceId = this.hostInformation()?.paymentAccountReferenceId()
        ),
        totalAmount = TotalAmount(
            this.totalAmount().creditAmount(),
            this.totalAmount().debitAmount(),
            this.totalAmount().ebtAmount()
        ),
        totalCount = TotalCount(
            this.totalCount().creditCount(),
            this.totalCount().debitCount(),
            this.totalCount().ebtCount()
        ),
        failedTransactionNumber = this.failedTransactionNumber(),
        failedCount = this.failedTransactionNumber(),
        safFailedCount = this.safFailedCount(),
        safFailedTotal = this.safFailedTotal(),
        responseCode = this.responseCode()
    )
}