package com.swiftsku.swiftpos.domain.couchbase

import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.config.GetTerminalConfigUseCase
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import com.swiftsku.swiftpos.utils.ENABLE_EMULATOR
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetSGCredentialUseCase @Inject constructor(
    private val getTerminalConfigUseCase: GetTerminalConfigUseCase,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher
) {
    suspend operator fun invoke(): Result<SgCreds> = withContext(coroutineDispatcher) {
        if (ENABLE_EMULATOR) {
            return@withContext Result.Success(
                SgCreds(
                    BuildConfig.TEST_SG_USERNAME,
                    BuildConfig.TEST_SG_PASSWORD
                )
            )
        }

        val posId = DeviceIdentifier.getSerialNumber()
            ?: return@withContext Result.Error("Please enable the device permission to read serial number")

        return@withContext getTerminalConfigUseCase
            .get(posId)
            ?.sgCreds
            ?.let { Result.Success(it) }
            ?: Result.Error("Failed to fetch SG credentials")
    }

}