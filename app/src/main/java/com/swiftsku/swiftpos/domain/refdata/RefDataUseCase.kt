package com.swiftsku.swiftpos.domain.refdata

import android.view.Menu
import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.PluItem
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class RefDataUseCase @Inject constructor(
    private val storeRepository: RefDataRepository
) {
    suspend fun saveReorderedDepartments(
        storeId: String,
        orderedDepartments: List<Department>,
        allDepartments: List<Department>
    ): Boolean {
        /**
         * orderedDepartments is the partial list visible to the users based on the showInDashboard flag.
         * allDepartments is the complete list that includes the reordered departments.
         */
        val departments = orderedDepartments.toMutableList()
        val removeIds = orderedDepartments.map { it.departmentId }
        departments.addAll(allDepartments.filter { it.departmentId !in removeIds })
        return storeRepository.saveReorderedDepartments(storeId, departments)
    }

    suspend fun saveReorderedMenuKeys(
        storeId: String,
        orderedMenuKeys: List<MenuKey>,
        allMenuKeys: List<MenuKey>
    ): Boolean {
        /**
         * orderedMenuKeys is the partial list visible to the users based on the active flag.
         * allMenuKeys is the complete list that includes the reordered menuKeys.
         *
         * First we extract all the IDs from orderedMenuKeys
         * Then we filter out these MenuKeys from allMenuKeys and add the remaining to menuKeys
         */
        val menuKeys = orderedMenuKeys.toMutableList()
        val removeIds = orderedMenuKeys.map { it.id }
        menuKeys.addAll(allMenuKeys.filter { it.id !in removeIds })
        return storeRepository.saveReorderedMenuKeys(storeId, menuKeys)
    }

    suspend fun saveReorderedSubMenu(
        storeId: String,
        menuKey: MenuKey,
        subMenu: List<PluItem>,
        allMenuKeys: List<MenuKey>
    ): Boolean {
        val orderedMenuKeys = allMenuKeys.map {
            if (it.id == menuKey.id) {
                it.copy(items = subMenu.map { "${it.pluId}-${it.pluModifier}" })
            } else it
        }
        return storeRepository.saveReorderedMenuKeys(storeId, orderedMenuKeys)
    }

    suspend fun addVendor(storeCode: String, vendor: String): Boolean {
        return storeRepository.addVendor(storeCode, vendor)
    }
}