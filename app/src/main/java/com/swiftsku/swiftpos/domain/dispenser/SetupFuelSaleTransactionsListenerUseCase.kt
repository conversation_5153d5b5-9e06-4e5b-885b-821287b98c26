package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.ClearFuelSaleRequestType
import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.FreeFuelPointRequestType
import com.fdc.core.types.FuelSalePOSdataType
import com.fdc.core.types.FuelSaleRequestDeviceClassType
import com.fdc.core.types.FuelSalesTrxDetailsDeviceClassType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.POSdataBaseType
import com.fdc.core.types.State
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.ClearFuelSaleRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFuelSaleTransactionsMessageUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelSaleTrx
import com.swiftsku.swiftpos.data.model.FuelSaleTrxState
import com.swiftsku.swiftpos.data.model.POSTransData
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCRequestType
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.addOngoingRequest
import com.swiftsku.swiftpos.ui.dashboard.main.state.deletedFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.hasOngoingRequest
import com.swiftsku.swiftpos.ui.dashboard.main.state.parsePosDataFromFdc
import com.swiftsku.swiftpos.ui.dashboard.main.state.removeOngoingRequest
import com.swiftsku.swiftpos.ui.dashboard.main.state.selectFpLock
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.util.Date
import javax.inject.Inject

class SetupFuelSaleTransactionsListenerUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val listenForFuelSaleTransactionsMessageUseCase: ListenForFuelSaleTransactionsMessageUseCase,
    private val clearFuelSale: ClearFuelSaleRequestUseCase,
    private val freeFP: FreeFuelPointUseCase,
    private val updateFSTransactionStatus: UpdateFuelSaleTransactionStatusUseCase
) {

    private suspend fun executeRequest(
        deviceId: Int,
        requestType: FDCRequestType,
        fdcState: MutableStateFlow<FDCState>,
        action: suspend () -> Unit
    ) = kotlinx.coroutines.coroutineScope {
        if (fdcState.value.hasOngoingRequest(deviceId, requestType)) {
            EventUtils.recordException(
                Exception("Request $requestType for device $deviceId already in progress")
            )
            return@coroutineScope
        }

        fdcState.update { it.addOngoingRequest(deviceId, requestType) }
        try {
            action()
        } catch (t: Throwable) {
            EventUtils.recordException(t)
            fdcState.update { it.removeOngoingRequest(deviceId, requestType) }
            throw t
        }
    }

    private suspend fun freePump(
        deviceId: Int,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        val pumpRequestId = generatePumpRequestId(deviceId)

        executeRequest(deviceId, FDCRequestType.UNLOCK_FUEL_POINT, stateFlow) {
            launch {
                freeFP.freeFPResponse.collectLatest { response ->
                    if (response.isForCurrentDevice(fdcConfig.workstationID, pumpRequestId)) {
                        stateFlow.update {
                            it.removeOngoingRequest(deviceId, FDCRequestType.UNLOCK_FUEL_POINT)
                        }
                        if (response.overallResult == OverallResult.SUCCESS) {
                            stateFlow.update { state ->
                                val afterDeleting = state.deletedFpLock(deviceId)
                                afterDeleting.copy(linkedTxnId = if (afterDeleting.fpLock.isEmpty()) null else afterDeleting.linkedTxnId)
                            }
                        }
                    }
                }
            }

            freeFP(
                FreeFuelPointRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = pumpRequestId
                    poSdata = POSdataBaseType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            type = Type.FP
                            deviceID = deviceId
                        }
                    }
                }
            )
        }
    }

    private suspend fun clearFuelSale(
        device: FuelSalesTrxDetailsDeviceClassType,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {

        val requestId = generatePumpRequestId(device.pumpNo)

        executeRequest(device.deviceID, FDCRequestType.CLEAR_FUEL_POINT, stateFlow) {
            launch {
                clearFuelSale.changeFuelPriceResponse.collectLatest { clear ->
                    if (clear.isForCurrentDevice(fdcConfig.workstationID, requestId)) {
                        stateFlow.update {
                            it.removeOngoingRequest(
                                device.deviceID,
                                FDCRequestType.CLEAR_FUEL_POINT
                            )
                        }
                        if (clear.overallResult == OverallResult.SUCCESS) {
                            freePump(device.deviceID, stateFlow, fdcConfig)
                        }
                    }
                }
            }
            clearFuelSale.invoke(ClearFuelSaleRequestType().apply {
                applicationSender =
                    fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = requestId
                poSdata = FuelSalePOSdataType().apply {
                    posTimeStamp =
                        LocalDateTime.now().toPOSTimeStamp()
                    deviceClass =
                        FuelSaleRequestDeviceClassType().apply {
                            deviceID = device.deviceID
                            type = device.type
                            transactionSeqNo =
                                device.transactionSeqNo
                        }
                }
            })
        }
    }

    suspend operator fun invoke(
        stateFlow: MutableStateFlow<FDCState>,
        storeConfig: StoreConfig,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        listenForFuelSaleTransactionsMessageUseCase
            .message
            .collectLatest { latestTrx ->
                stateFlow.update { state ->
                    state.copy(dispensers = state.dispensers.map { dispenserState ->
                        dispenserState.copy(fuelPump = dispenserState.fuelPump.map fpMap@{ fuelPumpState ->
                            val device = latestTrx.fdCdata.deviceClass
                            if (device.authorisationApplicationSender == fdcConfig.applicationSender
                                && device.deviceID == fuelPumpState.deviceId
                            ) {
                                val posTxnData = parsePosDataFromFdc(
                                    storeConfig.storeCode,
                                    storeConfig.posNumber,
                                    device.posTransData.posTransactionData
                                )
                                val fpLock = state.selectFpLock(
                                    pumpNo = device.pumpNo,
                                    deviceId = device.deviceID
                                )
                                if (device.state == State.PAYABLE) {
                                    val extractedPosTxnId = posTxnData?.txnId
                                    extractedPosTxnId?.let {
                                        val fuelSaleTrx = FuelSaleTrx(
                                            lockedAmount = (fpLock?.lockedAmount
                                                ?: 0.0).to2Decimal(),
                                            lockedVolume = fpLock?.lockedVolume ?: 0.0,
                                            pumpNo = device.pumpNo,
                                            deviceId = device.deviceID,
                                            posTransData = POSTransData(
                                                tranType = device?.posTransData?.tranType,
                                                posTransactionData = device?.posTransData?.posTransactionData,
                                                epsStan = device?.posTransData?.epsstan?.toDouble(),
                                                merchandiseTrxAmount = device?.posTransData?.merchandiseTrxAmount?.toDouble()
                                            ),
                                            volume = device?.volume?.toDouble(),
                                            amount = device?.amount?.toDouble()?.to2Decimal(),
                                            volume1 = device.volumeProduct1?.toDouble(),
                                            volume2 = device.volumeProduct2?.toDouble(),
                                            product1 = device.productNo1,
                                            product2 = device.productNo2,
                                            unitPrice = device?.unitPrice?.toDouble(),
                                            timestamp = Date(),
                                            lockingApplicationSender = device?.lockingApplicationSender,
                                            authorizationApplicationSender = device.authorisationApplicationSender,
                                            txnId = extractedPosTxnId,
                                            txnItemId = posTxnData.txnItemId,
                                            state = device?.state,
                                            linkedTxnId = state.linkedTxnId
                                        )
                                        launch {
                                            updateFSTransactionStatus.invoke(
                                                fuelSaleTrx,
                                                state
                                            )
                                        }
                                        launch {
                                            clearFuelSale(
                                                device,
                                                stateFlow,
                                                fdcConfig
                                            )
                                        }
                                    } ?: run {
                                        EventUtils.recordException(
                                            Exception("FuelError: extractedPosTxnId is null. FDC postData: ${device.posTransData.posTransactionData}")
                                        )
                                    }
                                }

                                return@fpMap fuelPumpState.copy(
                                    fuelSaleTrxState = FuelSaleTrxState(
                                        fuelSaleTrxDeviceClass = device,
                                    )
                                )
                            }
                            fuelPumpState
                        })
                    })
                }
            }
    }
}