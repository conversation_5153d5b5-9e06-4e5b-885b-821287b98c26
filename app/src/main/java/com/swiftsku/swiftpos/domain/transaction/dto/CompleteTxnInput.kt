package com.swiftsku.swiftpos.domain.transaction.dto

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.EbtInfo
import com.swiftsku.swiftpos.data.model.LoyaltyAccountInfo
import com.swiftsku.swiftpos.data.model.PaymentRecord
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.TxnPayment
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.ui.dashboard.main.state.PayoutState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary

data class CompleteTxnInput(
    val transaction: Transaction,
    val txnPayment: MutableMap<TxnPaymentType, TxnPayment>,
    val cardInfo: CardInfo? = null,
    val ebtInfo: EbtInfo? = null,
    val transactionSummary: TransactionSummary,
    val payout: PayoutState,
    val loyaltyId: String? = null,
    val dob: String? = null,
    val txnItems: List<TransactionItem>,
    val coupons: List<Coupon>,
    val storeConfig: StoreConfig,
    val cashierId: String,
    val hasFuel: Boolean,
    val hasPostFuel: Boolean,
    val paymentRecord: PaymentRecord? = null,
    val printTxnIdBarcode: Boolean? = false,
    val appliedFees: List<AppliedFee>
)

val CompleteTxnInput.hasPendingFuel: Boolean
    get() = hasFuel && !hasPostFuel

val CompleteTxnInput.transformedTxnItems: List<TransactionItem>
    get() = if (hasPendingFuel) txnItems.map {
        if (it is TransactionItemWithFuel) {
            return@map it.copy(paid = true)
        }
        if (it is TransactionItemWithDepartment) {
            return@map it.copy(paid = true)
        }
        if (it is TransactionItemWithPLUItem) {
            return@map it.copy(paid = true)
        }
        it
    } else txnItems

val CompleteTxnInput.txnStatus: TransactionStatus
    get() =
        if (hasPendingFuel) (if (hasPostFuel) TransactionStatus.FuelDispensed else TransactionStatus.FuelAuthorized) else TransactionStatus.Complete


val CompleteTxnInput.accountInfo
    get() = if (loyaltyId == null) null else LoyaltyAccountInfo(loyaltyId = loyaltyId)

