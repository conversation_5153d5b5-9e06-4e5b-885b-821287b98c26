package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.POSReadyMessageType
import com.fdc.core.types.POSReadyPOSdataType
import com.orhanobut.logger.Logger
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.SendPOSReadyUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

class FDCHeartBeatInitializeUseCase @Inject constructor(
    private val listenForFDCReadyUseCase: ListenForFDCReadyUseCase,
    private val sendPOSReadyUseCase: SendPOSReadyUseCase
) {
    operator fun invoke(scope: CoroutineScope, fdcConfig: FDCConfig) {
        scope.launch {
            listenForFDCReadyUseCase.fdcReady.collectLatest {
                Logger.d("FDC Ready Message $it")
                if (it.isForCurrentDevice(workstationId = fdcConfig.workstationID)) {
                    launch {
                        sendPOSReadyUseCase(POSReadyMessageType().apply {
                            poSdata = POSReadyPOSdataType().apply {
                                messageID = it.messageID
                                applicationSender = fdcConfig.applicationSender
                                workstationID = fdcConfig.workstationID
                                posTimeStamp = LocalDateTime
                                    .now()
                                    .toPOSTimeStamp()
                            }
                        })
                    }
                }


            }
        }
    }
}