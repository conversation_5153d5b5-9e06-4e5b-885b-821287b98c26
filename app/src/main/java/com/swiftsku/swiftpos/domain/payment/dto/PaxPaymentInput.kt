package com.swiftsku.swiftpos.domain.payment.dto

import com.pax.poslinkadmin.constant.TransactionType
import com.pax.poslinksemiintegration.constant.EbtCountType


data class PaxPaymentInput(
    val transactionId: String,
    val batchId: String,
    val amount: String,
    val cashierId: String,
    val epoch: Int,
    val ebtType: EbtCountType? = null,
    val txnType: TransactionType = TransactionType.SALE,
    val originalReferenceNumber: String? = null
)

fun PaxPaymentInput.toDoCreditRequestDto(): DoCreditRequestDto {
    var suffix = when (txnType) {
        TransactionType.SALE -> "-s"
        TransactionType.RETURN -> "-r"
        TransactionType.AUTHORIZATION -> "-a"
        TransactionType.POST_AUTHORIZATION -> "-p"
        TransactionType.INQUIRY -> "-i"
        else -> ""
    }
    if (ebtType != null) {
        suffix = "$suffix-e"
    }
    return DoCreditRequestDto(
        epoch = epoch,
        transactionType = txnType,
        amountInformation = AmountRequest(transactionAmount = amount),
        cashierInformation = CashierRequest(clerkId = cashierId.take(5)),
        traceInformation = TraceRequest(
            invoiceNumber = "${transactionId.substringAfter("-")}$suffix",
            timeStamp = "$epoch"
        ),
        transactionBehavior = TransactionBehavior(entryMode = EntryModeBitmap()),
        accountInformation = AccountRequest(ebtType = ebtType),
        batchId = batchId,
        originalReferenceNumber = originalReferenceNumber
    )
}