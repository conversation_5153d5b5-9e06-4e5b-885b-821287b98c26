package com.swiftsku.swiftpos.domain.transaction

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.Fee
import com.swiftsku.swiftpos.data.model.FeeApplicationType
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary

data class CardFeeCalculationOptions(
        val ignoreConfigured: Boolean = false,
        val ignoreCompletely: Boolean = false,
        val minAmountToSkip: Float? = null
)

data class SurchargeCalculationOptions(val skipNoSurchargeOptionItems: Boolean = false)

object GetFeesUseCase {

    fun getCardProcessingFee(
            cardAmount: Float,
            fees: List<Fee>?,
            transactionSummary: TransactionSummary,
            options: CardFeeCalculationOptions
    ): AppliedFee? {
        val cardFeeConfig = fees?.firstOrNull { it.type == FeeType.CARD_PROCESSING }
        cardFeeConfig?.let {
            if (it.enabled && cardAmount > 0f) {
                val feeAmount =
                        when (it.applicationType) {
                            FeeApplicationType.PERCENT ->
                                    it.feePerc?.let { cardAmount * (it / 100) }
                            FeeApplicationType.FIXED -> it.feeFixed.toDollars()
                        }
                val info =
                        when (it.applicationType) {
                            FeeApplicationType.PERCENT -> "${it.feePerc?.to2Decimal()}%"
                            FeeApplicationType.FIXED -> it.feeFixed.toDollars().toDollars()
                        }
                return feeAmount?.let {
                    AppliedFee(
                            type = FeeType.CARD_PROCESSING,
                            amount = it.to2Decimal(),
                            info = info
                    )
                }
            }
        }
        return null
    }

    fun getSurchargeFee(
            transactionItems: List<TransactionItem>,
            fees: List<Fee>?,
            options: SurchargeCalculationOptions = SurchargeCalculationOptions()
    ): AppliedFee? {
        val surchargeFeeConfig = fees?.firstOrNull { it.type == FeeType.SURCHARGE }
        surchargeFeeConfig?.let { feeConfig ->
            if (feeConfig.enabled) {
                // Filter items that are eligible for surcharge
                val eligibleItems =
                        transactionItems.filter { item ->
                            // Only consider normal status items
                            item.status == TransactionItemStatus.Normal &&
                                    // Check if item should be excluded from surcharge
                                    !shouldExcludeFromSurcharge(item, feeConfig, options)
                        }

                // Calculate total amount for surcharge consideration
                val surchargeableAmount = eligibleItems.sumOfFloats { it.totalItemPrice() }

                // Check if amount is above threshold
                val threshold = feeConfig.ignoreThreshold
                if (threshold != null && surchargeableAmount <= threshold) {
                    return null
                }

                // Calculate surcharge fee
                val feeAmount =
                        when (feeConfig.applicationType) {
                            FeeApplicationType.PERCENT ->
                                    feeConfig.feePerc?.let { surchargeableAmount * (it / 100) }
                            FeeApplicationType.FIXED -> feeConfig.feeFixed?.toDollars()
                        }

                val info =
                        when (feeConfig.applicationType) {
                            FeeApplicationType.PERCENT -> "${feeConfig.feePerc?.to2Decimal()}%"
                            FeeApplicationType.FIXED -> feeConfig.feeFixed?.toDollars()?.toDollars()
                        }

                return feeAmount?.let {
                    AppliedFee(type = FeeType.SURCHARGE, amount = it.to2Decimal(), info = info)
                }
            }
        }
        return null
    }

    private fun shouldExcludeFromSurcharge(
            item: TransactionItem,
            feeConfig: Fee,
            options: SurchargeCalculationOptions
    ): Boolean {
        return when (item) {
            is TransactionItemWithPLUItem -> {
                // Exclude if PLU has no_surcharge property
                item.pluItem.props.contains(PluItemProps.NoSurcharge) ||
                        // Exclude if PLU has no_surcharge_option and cashier chose to skip
                        (item.pluItem.props.contains(PluItemProps.NoSurchargeOption) &&
                                options.skipNoSurchargeOptionItems)
            }
            is TransactionItemWithDepartment -> {
                // Exclude if Department has no_surcharge property
                item.department.props.contains(PluItemProps.NoSurcharge) ||
                        // Exclude if Department has no_surcharge_option and cashier chose to skip
                        (item.department.props.contains(PluItemProps.NoSurchargeOption) &&
                                options.skipNoSurchargeOptionItems)
            }
            is TransactionItemWithFuel -> {
                // Exclude fuel if noFuelSurcharge is enabled in fee config
                feeConfig.noFuelSurcharge ||
                        // Exclude fuel if noFuelSurchargeOption is enabled and cashier chose to
                        // skip
                        (feeConfig.noFuelSurchargeOption && options.skipNoSurchargeOptionItems)
            }
            else -> false
        }
    }

    fun getTotalAppliedFee(appliedFees: List<AppliedFee>): Float {
        return appliedFees.sumOfFloats { it.amount }.to2Decimal()
    }
}
