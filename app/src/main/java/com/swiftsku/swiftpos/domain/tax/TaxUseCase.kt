package com.swiftsku.swiftpos.domain.tax

import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject


class TaxUseCase @Inject constructor(
    private val refDataRepository: RefDataRepository,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getTaxForAmountMapUseCase: GetTaxForAmountMapUseCase,
    private val getCachedTaxUseCase: GetCachedTaxUseCase
) {

    suspend operator fun invoke(storeCode: String): Flow<List<Tax>> = withContext(dispatcher) {
        refDataRepository.getTaxes(storeCode)
    }


}