package com.swiftsku.swiftpos.domain.departments.dto

import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookUpdate
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary


sealed class DepartmentUpdate {
    abstract val cartItems: List<TransactionItem>
    abstract val storeCode: String
    abstract val transactionSummary: TransactionSummary
    abstract val taxes: Map<String, Tax>
}

data class DepartmentAdd(
    val lineItem: TransactionItemWithDepartment,
    override val cartItems: List<TransactionItem>,
    override val storeCode: String,
    override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : DepartmentUpdate()

data class DepartmentUpdateQuantity(
    val lineItem: TransactionItemWithDepartment,
    override val cartItems: List<TransactionItem>,
    override val storeCode: String,
    val quantity: Int,
    override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : DepartmentUpdate()

data class RecalculateAmount(
    override val cartItems: List<TransactionItem>,
    override val storeCode: String,
    override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : DepartmentUpdate()