package com.swiftsku.swiftpos.domain.promotion

import com.swiftsku.swiftpos.data.couchbase.promotion.PromotionsRepository
import com.swiftsku.swiftpos.data.model.MMTDetail
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject

class GetMMTDetailsUseCase @Inject constructor(
    private val promotionsRepository: PromotionsRepository,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(startDate: Date, endDate: Date, storeId: String): List<MMTDetail> =
        withContext(dispatcher) {
            promotionsRepository.getMMTDetails(startDate, endDate, storeId)
        }
}