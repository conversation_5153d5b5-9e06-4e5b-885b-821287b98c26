package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.GetDSPConfigurationPOSdataType
import com.fdc.core.types.GetDSPConfigurationRequestType
import com.fdc.core.types.LogOnRequestPOSdataType
import com.fdc.core.types.LogOnRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.POSdataBaseWithoutDeviceClassType
import com.fdc.core.types.StartForecourtRequestType
import com.orhanobut.logger.Logger
import com.swiftsku.fdc.core.di.usecases.core.GetVersionInfoUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCExceptionUseCase
import com.swiftsku.fdc.core.di.usecases.core.LogInUseCase
import com.swiftsku.fdc.core.di.usecases.core.StartFDCUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject

class SetupFDCLogOnUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val logIn: LogInUseCase,
    private val startFDC: StartFDCUseCase,
    private val versionInfo: GetVersionInfoUseCase,
    private val getPumps: GetDashboardFuelPumpsUseCase,
    private val setUpHeartBeat: FDCHeartBeatInitializeUseCase,
    private val setUpSaleTransactions: SetupFuelSaleTransactionsListenerUseCase,
    private val listenForFDCException: ListenForFDCExceptionUseCase,
    private val setupFPStateChangeListener: SetupFPStateChangeListenerUseCase,
    private val setupChangeFuelPriceListener: SetupChangeFuelPriceListenerUseCase
) {

    suspend operator fun invoke(
        stateFlow: MutableStateFlow<FDCState>,
        viewModelScope: CoroutineScope,
        config: StoreConfig,
        fdcConfig: FDCConfig,
        cashierId: String?
    ) = withContext(dispatcher) {

        val pumpRequestId = generatePumpRequestId(9)

        launch {
            logIn.logOnResponse.collectLatest {
                if (it.isForCurrentDevice(
                        workstationId = fdcConfig.workstationID,
                        requestID = pumpRequestId
                    ) && it.overallResult == OverallResult.SUCCESS
                ) {
                    launch { initFDC(viewModelScope, stateFlow, config, fdcConfig, cashierId) }
                }
            }
        }

        launch {

            logIn(LogOnRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = LogOnRequestPOSdataType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    interfaceVersion = fdcConfig.interfaceVersion
                }
            })
        }
    }

    private suspend fun initFDC(
        viewModelScope: CoroutineScope,
        stateFlow: MutableStateFlow<FDCState>,
        storeConfig: StoreConfig,
        fdcConfig: FDCConfig,
        cashierId: String?
    ) = withContext(dispatcher) {
        val pumpRequestId = generatePumpRequestId(8)
        launch {
            startFDC.startFDCResponse.collectLatest {
                if (it.isForCurrentDevice(
                        workstationId = fdcConfig.workstationID,
                    ) && it.overallResult == OverallResult.SUCCESS
                ) {
                    viewModelScope.launch { initDSPConfiguration(stateFlow, fdcConfig) }
                    viewModelScope.launch { initFDCErrorListener() }
                    viewModelScope.launch { setupFPStateChangeListener.invoke(stateFlow) }
                    viewModelScope.launch {
                        setUpHeartBeat.invoke(
                            viewModelScope,
                            fdcConfig
                        )
                    }
                    viewModelScope.launch {
                        setUpSaleTransactions.invoke(stateFlow, storeConfig, fdcConfig)
                    }
                    viewModelScope.launch {
                        setupChangeFuelPriceListener.invoke(
                            stateFlow,
                            fdcConfig
                        )
                    }
//                    viewModelScope.launch { getVersionInfo(viewModelScope) }
                }
            }
        }
        launch {
            startFDC(StartForecourtRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = POSdataBaseWithoutDeviceClassType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                }
            })
        }
    }

    private suspend fun initFDCErrorListener() = withContext(dispatcher) {
        launch {
            listenForFDCException.fdcException.collectLatest {
                Logger.d("FDC Exception Message $it")
            }
        }
    }

//    private suspend fun getVersionInfo(viewModelScope: CoroutineScope) = withContext(dispatcher) {
//        val pumpRequestId = generatePumpRequestId(7)
//        versionInfo.invoke(scope = viewModelScope, input = VersionInfoRequestType().apply {
//            applicationSender = MOCKED_FDC_CONFIG.applicationSender
//            workstationID = MOCKED_FDC_CONFIG.workstationID
//            requestID = pumpRequestId
//            poSdata = POSdataBaseWithoutDeviceClassType().apply {
//                posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
//            }
//        }).collectLatest {
//            Logger.d("Version Info Response $it")
//        }
//    }


    private suspend fun initDSPConfiguration(
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig

    ) =
        withContext(dispatcher) {
            launch {
                getPumps.invoke(
                    GetDSPConfigurationRequestType().apply {
                        poSdata = GetDSPConfigurationPOSdataType().apply {
                            posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        }
                        applicationSender = fdcConfig.applicationSender
                        workstationID = fdcConfig.workstationID
                        requestID = generatePumpRequestId(11)
                    },
                    stateFlow,
                    fdcConfig
                )
            }
        }

}