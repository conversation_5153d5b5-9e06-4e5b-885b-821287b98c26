package com.swiftsku.swiftpos.domain.dispenser

import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.ui.dashboard.main.state.DispenserState
import com.swiftsku.swiftpos.utils.FUEL_CARD_MODE
import com.swiftsku.swiftpos.utils.FUEL_CASH_MODE
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlin.math.abs


class ValidateFuelPriceUseCase(
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {
    suspend fun priceDifferenceAndUpdateItems(
        txnItems: List<TransactionItem>,
        paymentType: TxnPaymentType,
        dispensers: List<DispenserState>
    ): Pair<Double, List<TransactionItem>> = withContext(dispatcher) {
        if (txnItems.filterIsInstance<TransactionItemWithFuel>().isEmpty()) return@withContext Pair(
            0.0,
            txnItems
        )

        val paymentFuelModeNo =
            if (paymentType == TxnPaymentType.Card) FUEL_CARD_MODE else FUEL_CASH_MODE
        var totalPriceDiff = 0.0

        val updatedItems = txnItems.map { txnItem ->
            if (txnItem !is TransactionItemWithFuel) return@map txnItem
            txnItem.postFuel?.let { postFuel ->
                val dispenser = dispensers.find {
                    it.fuelPump.find { it.deviceId == postFuel.deviceId } != null
                }
                val dspPrice = dispenser?.product?.firstOrNull {
                    it.productNo == txnItem.postFuel.productNo1
                }?.fuelPrice?.find { it.modeNo == paymentFuelModeNo }?.price?.toDouble() ?: 0.0
                val posPrice = postFuel.unitPrice ?: 0.0
                val priceDiff = abs(dspPrice.minus(posPrice))
                if (priceDiff > 0.001) {
                    val amountDiff = priceDiff * postFuel.volume
                    totalPriceDiff += amountDiff
                    return@map txnItem.copy(
                        postFuel = postFuel.copy(
                            amount = postFuel.amount.plus(amountDiff),
                            unitPrice = dspPrice
                        )
                    )
                }
                txnItem
            } ?: run {
                txnItem.preFuel.let { preFuel ->
                    val dispenser = dispensers.find {
                        it.fuelPump.find { it.deviceId == preFuel.deviceId } != null
                    }
                    val dspPrice = dispenser?.product?.firstOrNull {
                        it.productNo == txnItem.preFuel.productNo
                    }?.fuelPrice?.find { it.modeNo == paymentFuelModeNo }?.price?.toDouble() ?: 0.0
                    val posPrice = preFuel.unitPrice ?: 0.0
                    val priceDiff = abs(dspPrice.minus(posPrice))
                    if (priceDiff > 0.001) {
                        val amountDiff = priceDiff * (preFuel.volume ?: 0.0)
                        totalPriceDiff += amountDiff
                        return@map txnItem.copy(
                            preFuel = preFuel.copy(
                                amount = preFuel.amount.plus(amountDiff),
                                unitPrice = dspPrice
                            )
                        )
                    }
                }
                txnItem
            }
        }
        return@withContext Pair(totalPriceDiff, updatedItems)
    }
}