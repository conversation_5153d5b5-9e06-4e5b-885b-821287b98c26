package com.swiftsku.swiftpos.domain.payout

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.data.model.PayoutEvent
import com.swiftsku.swiftpos.data.type.PayoutType
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payout.dto.PayoutInput
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.extension.epochInSeconds
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date

class CreateLotteryPayoutUseCase(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val printTransactionUseCase: PrintTransactionUseCase,
    private val transactionRepository: TransactionRepository
) {
    suspend operator fun invoke(input: PayoutInput): Boolean = withContext(dispatcher) {
        input.lotteryPayouts.forEach {
            val txn = PayoutEvent(
                txnId = it.linkedTxnId,
                cashierId = input.cashierId,
                txnStartTime = input.txnStartTime,
                fromSale = input.fromSale,
                payoutData = Payout(it.info, PayoutType.Lottery, it.amount),
                statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.Complete)
            )
            transactionRepository.saveTransaction(txn)
            printTransactionUseCase.printIfAllowed(input.storeConfig, txn)
        }
        return@withContext true
    }
}