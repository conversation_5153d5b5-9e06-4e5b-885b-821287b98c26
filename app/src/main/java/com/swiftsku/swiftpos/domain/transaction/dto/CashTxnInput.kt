package com.swiftsku.swiftpos.domain.transaction.dto

import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TxnPayment
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.ui.dashboard.main.state.PayoutState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary

data class CashTxnInput(
    val transaction: Transaction,
    val txnPayment: MutableMap<TxnPaymentType, TxnPayment>,
    val transactionSummary: TransactionSummary,
    val payout: PayoutState,
    val loyaltyId: String? = null,
    val dob: String? = null,
    val txnItems: List<TransactionItem>,
    val coupons: List<Coupon>,
    val storeConfig: StoreConfig,
    val cashierId: String
)
