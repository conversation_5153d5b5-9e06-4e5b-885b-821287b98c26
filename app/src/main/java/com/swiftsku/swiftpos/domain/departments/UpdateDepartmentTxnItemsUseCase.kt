package com.swiftsku.swiftpos.domain.departments

import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.departments.dto.DepartmentAdd
import com.swiftsku.swiftpos.domain.departments.dto.DepartmentUpdate
import com.swiftsku.swiftpos.domain.departments.dto.DepartmentUpdateQuantity
import com.swiftsku.swiftpos.domain.departments.dto.RecalculateAmount
import com.swiftsku.swiftpos.domain.pricebook.EBTCalculationInput
import com.swiftsku.swiftpos.domain.pricebook.EBTCalculationsUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxForAmountMapUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxMap
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalEBT
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject


class UpdateDepartmentTxnItemsUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getTaxForAmountMapUseCase: GetTaxForAmountMapUseCase,
    private val ebtCalculationsUseCase: EBTCalculationsUseCase
) {

    suspend operator fun invoke(input: DepartmentUpdate): List<TransactionItem> =
        withContext(dispatcher) {
            var previouslyAdded = input.cartItems
            val transactionSummary = input.transactionSummary

            previouslyAdded = when (input) {
                is DepartmentAdd -> {
                    previouslyAdded.plus(input.lineItem)
                }

                is DepartmentUpdateQuantity -> {
                    previouslyAdded.map {
                        if (it.transactionItemId == input.lineItem.transactionItemId) {
                            if (it is TransactionItemWithDepartment) {
                                return@map it.copy(quantity = input.quantity)
                            }
                        }
                        it
                    }
                }

                is RecalculateAmount -> previouslyAdded
            }

            previouslyAdded = previouslyAdded.map {
                if (it is TransactionItemWithDepartment) {
                    var discount = 0.0
                    it.promotion?.forEach { promotion -> discount += promotion.promotionAmount }
                    it.discount.loyalty.forEach { loyalty -> discount += loyalty.amount }

                    val taxation = getTaxForAmountMapUseCase(
                        GetTaxMap(
                            existing = it.taxation,
                            taxIds = it.department.taxes,
                            storeCode = input.storeCode,
                            itemPrice = it.department.departmentPrice,
                            quantity = it.quantity,
                            discount = discount
                        )
                    )

                    val discardTaxation = it.isEBT() && transactionSummary.ebtAmountCollected > 0

                    return@map it.copy(
                        quantity = it.quantity,
                        taxation = if (discardTaxation) emptyMap() else taxation
                    )
                }
                it
            }

            if (transactionSummary.ebtAmountCollected > 0 && transactionSummary.ebtAmountCollected < transactionSummary.totalEBT()) {
                previouslyAdded = ebtCalculationsUseCase(
                    EBTCalculationInput(
                        previouslyAdded,
                        input.taxes,
                        transactionSummary.ebtAmountCollected
                    )
                )
            }

            previouslyAdded
        }
}