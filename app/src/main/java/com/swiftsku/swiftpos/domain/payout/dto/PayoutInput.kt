package com.swiftsku.swiftpos.domain.payout.dto

import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.data.model.StoreConfig
import java.util.Date

data class PayoutInput(
    val lotteryPayouts: List<LotteryPayout>,
    val fromSale: Boolean = false,
    val txnStartTime: Date,
    val cashierId: String,
    val storeConfig: StoreConfig?
)
