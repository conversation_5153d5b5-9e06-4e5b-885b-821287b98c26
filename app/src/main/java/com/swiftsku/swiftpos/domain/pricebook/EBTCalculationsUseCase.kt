package com.swiftsku.swiftpos.domain.pricebook

import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.UID
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.data.model.getTaxIds
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.sumOfFloats
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject


data class EBTCalculationInput(
    val cartItems: List<TransactionItem>,
    val taxes: Map<String, Tax>,
    val ebtAmountCollected: Float
)

fun calculateTotalTaxRate(taxIds: List<String>, taxes: Map<String, Tax>): Float {
    return taxIds
        .mapNotNull { taxId -> taxes[taxId] }
        .sumOfFloats { it.rate }
}

fun sortEbtItemsByTotalTaxRate(
    cartItems: List<TransactionItem>, taxes: Map<String, Tax>
): List<TransactionItem> {
    return cartItems.sortedByDescending { calculateTotalTaxRate(it.getTaxIds(), taxes) }
}

/**
 * EBT items are tax free, so when user purchase an EBT item through an EBT card we shouldn't levy an tax on it.
 * In case, user doesn't have enough balance to make the full EBT payment and wants to use remaining EBT
 * balance, we have to allocate that balance to the EBT eligible cart items with the highest tax rate.
 * If EBT balance is not sufficient for any cart item, we find out the remaining amount to be paid
 * for that item and levy tax on it.
 * This is step by step workings of the following calculations
 * 1. Filter out EBT items from all the cart items
 * 2. Sort these filtered items based on the applicable tax rates, highest first
 * 3. Loop through these filtered items and allocate EBT paid amount to them, calculate tax if there is unallocated amount
 * 4. Since filtered items is sub list of original cart items, loop through the original list and replace the items from the mew list
 */
class EBTCalculationsUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {
    suspend operator fun invoke(input: EBTCalculationInput): List<TransactionItem> =
        withContext(dispatcher) {
            var unallocatedAmount = input.ebtAmountCollected
            if (unallocatedAmount == 0f) {
                return@withContext input.cartItems
            }

            // First filter out EBT items only and sort them based on the tax rates
            val ebtItems = input.cartItems.filter {
                it.isEBT() && it.status != TransactionItemStatus.Deleted
            }

            var sortedEbtItems = sortEbtItemsByTotalTaxRate(ebtItems, input.taxes)
            sortedEbtItems = sortedEbtItems.map { ebtItem ->
                val itemAllocated = minOf(unallocatedAmount, ebtItem.totalItemPrice())
                unallocatedAmount -= itemAllocated
                val itemUnallocatedAmount = ebtItem.totalItemPrice() - itemAllocated
                val taxationMap = ebtItem.taxation.toMutableMap()
                if (itemUnallocatedAmount > 0) {
                    when (ebtItem) {
                        is TransactionItemWithPLUItem -> {
                            ebtItem.pluItem.taxIds.forEach { taxId ->
                                input.taxes[taxId]?.let { tax ->
                                    taxationMap[taxId] = (itemUnallocatedAmount * tax.rate / 100)
                                }
                            }
                        }

                        is TransactionItemWithDepartment -> {
                            ebtItem.department.taxes.forEach { taxId ->
                                input.taxes[taxId]?.let { tax ->
                                    taxationMap[taxId] = (itemUnallocatedAmount * tax.rate / 100)
                                }
                            }
                        }

                        is TransactionItemWithFuel -> {}
                    }
                }

                return@map when (ebtItem) {
                    is TransactionItemWithPLUItem -> ebtItem.copy(taxation = taxationMap)
                    is TransactionItemWithDepartment -> ebtItem.copy(taxation = taxationMap)
                    is TransactionItemWithFuel -> ebtItem
                }
            }

            // Replace actual cart item with calculated EBT item with taxation map
            val updatedItemsMap = sortedEbtItems.associateBy { it.transactionItemId }
            val updatedCart = input.cartItems.map { item ->
                updatedItemsMap[item.transactionItemId] ?: item
            }
            updatedCart
        }
}