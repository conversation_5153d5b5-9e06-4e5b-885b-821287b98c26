package com.swiftsku.swiftpos.domain.cart.dto

import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItem

data class DeleteCartItemResult(
    val clearCart: Boolean,
    val txnItems: List<TransactionItem>
)

data class DeleteCartItemInput(
    val txnItems: List<TransactionItem>,
    val item: TransactionItem,
    val currentTransaction: Transaction?,
)
