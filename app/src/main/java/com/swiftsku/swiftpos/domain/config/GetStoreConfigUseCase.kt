package com.swiftsku.swiftpos.domain.config

import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GetStoreConfigUseCase @Inject constructor(
    @QualifierCouchbaseConfigRepository private val storeRepository: StoreRepository,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
) {

    private var _cache: StoreConfig? = null


    suspend operator fun invoke(
        serialKey: String,
    ): StoreConfig? = withContext(dispatcher) {
        if (_cache == null) {
            _cache = storeRepository.fetchStoreConfig(serialKey)
        }


        _cache
    }

    suspend fun getFlow(
        serialKey: String,
        coroutineScope: CoroutineScope
    ): Flow<StoreConfig?> {
        val flow = storeRepository.storeConfigFlow(serialKey)
        coroutineScope.launch {
            flow.stateIn(coroutineScope)
            flow.collectLatest { _cache = it }
        }
        return flow
    }

    suspend operator fun invoke(
        serialKey: String,
        coroutineScope: CoroutineScope
    ): Flow<StoreConfig?> {
        val flow = storeRepository.storeConfigFlow(serialKey)
        coroutineScope.launch {
            flow.stateIn(coroutineScope)
            flow.collectLatest { _cache = it }
        }
        return flow
    }


}