package com.swiftsku.swiftpos.domain.payment.dto

import com.pax.poslinkadmin.constant.EntryMode
import com.pax.poslinkadmin.constant.TransactionType
import com.pax.poslinksemiintegration.constant.CardPresentIndicator
import com.pax.poslinksemiintegration.constant.CardType
import com.pax.poslinksemiintegration.constant.DebitAccountType
import com.pax.poslinksemiintegration.constant.EbtCountType
import com.pax.poslinksemiintegration.constant.GiftCardType
import com.pax.poslinksemiintegration.transaction.DoCreditResponse
import com.pax.poslinksemiintegration.transaction.DoEbtResponse
import kotlinx.serialization.Serializable


/**
 * A serializable class that mimics DoCreditRequest class from PAX SDK.
 * This has only the relevant variables from the original class.
 */
@Serializable
data class DoCreditResponseDto(
    val hostInformation: HostResponse? = null,
    val transactionType: TransactionType? = null,
    val amountInformation: AmountResponse? = null,
    val accountInformation: AccountResponse? = null,
    val traceInformation: TraceResponse? = null,
    val paymentEmvTag: PaymentEmvTag? = null,
    val batchId: String? = "",
    val responseMessage: String? = null,
    val responseCode: String? = null,
    val errorMessage: String? = null,
    val errorCode: String? = null
)

@Serializable
data class HostResponse(
    val hostResponseCode: String? = "",
    val hostResponseMessage: String? = "",
    val authorizationCode: String? = "",
    val hostReferenceNumber: String? = "",
    val traceNumber: String? = "",
    val batchNumber: String? = "",
    val transactionIdentifier: String? = "",
    val gatewayTransactionId: String? = "",
    val hostDetailedMessage: String? = "",
    val transactionIntegrityClass: String? = "",
    val retrievalReferenceNumber: String? = "",
    val issuerResponseCode: String? = "",
    val paymentAccountReferenceId: String? = "",
)

@Serializable
data class AmountResponse(
    val approvedAmount: String? = "",
    val amountDue: String? = "",
    val tipAmount: String? = "",
    val cashBackAmount: String? = "",
    val merchantFee: String? = "",
    val taxAmount: String? = "",
    val balance1: String? = "",
    val balance2: String? = "",
    val serviceFee: String? = "",
    val transactionRemainingAmount: String? = "",
    val approvedTipAmount: String? = "",
    val approvedCashBackAmount: String? = "",
    val approvedMerchantFee: String? = "",
    val approvedTaxAmount: String? = "",
)

@Serializable
data class AccountResponse(
    val account: String? = "",
    val entryMode: EntryMode? = null,
    val expireDate: String? = "",
    val ebtType: EbtCountType? = null,
    val voucherNumber: String? = "",
    val currentAccountNumber: String? = "",
    val cardType: CardType? = null,
    val cardHolder: String? = "",
    val cvdApprovalCode: String? = "",
    val cvdMessage: String? = "",
    val cardPresentIndicator: CardPresentIndicator? = null,
    val giftCardType: GiftCardType? = null,
    val debitAccountType: DebitAccountType? = null,
    val hostAccount: String? = "",
    val hostCardType: String? = "",
    val track1Data: String? = "",
    val track2Data: String? = "",
    val track3Data: String? = "",
)

@Serializable
data class TraceResponse(
    val referenceNumber: String? = "",
    val ecrReferenceNumber: String? = "",
    val timeStamp: String? = "",
    val invoiceNumber: String? = "",
    val paymentService2000: String? = "",
    val authorizationResponse: String? = "",
    val ecrTransactionId: String? = "",
    val hostTimeStamp: String? = ""
)

@Serializable
data class PaymentEmvTag(
    val appLabel: String? = ""
)


fun DoCreditResponse.toDto(batchId: String): DoCreditResponseDto {
    return DoCreditResponseDto(
        hostInformation = HostResponse(
            hostResponseCode = this.hostInformation()?.hostResponseCode(),
            hostResponseMessage = this.hostInformation()?.hostResponseMessage(),
            authorizationCode = this.hostInformation()?.authorizationCode(),
            hostReferenceNumber = this.hostInformation()?.hostReferenceNumber(),
            traceNumber = this.hostInformation()?.traceNumber(),
            batchNumber = this.hostInformation()?.batchNumber(),
            transactionIdentifier = this.hostInformation()?.transactionIdentifier(),
            gatewayTransactionId = this.hostInformation()?.gatewayTransactionId(),
            hostDetailedMessage = this.hostInformation()?.hostDetailedMessage(),
            transactionIntegrityClass = this.hostInformation()?.transactionIntegrityClass(),
            retrievalReferenceNumber = this.hostInformation()?.retrievalReferenceNumber(),
            issuerResponseCode = this.hostInformation()?.issuerResponseCode(),
            paymentAccountReferenceId = this.hostInformation()?.paymentAccountReferenceId()
        ),
        transactionType = this.transactionType(),
        amountInformation = AmountResponse(
            approvedAmount = this.amountInformation()?.approvedAmount(),
            amountDue = this.amountInformation()?.amountDue(),
            tipAmount = this.amountInformation()?.tipAmount(),
            cashBackAmount = this.amountInformation()?.cashBackAmount(),
            merchantFee = this.amountInformation()?.merchantFee(),
            taxAmount = this.amountInformation()?.taxAmount(),
            balance1 = this.amountInformation()?.balance1(),
            balance2 = this.amountInformation()?.balance2(),
            serviceFee = this.amountInformation()?.serviceFee(),
            transactionRemainingAmount = this.amountInformation()?.transactionRemainingAmount(),
            approvedTipAmount = this.amountInformation()?.approvedTipAmount(),
            approvedCashBackAmount = this.amountInformation()?.approvedCashBackAmount(),
            approvedMerchantFee = this.amountInformation()?.approvedMerchantFee(),
            approvedTaxAmount = this.amountInformation()?.approvedTaxAmount()
        ),
        accountInformation = AccountResponse(
            account = this.accountInformation()?.account(),
            entryMode = this.accountInformation()?.entryMode(),
            expireDate = this.accountInformation()?.expireDate(),
            ebtType = this.accountInformation()?.ebtType(),
            voucherNumber = this.accountInformation()?.voucherNumber(),
            currentAccountNumber = this.accountInformation()?.currentAccountNumber(),
            cardType = this.accountInformation()?.cardType(),
            cardHolder = this.accountInformation()?.cardHolder(),
            cvdApprovalCode = this.accountInformation()?.cvdApprovalCode(),
            cvdMessage = this.accountInformation()?.cvdMessage(),
            cardPresentIndicator = this.accountInformation()?.cardPresentIndicator(),
            giftCardType = this.accountInformation()?.giftCardType(),
            debitAccountType = this.accountInformation()?.debitAccountType(),
            hostAccount = this.accountInformation()?.hostAccount(),
            hostCardType = this.accountInformation()?.hostCardType(),
            track1Data = this.accountInformation()?.track1Data(),
            track2Data = this.accountInformation()?.track2Data(),
            track3Data = this.accountInformation()?.track3Data()
        ),
        traceInformation = TraceResponse(
            referenceNumber = this.traceInformation()?.referenceNumber(),
            ecrReferenceNumber = this.traceInformation()?.ecrReferenceNumber(),
            timeStamp = this.traceInformation()?.timeStamp(),
            invoiceNumber = this.traceInformation()?.invoiceNumber(),
            paymentService2000 = this.traceInformation()?.paymentService2000(),
            authorizationResponse = this.traceInformation()?.authorizationResponse(),
            ecrTransactionId = this.traceInformation()?.ecrTransactionId(),
            hostTimeStamp = this.traceInformation()?.hostTimeStamp()
        ),
        paymentEmvTag = PaymentEmvTag(
            appLabel = this.paymentEmvTag()?.appLabel()
        ),
        responseMessage = this.responseMessage(),
        responseCode = this.responseCode(),
        batchId = batchId
    )
}

fun DoEbtResponse.toDto(): DoCreditResponseDto {
    return DoCreditResponseDto(
        hostInformation = HostResponse(
            hostResponseCode = this.hostInformation()?.hostResponseCode(),
            hostResponseMessage = this.hostInformation()?.hostResponseMessage(),
            authorizationCode = this.hostInformation()?.authorizationCode(),
            hostReferenceNumber = this.hostInformation()?.hostReferenceNumber(),
            traceNumber = this.hostInformation()?.traceNumber(),
            batchNumber = this.hostInformation()?.batchNumber(),
            transactionIdentifier = this.hostInformation()?.transactionIdentifier(),
            gatewayTransactionId = this.hostInformation()?.gatewayTransactionId(),
            hostDetailedMessage = this.hostInformation()?.hostDetailedMessage(),
            transactionIntegrityClass = this.hostInformation()?.transactionIntegrityClass(),
            retrievalReferenceNumber = this.hostInformation()?.retrievalReferenceNumber(),
            issuerResponseCode = this.hostInformation()?.issuerResponseCode(),
            paymentAccountReferenceId = this.hostInformation()?.paymentAccountReferenceId()
        ),
        transactionType = this.transactionType(),
        amountInformation = AmountResponse(
            approvedAmount = this.amountInformation()?.approvedAmount(),
            amountDue = this.amountInformation()?.amountDue(),
            tipAmount = this.amountInformation()?.tipAmount(),
            cashBackAmount = this.amountInformation()?.cashBackAmount(),
            merchantFee = this.amountInformation()?.merchantFee(),
            taxAmount = this.amountInformation()?.taxAmount(),
            balance1 = this.amountInformation()?.balance1(),
            balance2 = this.amountInformation()?.balance2(),
            serviceFee = this.amountInformation()?.serviceFee(),
            transactionRemainingAmount = this.amountInformation()?.transactionRemainingAmount(),
            approvedTipAmount = this.amountInformation()?.approvedTipAmount(),
            approvedCashBackAmount = this.amountInformation()?.approvedCashBackAmount(),
            approvedMerchantFee = this.amountInformation()?.approvedMerchantFee(),
            approvedTaxAmount = this.amountInformation()?.approvedTaxAmount()
        ),
        accountInformation = AccountResponse(
            account = this.accountInformation()?.account(),
            entryMode = this.accountInformation()?.entryMode(),
            expireDate = this.accountInformation()?.expireDate(),
            ebtType = this.accountInformation()?.ebtType(),
            voucherNumber = this.accountInformation()?.voucherNumber(),
            currentAccountNumber = this.accountInformation()?.currentAccountNumber(),
            cardType = this.accountInformation()?.cardType(),
            cardHolder = this.accountInformation()?.cardHolder(),
            cvdApprovalCode = this.accountInformation()?.cvdApprovalCode(),
            cvdMessage = this.accountInformation()?.cvdMessage(),
            cardPresentIndicator = this.accountInformation()?.cardPresentIndicator(),
            giftCardType = this.accountInformation()?.giftCardType(),
            debitAccountType = this.accountInformation()?.debitAccountType(),
            hostAccount = this.accountInformation()?.hostAccount(),
            hostCardType = this.accountInformation()?.hostCardType(),
            track1Data = this.accountInformation()?.track1Data(),
            track2Data = this.accountInformation()?.track2Data(),
            track3Data = this.accountInformation()?.track3Data()
        ),
        traceInformation = TraceResponse(
            referenceNumber = this.traceInformation()?.referenceNumber(),
            ecrReferenceNumber = this.traceInformation()?.ecrReferenceNumber(),
            timeStamp = this.traceInformation()?.timeStamp(),
            invoiceNumber = this.traceInformation()?.invoiceNumber(),
            paymentService2000 = this.traceInformation()?.paymentService2000(),
            authorizationResponse = this.traceInformation()?.authorizationResponse(),
            ecrTransactionId = this.traceInformation()?.ecrTransactionId(),
            hostTimeStamp = this.traceInformation()?.hostTimeStamp()
        ),
        paymentEmvTag = PaymentEmvTag(
            appLabel = this.paymentEmvTag()?.appLabel()
        ),
        responseMessage = this.responseMessage(),
        responseCode = this.responseCode()
    )
}