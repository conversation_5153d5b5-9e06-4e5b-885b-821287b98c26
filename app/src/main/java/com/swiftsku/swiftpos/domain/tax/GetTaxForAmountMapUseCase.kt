package com.swiftsku.swiftpos.domain.tax

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.to2Decimal
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

data class GetTaxMap(
    val existing: Map<String, Float> = emptyMap(),
    val taxIds: List<String>,
    val itemPrice: Float,
    val storeCode: String,
    val quantity: Int = 1,
    val discount: Double
)

class GetTaxForAmountMapUseCase @Inject constructor(
    private val getCachedTaxUseCase: GetCachedTaxUseCase,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(
        input: GetTaxMap
    ): Map<String, Float> = withContext(dispatcher) {
        val map = input.existing.toMutableMap()
        input.taxIds.forEach { taxId ->
            getCachedTaxUseCase(
                input.storeCode, taxId
            )?.let {
                val price = ((input.itemPrice * input.quantity) - input.discount).to2Decimal()
                val amount = (price * it.rate / 100f).toFloat().to2Decimal()
                map[it.taxId] = amount
            }
        }
        map
    }
}