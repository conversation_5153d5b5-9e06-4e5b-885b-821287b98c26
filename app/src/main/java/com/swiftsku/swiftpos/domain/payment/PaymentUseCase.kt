package com.swiftsku.swiftpos.domain.payment

import com.pax.poslinkadmin.constant.TransactionType
import com.pax.poslinkadmin.util.AmountRequest
import com.pax.poslinksemiintegration.batch.BatchCloseRequest
import com.pax.poslinksemiintegration.batch.BatchCloseResponse
import com.pax.poslinksemiintegration.transaction.DoCreditRequest
import com.pax.poslinksemiintegration.transaction.DoEbtRequest
import com.pax.poslinksemiintegration.util.AccountRequest
import com.pax.poslinksemiintegration.util.CashierRequest
import com.pax.poslinksemiintegration.util.EntryModeBitmap
import com.pax.poslinksemiintegration.util.TraceRequest
import com.pax.poslinksemiintegration.util.TransactionBehavior
import com.swiftsku.swiftpos.data.couchbase.event.EventRepository
import com.swiftsku.swiftpos.data.couchbase.payment.CbPayRecordsRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.BatchCloseEventData
import com.swiftsku.swiftpos.data.model.BatchOpenEventData
import com.swiftsku.swiftpos.data.model.Event
import com.swiftsku.swiftpos.data.model.PaymentRecord
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.data.type.EventType
import com.swiftsku.swiftpos.domain.payment.dto.DayCloseInputDto
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditRequestDto
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditResponseDto
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.domain.payment.dto.toDoCreditRequestDto
import com.swiftsku.swiftpos.domain.payment.dto.toDto
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date


class PaymentUseCase(
    private val transactionRepository: TransactionRepository,
    private val paymentRepository: PaymentRepository,
    private val eventRepository: EventRepository,
    private val cbPayRecordsRepository: CbPayRecordsRepository,
    private val paxPaymentService: PaxPaymentService
) {
    fun processEpxCreditRequest(
        paxPaymentInput: PaxPaymentInput,
        transaction: SaleTransaction
    ): Flow<Result<DoCreditResponseDto?>> = flow {
        emit(Result.Loading)

        val creditRequestDto = paxPaymentInput.toDoCreditRequestDto()
        val traceRequest =
            if (!creditRequestDto.originalReferenceNumber.isNullOrBlank()) {
                TraceRequest().apply {
                    invoiceNumber = creditRequestDto.traceInformation.invoiceNumber
                    ecrReferenceNumber = creditRequestDto.traceInformation.invoiceNumber
                    timeStamp = creditRequestDto.traceInformation.timeStamp
                    originalReferenceNumber = creditRequestDto.originalReferenceNumber
                }
            } else {
                TraceRequest().apply {
                    invoiceNumber = creditRequestDto.traceInformation.invoiceNumber
                    ecrReferenceNumber = creditRequestDto.traceInformation.invoiceNumber
                    timeStamp = creditRequestDto.traceInformation.timeStamp
                }
            }
        val doCreditReq = DoCreditRequest().apply {
            transactionType = creditRequestDto.transactionType
            amountInformation = AmountRequest().apply {
                transactionAmount = creditRequestDto.amountInformation.transactionAmount
            }
            cashierInformation = CashierRequest().apply {
                clerkId = creditRequestDto.cashierInformation.clerkId
            }
            traceInformation = traceRequest
            transactionBehavior = TransactionBehavior().apply {
                entryMode = EntryModeBitmap.Builder()
                    .manual(creditRequestDto.transactionBehavior.entryMode.manual == true)
                    .chip(creditRequestDto.transactionBehavior.entryMode.chip == true)
                    .swipe(creditRequestDto.transactionBehavior.entryMode.swipe == true)
                    .scan(creditRequestDto.transactionBehavior.entryMode.scan == true)
                    .contactless(creditRequestDto.transactionBehavior.entryMode.contactless == true)
                    .checkReader(creditRequestDto.transactionBehavior.entryMode.checkReader == true)
                    .build()
            }
            accountInformation = AccountRequest().apply {
                ebtType = creditRequestDto.accountInformation.ebtType
            }
        }

        // Save this dto in payment record as payload
        savePayloadInPaymentRecord(creditRequestDto, transaction)

        when (val result = paymentRepository.sendEpxCreditRequest(doCreditReq)) {
            is Result.Success -> {
                result.data?.let {
                    val responseDto = it.toDto(creditRequestDto.batchId)
                    // Save this dto in payment record as res
                    saveResInPaymentRecord(creditRequestDto, responseDto, transaction, null)
                    if (it.responseCode() == "000000") {
                        emit(Result.Success(responseDto))
                    } else {
                        // Save error in payment record as res
                        val errorResult = Result.Error(
                            errorMessage = it.responseMessage(),
                            errorCode = it.responseCode()
                        )
                        emit(errorResult)
                    }
                } ?: run {
                    // Save error in payment record as res
                    val errorResult = Result.Error(errorMessage = "Payment Failed")
                    saveResInPaymentRecord(creditRequestDto, null, transaction, errorResult)
                    emit(errorResult)
                }
            }

            is Result.Error -> {
                // Save error in payment record as res
                saveResInPaymentRecord(creditRequestDto, null, transaction, result)
                emit(result)
            }

            Result.Loading -> emit(Result.Loading)
        }
    }

    private suspend fun savePayloadInPaymentRecord(
        creditRequestDto: DoCreditRequestDto,
        transaction: SaleTransaction
    ) {
        if (transaction.paymentRecord == null) {
            transaction.paymentRecord =
                PaymentRecord(null, null, creditRequestDto.epoch)
        }
        transaction.paymentRecord?.lastModStamp = Date().epochInSeconds()
        transactionRepository.saveTransaction(transaction)
        cbPayRecordsRepository.savePayloadInPaymentRecord(
            transaction.txnId, transaction.txnId.take(5), creditRequestDto
        )
    }

    private suspend fun saveResInPaymentRecord(
        creditRequestDto: DoCreditRequestDto,
        responseDto: DoCreditResponseDto?,
        transaction: SaleTransaction,
        errorResult: Result.Error?
    ) {
        transaction.paymentRecord?.lastModStamp = Date().epochInSeconds()
        transaction.paymentRecord?.originalReferenceNumber =
            responseDto?.traceInformation?.referenceNumber
        if (creditRequestDto.transactionType != TransactionType.RETURN) {
            transactionRepository.saveTransaction(transaction)
        }
        cbPayRecordsRepository.saveResInPaymentRecord(
            transaction.txnId, transaction.txnId.take(5),
            creditRequestDto, responseDto, errorResult
        )
    }

    fun processEpxEbtRequest(
        paxPaymentInput: PaxPaymentInput,
        transaction: SaleTransaction
    ): Flow<Result<DoCreditResponseDto?>> = flow {
        emit(Result.Loading)

        val creditRequestDto = paxPaymentInput.toDoCreditRequestDto()
        var doEbtRequest = DoEbtRequest().apply {
            transactionType = creditRequestDto.transactionType
            cashierInformation = CashierRequest().apply {
                clerkId = creditRequestDto.cashierInformation.clerkId
                shiftId = creditRequestDto.batchId
            }
            traceInformation = TraceRequest().apply {
                invoiceNumber = creditRequestDto.traceInformation.invoiceNumber
                ecrReferenceNumber = creditRequestDto.traceInformation.invoiceNumber
                timeStamp = creditRequestDto.traceInformation.timeStamp
            }
            transactionBehavior = TransactionBehavior().apply {
                entryMode = EntryModeBitmap.Builder()
                    .manual(creditRequestDto.transactionBehavior.entryMode.manual == true)
                    .chip(creditRequestDto.transactionBehavior.entryMode.chip == true)
                    .swipe(creditRequestDto.transactionBehavior.entryMode.swipe == true)
                    .scan(creditRequestDto.transactionBehavior.entryMode.scan == true)
                    .contactless(creditRequestDto.transactionBehavior.entryMode.contactless == true)
                    .checkReader(creditRequestDto.transactionBehavior.entryMode.checkReader == true)
                    .build()
            }
            accountInformation = AccountRequest().apply {
                ebtType = creditRequestDto.accountInformation.ebtType
            }
        }
        if (paxPaymentInput.txnType in listOf(TransactionType.SALE, TransactionType.RETURN)) {
            doEbtRequest = doEbtRequest.apply {
                amountInformation = AmountRequest().apply {
                    transactionAmount = creditRequestDto.amountInformation.transactionAmount
                }
            }
        }

        // Save this dto in payment record as payload
        savePayloadInPaymentRecord(creditRequestDto, transaction)

        when (val result = paymentRepository.sendEpxEbtRequest(doEbtRequest)) {
            is Result.Success -> {
                result.data?.let {
                    val responseDto = it.toDto()
                    // Save this dto in payment record as res
                    saveResInPaymentRecord(creditRequestDto, responseDto, transaction, null)
                    if (it.responseCode() == "000000") {
                        emit(Result.Success(responseDto))
                    } else {
                        // Save error in payment record as res
                        val errorResult = Result.Error(
                            errorMessage = it.responseMessage(),
                            errorCode = it.responseCode()
                        )
                        emit(errorResult)
                    }
                } ?: run {
                    // Save error in payment record as res
                    val errorResult = Result.Error(errorMessage = "Payment Failed")
                    saveResInPaymentRecord(creditRequestDto, null, transaction, errorResult)
                    emit(errorResult)
                }
            }

            is Result.Error -> {
                // Save error in payment record as res
                saveResInPaymentRecord(creditRequestDto, null, transaction, result)
                emit(result)
            }

            Result.Loading -> emit(Result.Loading)
        }
    }

    fun closeEpxBatch(input: DayCloseInputDto): Flow<Result<BatchCloseResponse?>> =
        flow {
            emit(Result.Loading)
            val batchCloseRequest = BatchCloseRequest()
            when (val result = paymentRepository.sendEpxBatchClose(batchCloseRequest)) {
                is Result.Success -> {
                    result.data?.let {
                        /*
                            1. Log BATCH_CLOSE event
                            2. Create a new batchId
                            3. Log BATCH_OPEN event
                         */
                        val storeCode = input.storeCode
                        val posNo = input.posNo
                        val posId = input.posId
                        val cashierId = input.cashierId
                        val closeTime = Date()
                        val currentBatchId = paxPaymentService.getCurrentBatchId()
                        val batchCloseEvent = Event(
                            eventId = generateTransactionId(storeCode, posNo),
                            event = EventType.BatchClose,
                            eventStartTime = closeTime,
                            eventEndTime = closeTime,
                            storeCode = storeCode,
                            cashierId = cashierId,
                            eventData = BatchCloseEventData(
                                batchId = currentBatchId,
                                res = it.toDto()
                            )
                        )
                        eventRepository.saveEvent(batchCloseEvent)
                        val newBatchId = paxPaymentService.getNextBatchId(
                            storeCode, posNo, posId, currentBatchId
                        )
                        delay(1000)
                        val openTime = Date()
                        val batchOpenEvent = Event(
                            eventId = generateTransactionId(storeCode, posNo),
                            event = EventType.BatchOpen,
                            eventStartTime = openTime,
                            eventEndTime = openTime,
                            storeCode = storeCode,
                            cashierId = cashierId,
                            eventData = BatchOpenEventData(batchId = newBatchId)
                        )
                        eventRepository.saveEvent(batchOpenEvent)
                        emit(result)
                    } ?: run {
                        val errorResult = Result.Error(errorMessage = "Failed to close batch")
                        emit(errorResult)
                    }
                }

                is Result.Error -> {
                    emit(result)
                }

                Result.Loading -> emit(Result.Loading)
            }
        }
}