package com.swiftsku.swiftpos.domain.report

import com.swiftsku.swiftpos.data.model.AdHocReport
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EOMReport
import com.swiftsku.swiftpos.data.model.EOYReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReportList
import com.swiftsku.swiftpos.data.model.HistoricalShiftReport
import com.swiftsku.swiftpos.data.model.HistoricalShiftReportList
import com.swiftsku.swiftpos.data.model.PresetReport
import com.swiftsku.swiftpos.data.model.ShiftReport


fun interface GetEODReportUseCase {
    suspend fun get(storeCode: String, date: String): EODReport?
}

fun interface GetEOMReportUseCase {
    suspend fun get(storeCode: String, date: String): EOMReport?
}

fun interface GetEOYReportUseCase {
    suspend fun get(storeCode: String, year: Int): EOYReport?
}

fun interface GetAdHocReportUseCase {
    suspend fun get(storeCode: String, startDate: String, endDate: String): AdHocReport?
}

fun interface GetShiftReportUseCase {
    suspend fun get(storeCode: String, terminal: String, drawerAmount: Float?): ShiftReport?
}

fun interface InitCbSyncUseCase {
    suspend fun init(docIds: List<String>, scopeCollection: String)
}

fun interface GetPresetReportUseCase {
    suspend fun get(storeCode: String, preset: String): PresetReport?
}

fun interface GetHistoricalShiftReportUseCase {
    suspend fun get(storeCode: String, shiftId: String): HistoricalShiftReport?
}

fun interface GetHistoricalShiftReportListUseCase {
    suspend fun get(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?,
        cashierId: String?
    ): HistoricalShiftReportList?
}

fun interface GetHistoricalBatchReportUseCase {
    suspend fun get(storeCode: String, batchId: String): HistoricalBatchReport?
}

fun interface GetHistoricalBatchReportListUseCase {
    suspend fun get(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?
    ): HistoricalBatchReportList?
}