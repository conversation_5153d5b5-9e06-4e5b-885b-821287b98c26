package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.GetFPStatePOSdataType
import com.fdc.core.types.GetFPStateRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.GetFPStateRequestUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FPState
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject

class UpdateDSPStateWithFPStateUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getFPState: GetFPStateRequestUseCase,
) {

    suspend operator fun invoke(
        state: FDCState,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {

        state.dispensers.forEach { dispenser ->
            dispenser
                .fuelPump
                .forEach { eachPump ->
                    val pumpRequestId = generatePumpRequestId(eachPump.pumpNo)
                    launch {
                        getFPState
                            .fpStateResponse
                            .collectLatest { currentFuelingStatus ->
                                if (currentFuelingStatus.isForCurrentDevice(
                                        workstationId = fdcConfig.workstationID
                                    ) && currentFuelingStatus.overallResult == OverallResult.SUCCESS
                                ) {
                                    stateFlow.update { updateState ->
                                        updateState.copy(
                                            dispensers = updateState
                                                .dispensers
                                                .map { dsp ->
                                                    return@map dsp.copy(
                                                        fuelPump = dsp
                                                            .fuelPump
                                                            .map fuelMap@{ fp ->
                                                                if (fp.deviceId == currentFuelingStatus.fdCdata.deviceClass.deviceID) {
                                                                    return@fuelMap fp.copy(
                                                                        fpState = FPState(
                                                                            fdcTimeStamp = currentFuelingStatus.fdCdata.fdcTimeStamp,
                                                                            fpState = currentFuelingStatus.fdCdata.deviceClass,
                                                                        )
                                                                    )
                                                                }
                                                                fp
                                                            }
                                                    )
                                                }
                                        )
                                    }
                                }
                            }
                    }
                    launch {
                        getFPState(GetFPStateRequestType().apply {
                            applicationSender = fdcConfig.applicationSender
                            workstationID = fdcConfig.workstationID
                            requestID = pumpRequestId
                            poSdata = GetFPStatePOSdataType().apply {
                                posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                                deviceClass = DeviceClassType().apply {
                                    type = Type.FP
                                    deviceID = eachPump.deviceId
                                }
                            }
                        })
                    }
                }
        }
    }

    suspend fun getFpState(
        deviceId: Int,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) {
        withContext(dispatcher) {
            val pumpRequestId = generatePumpRequestId(deviceId)
            launch {
                getFPState
                    .fpStateResponse
                    .collectLatest { currentFuelingStatus ->
                        if (currentFuelingStatus.isForCurrentDevice(
                                workstationId = fdcConfig.workstationID
                            ) && currentFuelingStatus.overallResult == OverallResult.SUCCESS
                        ) {
                            stateFlow.update { updateState ->
                                updateState.copy(
                                    dispensers = updateState
                                        .dispensers
                                        .map { dsp ->
                                            return@map dsp.copy(
                                                fuelPump = dsp
                                                    .fuelPump
                                                    .map fuelMap@{ fp ->
                                                        if (fp.deviceId == currentFuelingStatus.fdCdata.deviceClass.deviceID) {
                                                            return@fuelMap fp.copy(
                                                                fpState = FPState(
                                                                    fdcTimeStamp = currentFuelingStatus.fdCdata.fdcTimeStamp,
                                                                    fpState = currentFuelingStatus.fdCdata.deviceClass,
                                                                )
                                                            )
                                                        }
                                                        fp
                                                    }
                                            )
                                        }
                                )
                            }
                        }
                    }
            }
            launch {
                getFPState(GetFPStateRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = pumpRequestId
                    poSdata = GetFPStatePOSdataType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            type = Type.FP
                            deviceID = deviceId
                        }
                    }
                })
            }
        }
    }
}