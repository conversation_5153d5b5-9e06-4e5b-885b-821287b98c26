package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.GetDSPConfigurationRequestType
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.GetDSPConfigurationUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelPrice
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.ui.dashboard.main.state.DispenserState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.NozzleState
import com.swiftsku.swiftpos.ui.dashboard.main.state.ProductState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetDashboardFuelPumpsUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getDSPConfiguration: GetDSPConfigurationUseCase,
    private val updateCurrentFuelingStatus: UpdateDispenserStateWithCurrentFuelingStatusUseCase,
    private val updateFPState: UpdateDSPStateWithFPStateUseCase,
) {


    suspend operator fun invoke(
        input: GetDSPConfigurationRequestType,
        dspStateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        launch {
            getDSPConfiguration
                .dspConfiguration
                .collectLatest { configuration ->

                    if (!configuration.isForCurrentDevice(
                            workstationId = fdcConfig.workstationID,
                            requestID = input.requestID
                        )
                    ) {
                        return@collectLatest
                    }

                    val dispensers = configuration.fdCdata.deviceClass
                        .filter { filter -> filter.type == Type.DSP }
                        .map { dsp ->
                            val state = DispenserState(
                                deviceId = dsp.deviceID,
                                product = dsp.product.map { product ->
                                    ProductState(
                                        productNo = product.productNo,
                                        productName = product.productName,
                                        fuelPrice = product.fuelPrice.map { fuelPrice ->
                                            FuelPrice(fuelPrice.modeNo, fuelPrice.fuelPrice)
                                        }
                                    )
                                },
                                fuelPump = dsp.deviceClass.filter { pump -> pump.type == Type.FP }
                                    .map { pump ->
//                                        launch { watchPumpUseCase(pump.deviceID, dspStateFlow) }
                                        FuelPumpState(pumpNo = pump.pumpNo,
                                            deviceId = pump.deviceID,
                                            nozzle = pump.nozzle.map { nozzle ->
                                                NozzleState(
                                                    nozzleNo = nozzle.nozzleNo,
                                                    productNo = nozzle.productID.productNo,
                                                    blendRatio = nozzle.productID.blendRatio,
                                                    productName = dsp.product.find { product ->
                                                        product.productNo == nozzle.productID.productNo
                                                    }?.productName ?: "Unknown"
                                                )
                                            }
                                        )
                                    }
                            )
                            state
                        }

                    val fdcState = FDCState(dispensers)
                    dspStateFlow.update { fdcState }

                    launch { updateCurrentFuelingStatus.invoke(fdcState, dspStateFlow,fdcConfig) }
                    launch { updateFPState(fdcState, dspStateFlow,fdcConfig) }

                }
        }
        getDSPConfiguration.invoke(input)
    }


}