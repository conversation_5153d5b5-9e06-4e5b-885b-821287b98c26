package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.CloseDeviceRequestType
import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.OpenClosePOSdataType
import com.fdc.core.types.OpenDeviceRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.CloseFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.OpenFPRequestUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject

class StopFuelPumpUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val closeFP: CloseFPRequestUseCase,
    private val openFP: OpenFPRequestUseCase
) {
    suspend fun closePump(
        fdcConfig: FDCConfig,
        pumpId: Int,
        failureCallback: (result: OverallResult) -> Unit
    ) = withContext(dispatcher) {
        launch {
            closeFP.closeFPResponse.collectLatest {
                if (it.overallResult != OverallResult.SUCCESS) {
                    failureCallback.invoke(it.overallResult)
                }
            }
        }

        launch {
            val pumpRequestId = generatePumpRequestId(pumpId)

            closeFP(CloseDeviceRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = OpenClosePOSdataType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = DeviceClassType().apply {
                        type = Type.FP
                        deviceID = pumpId
                    }
                }
            })
        }
    }

    suspend fun openPump(
        fdcConfig: FDCConfig,
        pumpId: Int,
        failureCallback: (result: OverallResult) -> Unit
    ) = withContext(dispatcher) {
        launch {
            openFP.openFPResponse.collectLatest {
                if (it.overallResult != OverallResult.SUCCESS) {
                    failureCallback.invoke(it.overallResult)
                }
            }
        }

        launch {
            val pumpRequestId = generatePumpRequestId(pumpId)

            openFP(OpenDeviceRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = OpenClosePOSdataType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = DeviceClassType().apply {
                        type = Type.FP
                        deviceID = pumpId
                    }
                }
            })
        }
    }
}