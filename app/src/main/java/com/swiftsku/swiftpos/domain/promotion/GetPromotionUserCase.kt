package com.swiftsku.swiftpos.domain.promotion

import com.swiftsku.swiftpos.data.model.ApplicablePromo
import com.swiftsku.swiftpos.data.model.AvailableOffer
import com.swiftsku.swiftpos.data.model.ILTDetail
import com.swiftsku.swiftpos.data.model.MMTDetail
import com.swiftsku.swiftpos.data.model.MixMatchEntry
import com.swiftsku.swiftpos.data.model.Promotion
import com.swiftsku.swiftpos.data.model.PromotionsData
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.promotion.dto.AppliedPromotion
import com.swiftsku.swiftpos.domain.promotion.dto.CartItemDetail
import com.swiftsku.swiftpos.domain.promotion.dto.GetPromotion
import com.swiftsku.swiftpos.domain.promotion.dto.Reward
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toDollars
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import kotlin.math.floor

private fun getPromoAmount(
    mixMatchEntry: MixMatchEntry,
    price: Float,
    lineQty: Int,
): Float {
    val mixMatchUnits = mixMatchEntry.mixMatchUnits.value.toInt()
    if (mixMatchEntry.mixMatchDiscountAmount != null) { // Buy 2, get $1 off
        return ((mixMatchEntry.mixMatchDiscountAmount.value / mixMatchUnits) * lineQty).toFloat()
            .to2Decimal()
    } else if (mixMatchEntry.mixMatchDiscountPercent != null) { // Buy 2, get 50% off
        return ((price * mixMatchEntry.mixMatchDiscountPercent.value * lineQty) / 100f).to2Decimal()
    } else if (mixMatchEntry.mixMatchPrice != null) { // Buy 2 for $5
        return ((price - mixMatchEntry.mixMatchPrice.value / mixMatchUnits) * lineQty).toFloat()
            .to2Decimal()
    }
    return 0f.to2Decimal()
};


fun getBestOffers(
    transactionLines: List<CartItemDetail>,
// The assumption is that the available promotions are sorted by promotion quantity
    availablePromotions: List<MMTDetail>,
    itemLists: List<ILTDetail>,
): Reward {
    // Check for an item line in the transaction
    if (transactionLines.isEmpty()) {
        return Reward()
    }

    // Create a mapping of item list id to item codes
    // This will be used to check if an item is in the item list
    // {itemListID: [itemCode1, itemCode2, ...]}

    val itemListMapping = hashMapOf<String, List<String>>()

    for (item in itemLists) {
        itemListMapping[item.itemListID] =
            item.itemListEntry.map { entry -> "${entry.itemCode.posCode}-${entry.itemCode.pluModifier.value}" }
    }


    val appliedPromotions = mutableListOf<AppliedPromotion>()
    // Keep track of how much of each item has been used in the promotions applied
    var usedQuantityPerItem = hashMapOf<String, Int>()

    val sortedAvailablePromotions = availablePromotions.map {
        it.copy(mixMatchEntry = it.mixMatchEntry.sortedByDescending { it.mixMatchUnits.value })
    }

    // Go through each promotion
    for (promotion in sortedAvailablePromotions) {
        // Get the item list for the promotion

        val hasItems = itemListMapping.containsKey(promotion.itemListID)

        if (!hasItems) {
            continue
        }

        val itemList = itemListMapping[promotion.itemListID] ?: emptyList()
        // Go through each mix match entry in the promotion
        for (mixMatchEntry in promotion.mixMatchEntry) {
            for (transactionLine in transactionLines) {

                val itemLineQty = transactionLine.quantity
                // Check if the item is in the item list

                val transactionLinePluWithModifier =
                    "${transactionLine.pluId}-${transactionLine.pluModifier}"
                if (itemList.indexOf(transactionLinePluWithModifier) == -1) {
                    continue
                }

                // Get the remaining quantity of the item that can be used in the promotion
//                val remainingQty =
//                    itemLine.sellingUnits - (usedQuantityPerItem[itemLine.pluId] ?: 0);
                val remainingQty =
                    itemLineQty - (usedQuantityPerItem[transactionLinePluWithModifier] ?: 0);

                // Group them in multiples of the mix match units
                // For example, if the mix match units is 2, then group them in 2s
                // If the remaining quantity is 1, then take 0
                // If the remaining quantity is 2, then take 2
                // If the remaining quantity is 3, then take 2
                // If the remaining quantity is 4, then take 4
                // If the remaining quantity is 5, then take 4
                val quantity: Int =
                    (floor(remainingQty / mixMatchEntry.mixMatchUnits.value) * mixMatchEntry.mixMatchUnits.value).toInt()

                // If the quantity is 0, then skip
                if (quantity <= 0) {
                    continue;
                }
                // Add the quantity to the used quantity for the item
                usedQuantityPerItem[transactionLinePluWithModifier] =
                    ((usedQuantityPerItem[transactionLinePluWithModifier] ?: 0) + quantity)
                // Add the promotion to the reward

                val promo = AppliedPromotion(
                    transactionItemId = transactionLine.itemLineId,
                    promotion = Promotion(
                        promotionID = promotion.promotion.promotionID,
                        promotionAmount = getPromoAmount(
                            mixMatchEntry, transactionLine.price, quantity
                        ),
                        promotionReason = promotion.mixMatchDescription,
                    )
                )
                appliedPromotions.add(promo)
            };

            // For the remaining quantity, loop through one by one, keep building the promotion quantity.
            // Once it reaches the required mix match units, add the promotion to the reward
            var promotionRewardQuantity = 0;
            val tempPromotion = mutableListOf<AppliedPromotion>()
            val tempUsedQuantityPerItem = HashMap(usedQuantityPerItem)

            for (transactionLine in transactionLines) {
                // Same early logic
                val itemLineQty = transactionLine.quantity
                val transactionLinePluWithModifier =
                    "${transactionLine.pluId}-${transactionLine.pluModifier}"
                if (itemList.indexOf(transactionLinePluWithModifier) == -1) {
                    continue;
                }

                while (true) {
                    // Keep track of the remaining quantity
//                    val remainingQty =
//                        itemLine.sellingUnits -
//                                (tempUsedQuantityPerItem[itemLine.pluId] ?: 0);
                    val remainingQty =
                        itemLineQty -
                                (tempUsedQuantityPerItem[transactionLinePluWithModifier] ?: 0);
                    // Group by 1
                    val quantity = 1;

                    if (remainingQty <= 0) {
                        break
                    }
                    // Maintain a temporary tracker
                    promotionRewardQuantity += quantity;
                    tempUsedQuantityPerItem[transactionLinePluWithModifier] =
                        (tempUsedQuantityPerItem[transactionLinePluWithModifier] ?: 0) +
                                quantity;
                    val promo = AppliedPromotion(
                        transactionItemId = transactionLine.itemLineId,
                        Promotion(
                            promotionID = promotion.promotion.promotionID,
                            promotionAmount = getPromoAmount(
                                mixMatchEntry,
                                transactionLine.price,
                                quantity,
                            ),
                            promotionReason = promotion.mixMatchDescription,
                        )
                    )
                    tempPromotion.add(promo);

                    // If the promotion quantity reaches the required mix match units, then add the promotion to the reward
                    // Reset the temporary tracker
                    if (
                        promotionRewardQuantity == mixMatchEntry.mixMatchUnits.value.toInt()
                    ) {
                        usedQuantityPerItem = tempUsedQuantityPerItem

                        appliedPromotions.addAll(tempPromotion)
                        tempPromotion.clear()
                        promotionRewardQuantity = 0;
                    }
                }
            }
            // If there are any remaining promotions, add them to the reward
            if (
                promotionRewardQuantity >= mixMatchEntry.mixMatchUnits.value.toInt()
            ) {
                usedQuantityPerItem = tempUsedQuantityPerItem
                appliedPromotions.addAll(tempPromotion)
            }

        }


    }
    return Reward(appliedPromotions)
}

fun getSavingAmount(availableOffer: AvailableOffer): Float {
    val mixMatchEntry = availableOffer.mixMatchEntry
    val pluItem = availableOffer.pluItem
    val mixMatchUnits = mixMatchEntry.mixMatchUnits.value.toInt()

    val savings: Float = when {
        mixMatchEntry.mixMatchDiscountAmount != null -> {
            mixMatchEntry.mixMatchDiscountAmount.value.toFloat()
        }

        mixMatchEntry.mixMatchDiscountPercent != null -> {
            val totalPrice = pluItem.price * mixMatchUnits
            (totalPrice * mixMatchEntry.mixMatchDiscountPercent.value / 100f)
        }

        mixMatchEntry.mixMatchPrice != null -> {
            // Calculate savings from fixed price
            val regularPrice = pluItem.price * mixMatchUnits
            val discountedPrice = mixMatchEntry.mixMatchPrice.value
            (regularPrice - discountedPrice).toFloat()
        }

        else -> 0f
    }

    return savings.to2Decimal()
}

private fun getPromoText(description: String, mixMatchEntry: MixMatchEntry): String? {
    val mixMatchUnits = mixMatchEntry.mixMatchUnits.value.toInt()
    val discountAmt = mixMatchEntry.mixMatchDiscountAmount?.value
    val discountPerc = mixMatchEntry.mixMatchDiscountPercent?.value
    val mixMatchPrice = mixMatchEntry.mixMatchPrice?.value
    return when {
        mixMatchUnits > 0 && discountAmt != null -> "Buy $mixMatchUnits ${description.take(33)}, Get ${discountAmt.toDollars()} off"
        mixMatchUnits > 0 && discountPerc != null -> "Buy $mixMatchUnits $description, Get $discountPerc% off"
        mixMatchUnits > 0 && mixMatchPrice != null -> "Buy $mixMatchUnits $description for ${mixMatchPrice.toDollars()}"
        else -> null
    }
}

fun getPromoTextAlt(availableOffer: AvailableOffer, withLineBreak: Boolean = false): String? {
    val mixMatchEntry = availableOffer.mixMatchEntry
    val mixMatchUnits = mixMatchEntry.mixMatchUnits.value.toInt()
    val discountAmt = mixMatchEntry.mixMatchDiscountAmount?.value
    val discountPerc = mixMatchEntry.mixMatchDiscountPercent?.value
    val mixMatchPrice = mixMatchEntry.mixMatchPrice?.value

    return if (withLineBreak) {
        when {
            discountAmt != null -> "BUY $mixMatchUnits\nGet\n${discountAmt.toDollars()} off"
            discountPerc != null -> "BUY $mixMatchUnits\nGet\n$discountPerc% off"
            mixMatchPrice != null -> "BUY $mixMatchUnits\nFor\n${mixMatchPrice.toDollars()}"
            else -> null
        }
    } else {
        when {
            discountAmt != null -> "Buy $mixMatchUnits get ${discountAmt.toDollars()} off"
            discountPerc != null -> "Buy $mixMatchUnits get $discountPerc% off"
            mixMatchPrice != null -> "Buy $mixMatchUnits for ${mixMatchPrice.toDollars()}"
            else -> null
        }
    }
}

class GetPromotionUserCase @Inject constructor(
    private val getMMTDetailsUseCase: GetMMTDetailsUseCase,
    private val getILTDetailsUseCase: GetILTDetailsUseCase,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {
    suspend operator fun invoke(input: GetPromotion): List<AppliedPromotion> =
        withContext(dispatcher) {


            val rewards =
                getBestOffers(
                    input.items,
                    getMMTDetailsUseCase(input.startDate, input.endDate, input.storeId),
                    getILTDetailsUseCase(input.startDate, input.endDate, input.storeId),
                )


            return@withContext rewards.promotions
        }

    suspend fun getAllPromotions(storeCode: String): PromotionsData {
        val date = Date()
        val mmtDetails = getMMTDetailsUseCase(date, date, storeCode)
        val iltDetails = getILTDetailsUseCase(date, date, storeCode)
        return PromotionsData(mmtDetails, iltDetails)
    }

    private fun getApplicablePromotions(
        cartItems: List<TransactionItemWithPLUItem>,
        promotionsData: PromotionsData
    ): List<ApplicablePromo> {
        val applicablePromotions = mutableListOf<ApplicablePromo>()
        val itemListMapping = hashMapOf<String, List<String>>()
        for (iltDetail in promotionsData.iltDetails) {
            itemListMapping[iltDetail.itemListID] = iltDetail.itemListEntry.map { entry ->
                "${entry.itemCode.posCode}-${entry.itemCode.pluModifier.value}"
            }
        }
        promotionsData.mmtDetails.forEach { promo ->
            val itemList = promo.itemListID
            cartItems.forEach {
                val itemPluId = "${it.pluItem.pluId}-${it.pluItem.pluModifier}"
                if (itemListMapping[itemList]?.contains(itemPluId) == true) {
                    applicablePromotions.add(
                        ApplicablePromo(it.transactionItemId, it.pluItem, promo.mixMatchEntry)
                    )
                }
            }
        }
        return applicablePromotions
    }

    fun getAvailableOffers(
        promotionsData: PromotionsData,
        cartItems: List<TransactionItemWithPLUItem>,
        appliedPromotions: List<AppliedPromotion>
    ): List<AvailableOffer> {
        val applicableOffers = arrayListOf<AvailableOffer>()
        val applicablePromotions = getApplicablePromotions(cartItems, promotionsData)
        val appliedTxnItems = appliedPromotions.map { it.transactionItemId }
        applicablePromotions.forEach {
            it.promos.forEach mixMatchEntry@{ mixMatchEntry ->
                if (appliedTxnItems.contains(it.transactionItemId)) {
                    return@mixMatchEntry
                }
                getPromoText(it.pluItem.description, mixMatchEntry)?.let { promoText ->
                    applicableOffers.add(AvailableOffer(it.pluItem, mixMatchEntry, promoText))
                }
            }
        }
        return applicableOffers
    }
}