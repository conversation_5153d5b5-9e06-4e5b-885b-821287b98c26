package com.swiftsku.swiftpos.domain.pricebook

import com.swiftsku.swiftpos.data.model.Promotion
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.pricebook.dto.JustUpdateItemLines
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookAdd
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookIncrement
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookUpdate
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookUpdateQuantity
import com.swiftsku.swiftpos.domain.pricebook.dto.UpdatePbTxnItemResult
import com.swiftsku.swiftpos.domain.promotion.GetPromotionUserCase
import com.swiftsku.swiftpos.domain.promotion.dto.CartItemDetail
import com.swiftsku.swiftpos.domain.promotion.dto.GetPromotion
import com.swiftsku.swiftpos.domain.tax.GetTaxForAmountMapUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxMap
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalEBT
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject


class UpdatePriceBookTxnItemsUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getTaxForAmountMapUseCase: GetTaxForAmountMapUseCase,
    private val getPromotionUserCase: GetPromotionUserCase,
    private val ebtCalculationsUseCase: EBTCalculationsUseCase
) {


    suspend operator fun invoke(input: PriceBookUpdate): UpdatePbTxnItemResult =
        withContext(dispatcher) {
            var previouslyAdded = input.cartItems

            val transactionSummary = input.transactionSummary

            var items = previouslyAdded.filterIsInstance<TransactionItemWithPLUItem>()
                .filter { it.status == TransactionItemStatus.Normal }
                .map { txnItem ->

                    var q = txnItem.quantity

                    if ((input is PriceBookIncrement && txnItem.transactionItemId == input.itemLine.transactionItemId)) {
                        q++
                    }

                    if (input is PriceBookUpdateQuantity && txnItem.transactionItemId == input.itemLine.transactionItemId) {
                        q = input.quantity

                    }

                    CartItemDetail(
                        pluId = txnItem.pluItem.pluId,
                        itemLineId = txnItem.transactionItemId,
                        quantity = q,
                        price = txnItem.pluItem.price,
                        pluModifier = txnItem.pluItem.pluModifier
                    )
                }

            when (input) {
                is PriceBookAdd -> {
                    val pluItem = input.pluItem
                    val addItem = TransactionItemWithPLUItem(
                        pluItem = pluItem,
                        taxation = getTaxForAmountMapUseCase(
                            GetTaxMap(
                                taxIds = pluItem.taxIds,
                                storeCode = input.storeCode,
                                itemPrice = pluItem.price,
                                discount = 0.0
                            )
                        )
                    )
                    items = items.plus(
                        CartItemDetail(
                            pluId = addItem.pluItem.pluId,
                            itemLineId = addItem.transactionItemId,
                            quantity = addItem.quantity,
                            price = addItem.pluItem.price,
                            pluModifier = addItem.pluItem.pluModifier
                        )
                    )
                    // add item
                    previouslyAdded = previouslyAdded.plus(addItem)
                }

                is PriceBookIncrement -> {
                    items = items.plus(
                        CartItemDetail(
                            pluId = input.itemLine.pluItem.pluId,
                            itemLineId = input.itemLine.transactionItemId,
                            quantity = input.itemLine.quantity + 1,
                            price = input.itemLine.pluItem.price,
                            pluModifier = input.itemLine.pluItem.pluModifier
                        )
                    )
                }

                is PriceBookUpdateQuantity -> {
                    items = items.plus(
                        CartItemDetail(
                            pluId = input.itemLine.pluItem.pluId,
                            itemLineId = input.itemLine.transactionItemId,
                            quantity = input.quantity,
                            price = input.itemLine.pluItem.price,
                            pluModifier = input.itemLine.pluItem.pluModifier
                        )
                    )
                }

                is JustUpdateItemLines -> {}
            }

            val promotions = getPromotionUserCase(
                GetPromotion(
                    storeId = input.storeCode,
                    startDate = Date(),
                    endDate = Date(),
                    items = items
                )
            )
            val map = mutableMapOf<String, MutableList<Promotion>>()
            promotions.forEach { promotion ->
                if (promotion.transactionItemId in map.keys) {
                    map[promotion.transactionItemId]?.add(promotion.promotion)
                } else {
                    map[promotion.transactionItemId] = mutableListOf(promotion.promotion)
                }
            }

            previouslyAdded = previouslyAdded.map {

                if (it is TransactionItemWithPLUItem) {
                    var discount = 0.0

                    it.discount.loyalty.forEach { loyalty -> discount += loyalty.amount }

                    map[it.transactionItemId]?.forEach { promotion ->
                        discount += promotion.promotionAmount
                    }

                    var quantity = it.quantity

                    if ((input is PriceBookIncrement && it.transactionItemId == input.itemLine.transactionItemId)) {
                        quantity++
                    }

                    if (input is PriceBookUpdateQuantity && it.transactionItemId == input.itemLine.transactionItemId) {
                        quantity = input.quantity

                    }


                    val taxation = getTaxForAmountMapUseCase(
                        GetTaxMap(
                            existing = it.taxation,
                            taxIds = it.pluItem.taxIds,
                            storeCode = input.storeCode,
                            itemPrice = it.pluItem.price,
                            quantity = quantity,
                            discount = discount.to2Decimal()
                        )
                    )

                    val discardTaxation = it.isEBT() && transactionSummary.ebtAmountCollected > 0

                    return@map it.copy(
                        quantity = quantity,
                        taxation = if (discardTaxation) emptyMap() else taxation,
                        promotion = map[it.transactionItemId],
                    )
                }
                it
            }

            if (transactionSummary.ebtAmountCollected > 0 && transactionSummary.ebtAmountCollected < transactionSummary.totalEBT()) {
                previouslyAdded = ebtCalculationsUseCase(
                    EBTCalculationInput(
                        previouslyAdded,
                        input.taxes,
                        transactionSummary.ebtAmountCollected
                    )
                )
            }

            UpdatePbTxnItemResult(previouslyAdded, promotions)
        }
}