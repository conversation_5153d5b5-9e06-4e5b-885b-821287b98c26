package com.swiftsku.swiftpos.domain.pricebook.dto

import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary


sealed class PriceBookUpdate {
    abstract val cartItems: List<TransactionItem>
    abstract val storeCode: String
    abstract val transactionSummary: TransactionSummary
    abstract val taxes: Map<String, Tax>
}

data class JustUpdateItemLines(
    override val cartItems: List<TransactionItem>,
    override val storeCode: String,
    override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : PriceBookUpdate()

data class PriceBookAdd(
    val pluItem: PluItem,
    override val cartItems: List<TransactionItem>,
    override val storeCode: String, override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : PriceBookUpdate()

data class PriceBookIncrement(
    val itemLine: TransactionItemWithPLUItem,
    override val cartItems: List<TransactionItem>,
    override val storeCode: String, override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : PriceBookUpdate()

data class PriceBookUpdateQuantity(
    val itemLine: TransactionItemWithPLUItem,
    override val cartItems: List<TransactionItem>,
    override val storeCode: String,
    val quantity: Int,
    override val transactionSummary: TransactionSummary,
    override val taxes: Map<String, Tax>
) : PriceBookUpdate()


sealed class PriceBookUpdateType(val value: String) {
    object AddOrIncrement : PriceBookUpdateType("ADD_INCREMENT")
    object UpdateQuantity : PriceBookUpdateType("UPDATE_QUANTITY")
}