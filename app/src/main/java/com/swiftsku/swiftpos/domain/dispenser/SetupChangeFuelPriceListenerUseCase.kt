package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.ErrorCode
import com.fdc.core.types.GetDSPConfigurationPOSdataType
import com.fdc.core.types.GetDSPConfigurationRequestType
import com.swiftsku.fdc.core.di.usecases.dispenser.ChangeFuelPriceRequestUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject

class SetupChangeFuelPriceListenerUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getPumps: GetDashboardFuelPumpsUseCase,
    private val changeFuelPrice: ChangeFuelPriceRequestUseCase
) {

    suspend operator fun invoke(
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) =
        withContext(dispatcher) {
            launch {
                changeFuelPrice
                    .changeFuelPriceResponse
                    .collectLatest { priceChangeRes ->

                        if (priceChangeRes.fdCdata.errorCode.value == ErrorCode.ERRCD_OK) {
                            // Re-fetch DSP config to update fuel prices
                            getPumps.invoke(
                                GetDSPConfigurationRequestType().apply {
                                    poSdata = GetDSPConfigurationPOSdataType().apply {
                                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                                    }
                                    applicationSender = fdcConfig.applicationSender
                                    workstationID = fdcConfig.workstationID
                                    requestID = generatePumpRequestId(12)
                                },
                                stateFlow,
                                fdcConfig
                            )
                        }
                    }
            }
        }
}