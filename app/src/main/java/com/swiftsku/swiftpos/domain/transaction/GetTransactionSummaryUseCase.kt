package com.swiftsku.swiftpos.domain.transaction

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.Fee
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

class GetTransactionSummaryUseCase(@IODispatcher private val dispatcher: CoroutineDispatcher) {

    suspend operator fun invoke(
        items: List<TransactionItem>,
        couponAmount: Float,
        lotteryPayouts: List<LotteryPayout>,
        feeConfig: List<Fee>?,
        previousSummary: TransactionSummary
    ) = withContext(dispatcher) {
        val lotteryAmount = lotteryPayouts.sumOfFloats { it.amount }
        if (items.isEmpty()) {
            return@withContext TransactionSummary().copy(
                lotteryAmount = lotteryAmount
            )
        }
        var transactionTotalNetAmount = 0f
        var totalTax = 0f
        var totalPromotion = 0f
        var totalEBT = 0f
        var totalEBTPromotion = 0f
        var ebtTax = 0f
        var allEBT = true

        // calculate tax
        items.forEach {
            if (it.status == TransactionItemStatus.Deleted) {
                return@forEach
            }
            when (it) {
                is TransactionItemWithPLUItem -> {
                    val itemPrice = it.totalItemPrice()
                    if (it.isEBT()) {
                        totalEBT += itemPrice
                    } else {
                        allEBT = false
                    }
                    it.taxation.forEach { (_, taxAmount) ->
                        totalTax += taxAmount
                        if (it.isEBT()) {
                            ebtTax += taxAmount
                        }
                    }
                    it.promotion?.forEach { promotion ->
                        totalPromotion += promotion.promotionAmount
                        if (it.isEBT()) {
                            totalEBTPromotion += promotion.promotionAmount
                        }
                    }
                    transactionTotalNetAmount += itemPrice
                }

                is TransactionItemWithDepartment -> {
                    val itemPrice = it.totalItemPrice()
                    if (it.isEBT()) {
                        totalEBT += itemPrice
                    } else {
                        allEBT = false
                    }
                    it.taxation.forEach { (_, taxAmount) ->
                        totalTax += taxAmount
                        if (it.isEBT()) {
                            ebtTax += taxAmount
                        }
                    }
                    transactionTotalNetAmount += itemPrice
                }

                is TransactionItemWithFuel -> {
                    val price = it.totalItemPrice()
                    it.taxation.forEach { (taxId, taxAmount) ->
                        totalTax += taxAmount
                    }
                    transactionTotalNetAmount += (price.toFloat())
                }
            }
        }


        val loyaltyDiscountAmount =
            (items.filter { item -> item.status == TransactionItemStatus.Normal }
                .sumOf { item ->
                    item.discount.loyalty.sumOf { it.amount }
                }).toFloat()
        val nonDeletedItems = items.filter { item -> item.status == TransactionItemStatus.Normal }
        val ebtLoyaltyDiscounts = (nonDeletedItems.sumOf { item ->
            if (item.isEBT()) item.discount.loyalty.sumOf { it.amount } else 0.0
        }).toFloat()

        val txnGrand = transactionTotalNetAmount + totalTax - loyaltyDiscountAmount - totalPromotion

        val newSummary = previousSummary.copy(
            transactionTotalNetAmount = transactionTotalNetAmount.to2Decimal(),
            transactionTotalGrossAmount = (transactionTotalNetAmount - totalPromotion - loyaltyDiscountAmount).to2Decimal(),
            transactionTotalTaxNetAmount = totalTax.to2Decimal(),
            transactionTotalGrandAmount = txnGrand.to2Decimal(),
            promotionApplied = (totalPromotion + loyaltyDiscountAmount).to2Decimal(),
            ebtAmount = totalEBT,
            ebtPromotion = totalEBTPromotion,
            ebtLoyalty = ebtLoyaltyDiscounts,
            couponAmount = couponAmount,
            lotteryAmount = lotteryAmount,
            ebtTax = ebtTax,
            allEBT = allEBT
        )
        val cardFee = GetFeesUseCase.getCardProcessingFee(newSummary.pendingAmount(), feeConfig)
        val calculatedFees = mutableListOf<AppliedFee>()
        cardFee?.let { calculatedFees.add(it) }

        val transactionSummary = newSummary.copy(
            calculatedFees = calculatedFees
        )


        transactionSummary
    }
}