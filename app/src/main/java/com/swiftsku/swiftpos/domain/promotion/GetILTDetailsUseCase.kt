package com.swiftsku.swiftpos.domain.promotion


import com.swiftsku.swiftpos.data.couchbase.promotion.PromotionsRepository
import com.swiftsku.swiftpos.data.model.ILTDetail
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject

class GetILTDetailsUseCase @Inject constructor(
    private val promotionsRepository: PromotionsRepository,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(startDate: Date, endDate: Date, storeId: String): List<ILTDetail> =
        withContext(dispatcher) {
            promotionsRepository.getILTDetails(startDate, endDate, storeId)
        }
}