package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.GetCurrentFuelingStatusPOSdataType
import com.fdc.core.types.GetCurrentFuelingStatusRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.GetCurrentFuelingStatusUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelingStatus
import com.swiftsku.swiftpos.data.model.POSTransactionData
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject


class WatchPumpUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getCurrentFuelStatus: GetCurrentFuelingStatusUseCase
) {
    private val watchPumpInvokeJobs = ConcurrentHashMap<Int, Job>()
    private val watchPumpObservingJobs = ConcurrentHashMap<Int, Job>()

    fun isWatching(deviceId: Int) = watchPumpObservingJobs.containsKey(deviceId)

    fun stopWatchingPump(deviceId: Int) {
        watchPumpInvokeJobs.remove(deviceId)?.cancel()
        watchPumpObservingJobs.remove(deviceId)?.cancel()
    }

    suspend operator fun invoke(
        pumpDeviceId: Int,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        val observeJob = launch {
            getCurrentFuelStatus
                .currentFuelingStatus
                .collectLatest { currentStatus ->
                    if (
                        !currentStatus.isForCurrentDevice(workstationId = fdcConfig.workstationID)
                        || currentStatus.overallResult != OverallResult.SUCCESS
                        || currentStatus.fdCdata.deviceClass.deviceID != pumpDeviceId
                    ) {
                        return@collectLatest
                    }

                    val deviceClass = currentStatus.fdCdata.deviceClass
                    stateFlow.update { currentState ->
                        val newState =
                            currentState.copy(dispensers = currentState.dispensers.map { dispenserState ->
                                dispenserState.copy(fuelPump = dispenserState.fuelPump.map { fuelPumpState ->
                                    if (pumpDeviceId == fuelPumpState.deviceId) {
                                        fuelPumpState.copy(
                                            currentFuelStatus = FuelingStatus(
                                                currentAmount = deviceClass.currentAmount,
                                                currentVolume = deviceClass.currentVolume,
                                                currentUnitPrice = deviceClass.currentUnitPrice,
                                                currentNozzle = deviceClass.currentNozzle,
                                                releaseToken = deviceClass.releaseToken,
                                                authorisationApplicationSender = deviceClass.authorisationApplicationSender,
                                                postTransactionData = POSTransactionData(
                                                    posTransactionData = deviceClass.posTransData.posTransactionData,
                                                    tranType = deviceClass.posTransData.tranType,
                                                    epsStan = deviceClass.posTransData.epsstan,
                                                    merchandiseTrxAmount = deviceClass.posTransData.merchandiseTrxAmount,
                                                )
                                            )
                                        )
                                    } else {
                                        fuelPumpState
                                    }
                                })
                            })
                        newState
                    }
                }
        }
        watchPumpObservingJobs[pumpDeviceId] = observeJob

        val invokeJob = launch {
            while (isActive) {
                getCurrentFuelStatus.invoke(GetCurrentFuelingStatusRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = generatePumpRequestId(pumpDeviceId)
                    poSdata = GetCurrentFuelingStatusPOSdataType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            deviceID = pumpDeviceId
                            type = Type.FP
                        }
                    }
                })
                delay(1500)
            }
        }
        watchPumpInvokeJobs[pumpDeviceId] = invokeJob
    }
}