package com.swiftsku.swiftpos.domain.payment.dto

import com.pax.poslinkadmin.constant.TransactionType
import com.pax.poslinksemiintegration.constant.EbtCountType
import kotlinx.serialization.Serializable


/**
 * A serializable class that mimics DoCreditRequest class from PAX SDK.
 * This has only the relevant variables from the original class.
 */
@Serializable
data class DoCreditRequestDto(
    val epoch: Int,
    val transactionType: TransactionType?,
    val amountInformation: AmountRequest,
    val cashierInformation: CashierRequest,
    val traceInformation: TraceRequest,
    val transactionBehavior: TransactionBehavior,
    val accountInformation: AccountRequest,
    val batchId: String,
    val originalReferenceNumber: String? = null
)

@Serializable
data class AmountRequest(
    val transactionAmount: String? = "",
    val tipAmount: String? = "",
    val cashBackAmount: String? = "",
    val merchantFee: String? = "",
    val taxAmount: String? = "",
    val fuelAmount: String? = "",
    val serviceFee: String? = ""
)

@Serializable
data class CashierRequest(
    val clerkId: String? = "",
    val shiftId: String? = ""
)

@Serializable
data class TraceRequest(
    val invoiceNumber: String? = "",
    val timeStamp: String? = ""
)

@Serializable
data class TransactionBehavior(
    val entryMode: EntryModeBitmap
)

@Serializable
data class EntryModeBitmap(
    val manual: Boolean? = true,
    val swipe: Boolean? = true,
    val chip: Boolean? = true,
    val contactless: Boolean? = true,
    val scan: Boolean? = true,
    val checkReader: Boolean? = true,
)

@Serializable
data class AccountRequest(
    val ebtType: EbtCountType? = null
)