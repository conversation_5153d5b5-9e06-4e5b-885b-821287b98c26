package com.swiftsku.swiftpos.domain.cart

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.cart.dto.DeleteCartItemInput
import com.swiftsku.swiftpos.domain.cart.dto.DeleteCartItemResult
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject


class DeleteCartItemUseCase(
    @IODispatcher private val ioDispatcher: CoroutineDispatcher,
    private val transactionRepository: TransactionRepository
) {

    suspend operator fun invoke(input: DeleteCartItemInput): DeleteCartItemResult {
        var previouslyAdded = input.txnItems
        val item = input.item
        val currentTransaction = input.currentTransaction
        withContext(ioDispatcher) {
            previouslyAdded =
                previouslyAdded
                    .map {
                        if (it.transactionItemId == item.transactionItemId) {
                            return@map when (it) {
                                is TransactionItemWithDepartment -> it.copy(status = TransactionItemStatus.Deleted)
                                is TransactionItemWithFuel -> it.copy(status = TransactionItemStatus.Deleted)
                                is TransactionItemWithPLUItem -> it.copy(status = TransactionItemStatus.Deleted)
                            }
                        }
                        it
                    }
        }

        val clearCart = previouslyAdded.none { it.status == TransactionItemStatus.Normal }

        return DeleteCartItemResult(
            clearCart = clearCart,
            txnItems = previouslyAdded
        )
    }
}