package com.swiftsku.swiftpos.domain.transaction

import com.apollographql.apollo3.api.Optional
import com.swiftsku.swiftpos.data.model.PaymentResponse
import com.swiftsku.swiftpos.data.model.RefundResponse
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.getRefundableAmounts
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.modules.hotp.HOTPGenerator
import com.swiftsku.swiftpos.payment.type.RIM
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.flow.Flow
import java.util.Date


class PaymentRefundUseCase(
    private val paymentRepository: PaymentRepository,
    private val hOtpGenerator: HOTPGenerator
) {

    private fun createRIM(
        storeConfig: StoreConfig,
        transaction: SaleTransaction
    ): RIM {
        val token = hOtpGenerator.generateHOTP(
            tid = transaction.txnId,
            token = storeConfig.token,
            counter = storeConfig.posNumber.toLong()
        )
        val refundableAmounts = getRefundableAmounts(transaction)
        val cardRefundCents = refundableAmounts.cardAmount.convertDollarToCentPrecisely()
        val ebtRefundCents = refundableAmounts.ebtAmount.convertDollarToCentPrecisely()

        return RIM(
            token = token,
            attempt = Optional.present(1),
            txnId = Optional.present(transaction.txnId),
            ebtAmount = if (ebtRefundCents > 0) Optional.present(ebtRefundCents) else Optional.Absent,
            genAmount = if (cardRefundCents > 0) Optional.present(cardRefundCents) else Optional.Absent
        )
    }

    suspend fun processRefund(
        storeConfig: StoreConfig,
        cashierId: String,
        startTime: Date,
        transaction: SaleTransaction
    ): Flow<Result<PaymentResponse>> {
        return paymentRepository.securedPaymentRefunder(
            uid = cashierId,
            tData = createRIM(storeConfig, transaction),
            eventEpoch = startTime.epochInSeconds()
        )
    }

    suspend fun processRefundV2(
        storeConfig: StoreConfig,
        cashierId: String,
        startTime: Date,
        transaction: SaleTransaction
    ): Flow<Result<RefundResponse>> {
        return paymentRepository.refundProcessor(
            uid = cashierId,
            tData = createRIM(storeConfig, transaction),
            eventEpoch = startTime.epochInSeconds()
        )
    }
}