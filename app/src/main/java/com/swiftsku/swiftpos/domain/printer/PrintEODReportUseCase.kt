package com.swiftsku.swiftpos.domain.printer

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LegacyPrinter
import com.swiftsku.swiftpos.modules.printer.EODReportTemplate
import com.swiftsku.swiftpos.modules.printer.EODReportTemplatePrinter
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject

class PrintEODReportUseCase @Inject constructor(
    @LegacyPrinter private val legacyPrinterManager: ISunMiPrinter,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(template: EODReportTemplate) {
        try {
            legacyPrinterManager.print(
                EODReportTemplatePrinter(
                    eodReport = template,
                    coroutineDispatcher = dispatcher
                )
            )
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

}