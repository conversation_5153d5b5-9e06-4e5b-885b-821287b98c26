package com.swiftsku.swiftpos.domain.tax

import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetCachedTaxUseCase @Inject constructor(
    private val refDataRepository: RefDataRepository,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {
    private val map = mutableMapOf<String, Tax>()


    suspend operator fun invoke(
        storeCode: String,
        taxId: String,
        forceCall: Boolean = false
    ): Tax? = withContext(dispatcher) {
            if (map.contains(taxId) && !forceCall) {
                return@withContext map[taxId]
            }
            val tax = refDataRepository.getTax(taxId, storeCode)

            if (tax != null) {
                map[taxId] = tax
                return@withContext tax
            }

            null
        }

    fun clearCache() {
        map.clear()
    }
}



