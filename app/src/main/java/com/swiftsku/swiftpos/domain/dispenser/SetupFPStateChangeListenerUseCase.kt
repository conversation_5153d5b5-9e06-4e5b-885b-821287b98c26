package com.swiftsku.swiftpos.domain.dispenser

import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFPStateChangeMessageUseCase
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SetupFPStateChangeListenerUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val listenForFPStateChangeMessageUseCase: ListenForFPStateChangeMessageUseCase,
) {

    suspend operator fun invoke(
        stateFlow: MutableStateFlow<FDCState>
    ) = withContext(dispatcher) {
        listenForFPStateChangeMessageUseCase.fpStateChangeMessage.collectLatest { fpState ->
            stateFlow.update { state ->
                state.copy(dispensers = state.dispensers.map { dispenser ->
                    dispenser.copy(fuelPump = dispenser.fuelPump.map pumpMap@{ pump ->
                        val deviceID = fpState.fdCdata.deviceClass.deviceID
                        if (pump.deviceId == deviceID
                        ) {
                            val currentFPState = pump.fpState
                            if (currentFPState != null) {
                                return@pumpMap pump.copy(fpState = currentFPState.copy(fpState = fpState.fdCdata.deviceClass))
                            }
                        }
                        pump
                    })
                })
            }
        }
    }
}