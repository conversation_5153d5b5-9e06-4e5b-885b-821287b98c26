package com.swiftsku.swiftpos.utils

import com.apollographql.apollo3.api.ApolloRequest
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.Operation
import com.apollographql.apollo3.interceptor.ApolloInterceptor
import com.apollographql.apollo3.interceptor.ApolloInterceptorChain
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.BuildConfig
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach

class LoggingApolloInterceptor : ApolloInterceptor {
    override fun <D : Operation.Data> intercept(
        request: ApolloRequest<D>,
        chain: ApolloInterceptorChain
    ): Flow<ApolloResponse<D>> = chain.proceed(request).onEach { response ->
        if (BuildConfig.DEBUG) {
//            Logger.d("Received response for ${request.operation.name()}: ${response.data}")
        }
    }
}