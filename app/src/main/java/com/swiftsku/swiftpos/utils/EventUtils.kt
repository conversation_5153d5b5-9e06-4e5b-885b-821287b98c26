package com.swiftsku.swiftpos.utils

import android.os.Bundle
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.posthog.PostHog
import com.swiftsku.swiftpos.extension.epochInSeconds
import io.sentry.Sentry
import java.util.Date


object EventUtils {

    object UserProp {
        const val SERIAL_NO = "serial_no"
        const val USER_NAME = "user_name"
        const val POS_NO = "pos_no"
        const val POS_ID = "pos_id"
    }

    object Events {
        const val VOID_CLICK = "void_click"
        const val SAVE_CLICK = "save_click"
        const val NO_SALE_CLICK = "no_sale_click"
        const val LOYALTY_CLICK = "loyalty_click"
        const val COUPON_CLICK = "coupon_click"
        const val PAYOUT_CLICK = "payout_click"
        const val FIXED_AMOUNT_CLICK = "fixed_amount_click"
        const val DYNAMIC_AMOUNT_CLICK = "dynamic_amount_click"
        const val CASH_CLICK = "cash_click"
        const val CHECK_CLICK = "check_click"
        const val CARD_CLICK = "card_click"
        const val EBT_CLICK = "ebt_click"
        const val PLU_SEARCH = "plu_search"

        const val CART_ITEM_DELETE = "cart_item_delete"
        const val CART_ITEM_EDIT = "cart_item_edit"
    }

    object EventProp {
        const val AMOUNT = "amount"
        const val ITEM_NAME = "item_name"
        const val SEARCH_QUERY = "search_query"
        const val PENDING_TXN = "pending_txn"
    }

    fun setUserProperty(propertyName: String, propertyValue: String) {
        Firebase.analytics.setUserProperty(propertyName, propertyValue)
        PostHog.register(propertyName, propertyValue)
        Sentry.setTag(propertyName, propertyValue)
    }

    /**
     * Logs an event to all supported SDKs.
     * @param eventName Name of the event
     * @param eventProps Map of properties associated with the event
     */
    fun logEvent(eventName: String, eventProps: Map<String, Any> = mapOf()) {
        try {
            // Log event to Firebase
            logToFirebase(eventName, eventProps)
            logToPostHog(eventName, eventProps)
        } catch (ex: Exception) {
            Firebase.crashlytics.recordException(ex)
        }
    }

    private fun logToPostHog(eventName: String, properties: Map<String, Any>) {
        PostHog.capture(event = eventName, properties = properties)
    }

    private fun logToFirebase(eventName: String, properties: Map<String, Any>) {
        val bundle = Bundle()

        // Add epoch time to the properties
        val enhancedProperties = properties.toMutableMap()
        enhancedProperties["epoch"] = Date().epochInSeconds()

        // Convert properties to a Bundle
        for ((key, value) in enhancedProperties) {
            when (value) {
                is String -> bundle.putString(key, value)
                is Int -> bundle.putInt(key, value)
                is Double -> bundle.putDouble(key, value)
                is Boolean -> bundle.putBoolean(key, value)
                else -> bundle.putString(key, value.toString())
            }
        }

        Firebase.analytics.logEvent(eventName, bundle)
    }

    fun recordException(ex: Throwable) {
        Sentry.captureException(ex)
        Firebase.crashlytics.recordException(ex)
    }
}
