package com.swiftsku.swiftpos.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.util.Locale

fun isValidDate(date: String, minDate: LocalDate? = null, maxDate: LocalDate? = null): Boolean {
    val dateFormatter = DateTimeFormatter.ofPattern("MMddyyyy")
    return try {
        val parsedDate = LocalDate.parse(date, dateFormatter)

        if (minDate != null) {
            return parsedDate >= minDate
        }

        if (maxDate != null) {
            return parsedDate <= maxDate
        }

        true // Date is valid
    } catch (e: Exception) {
        false // Date is invalid
    }
}

fun String.parseDate(dateFormat: String = "MMddyyyy"): LocalDate? {
    return try {
        val dateFormatter = DateTimeFormatter.ofPattern(dateFormat)
        LocalDate.parse(this, dateFormatter)
    } catch (e: Exception) {
        null
    }
}

fun getAgeInYearsAndMonths(dob: LocalDate): String {
    val period = Period.between(dob, LocalDate.now())
    return "${period.years} years${if (period.months > 0) " and ${period.months} months" else ""}"
}

fun getFormattedDob(dob: String?): String {
    val inputFormat = SimpleDateFormat("MMddyyyy", Locale.US)
    val outputFormat = SimpleDateFormat("MM/dd/yyyy", Locale.US)
    val inputDob = dob.orEmpty()

    return try {
        inputFormat.parse(inputDob)?.let {
            outputFormat.format(it)
        } ?: inputDob
    } catch (e: Exception) {
        inputDob
    }
}

fun formatAsUSPhoneNumber(input: String): String {
    val digits = input.filter { it.isDigit() }.takeLast(10) // get last 10 digits
    return if (digits.length == 10) {
        "(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}"
    } else {
        input // return as-is if not exactly 10 digits
    }
}

fun copyAssetToExternalFile(context: Context, assetFileName: String): File {
    val baseDir = context.getExternalFilesDir(null) ?: context.filesDir
    val outFile = File(baseDir, assetFileName)
    if (outFile.exists()) return outFile
    context.assets.open(assetFileName).use { inputStream ->
        FileOutputStream(outFile).use { outputStream ->
            inputStream.copyTo(outputStream)
        }
    }
    return outFile
}

fun buildFdcWebSocketUrl(ip: String?, defaultPort: Int = 36101): String? {
    if (ip.isNullOrBlank()) return null
    // Check if a port is already specified
    val hasPort = ip.contains(":") && ip.split(":").last().all { it.isDigit() }
    return if (hasPort) {
        "ws://$ip/fqm/fdc"
    } else {
        "ws://$ip:$defaultPort/fqm/fdc"
    }
}

fun extractYoutubeVideoId(url: String?): String? {
    if (url.isNullOrBlank()) return null
    val regex = Regex(
        "(?:youtube(?:-nocookie)?\\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\\.be/)([^\"&?/ ]{11})"
    )
    return regex.find(url)?.groupValues?.get(1)
}