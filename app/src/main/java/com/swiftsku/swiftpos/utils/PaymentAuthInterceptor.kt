package com.swiftsku.swiftpos.utils


import com.apollographql.apollo3.api.http.HttpRequest
import com.apollographql.apollo3.api.http.HttpResponse
import com.apollographql.apollo3.network.http.HttpInterceptor
import com.apollographql.apollo3.network.http.HttpInterceptorChain
import com.swiftsku.swiftpos.domain.config.GetStoreConfigUseCase
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import javax.inject.Inject

/**
 * An [HttpInterceptor] that handles authentication
 *
 * This is provided as is and will most likely need to be adapted to different backend requirements.
 *
 * There is surprising amount of details that can differ between different implementation.
 *
 * This [AuthorizationInterceptor] assumes the tokens form a chain where each new token
 * is computed based on the previous token value and invalidates all previous ones.
 *
 * @param tokenProvider a [TokenProvider] that gets tokens from the preferences or from the network
 * @param maxSize The maximum number of links to keep. This is theoretically needed in case some requests are
 * **very** slow and the token has been refreshed (by other concurrent requests) multiple times
 * when the initial request receives the 401.
 *
 * In practice, this is very unlikely to happen and a max size of 1 should be enough for most
 * scenarios
 */
class PaymentAuthInterceptor @Inject constructor(
    private val getStoreConfigUseCase: GetStoreConfigUseCase
) : HttpInterceptor {

    override suspend fun intercept(
        request: HttpRequest,
        chain: HttpInterceptorChain
    ): HttpResponse {
        val storeConfig = DeviceIdentifier.getSerialNumber()?.let {
            val fetchStoreConfig = getStoreConfigUseCase(it)
            fetchStoreConfig
        }

        val newBuilder = request.newBuilder()
        storeConfig?.token?.let {
            newBuilder.addHeader("token", it)
        }
        storeConfig?.storeCode?.let {
            newBuilder.addHeader("store-code", it)
        }
        storeConfig?.posId?.let {
            newBuilder.addHeader("posid", it)
        }
        return chain.proceed(newBuilder.build())
    }
}