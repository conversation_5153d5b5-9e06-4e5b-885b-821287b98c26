package com.swiftsku.swiftpos.utils

import androidx.compose.ui.platform.ViewConfiguration
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp

class CustomViewConfiguration(
    private val viewConfiguration: android.view.ViewConfiguration
) : ViewConfiguration {
    override val longPressTimeoutMillis: Long
        get() = android.view.ViewConfiguration.getLongPressTimeout().toLong()

    override val doubleTapTimeoutMillis: Long
        get() = android.view.ViewConfiguration.getDoubleTapTimeout().toLong()

    override val doubleTapMinTimeMillis: Long
        get() = 40

    override val touchSlop: Float
        get() = viewConfiguration.scaledTouchSlop.toFloat()

    override val minimumTouchTargetSize: DpSize
        get() = DpSize(24.dp, 24.dp)
}