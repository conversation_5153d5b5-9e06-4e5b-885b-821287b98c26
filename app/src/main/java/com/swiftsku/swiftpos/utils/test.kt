package com.swiftsku.swiftpos.utils

import com.lambdaworks.codec.Base64
import com.lambdaworks.crypto.SCrypt
import com.lambdaworks.crypto.SCryptUtil
import java.io.UnsupportedEncodingException
import java.security.GeneralSecurityException


fun hashPassword(passwordString: String): String {
    val n = 16384
    val r = 8
    val p = 1




    return hash(passwordString, "".toByteArray(), n, r, p)
}

fun hash(password: String, salt: ByteArray, n: Int, r: Int, p: Int): String {
    return try {
        val derived = SCrypt.scrypt(password.toByteArray(charset("UTF-8")), salt, n, r, p, 64)
        String(Base64.encode(derived))
    } catch (e: Exception) {
        e.printStackTrace()
        return ""
    }
}


