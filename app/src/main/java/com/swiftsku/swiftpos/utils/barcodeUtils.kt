package com.swiftsku.swiftpos.utils

import android.graphics.Bitmap
import com.google.zxing.BarcodeFormat
import com.journeyapps.barcodescanner.BarcodeEncoder
import io.sentry.Sentry
import java.time.LocalDate
import java.time.format.DateTimeFormatter


fun upcEtoA(upcE: String): String {
    // Ensure a correct UPC-E code is provided
    if (upcE.length != 8) return "Invalid UPC-E code"

    val withoutLeadingZeroAndCheckDigit = upcE.substring(1, 7)
    val lastDigit = withoutLeadingZeroAndCheckDigit[5]

    var upcA = withoutLeadingZeroAndCheckDigit.substring(0, 5)

    upcA = when (lastDigit) {
        '0', '1', '2' -> upcA.substring(0, 2) + lastDigit + "0000" + upcA.substring(2)
        '3' -> upcA.substring(0, 3) + "00000" + upcA.substring(3)
        '4' -> upcA.substring(0, 4) + "00000" + upcA.substring(4)
        else -> upcA + "0000" + lastDigit
    }

    // Adding a leading zero to make it UPC-A 11 digits long
    upcA = "0" + upcA

    // Calculating the check digit and appending it
    upcA += calculateCheckDigit(upcA)

    return upcA
}

// This function calculates the check digit for UPC
fun calculateCheckDigit(upc: String): Char {
    var evenSum = 0
    var oddSum = 0

    // Sum even and odd digit places considering 0-based index
    for (i in upc.indices) {
        if (i % 2 == 0) {
            oddSum += Character.getNumericValue(upc[i])
        } else {
            evenSum += Character.getNumericValue(upc[i])
        }
    }

    // Calculate check digit
    val totalSum = oddSum * 3 + evenSum
    val modValue = totalSum % 10
    val checkDigit = if (modValue == 0) 0 else 10 - modValue

    return Character.forDigit(checkDigit, 10)
}


fun generateBarcodeBitmap(barcodeString: String, width: Int, height: Int): Bitmap? {
    return try {
        BarcodeEncoder().encodeBitmap(barcodeString, BarcodeFormat.CODE_128, width, height)
    } catch (ex: Exception) {
        EventUtils.recordException(ex)
        null
    }
}

/**
 * Barcode string on Dl has DCG column for the country, it can either be "USA" or "CAN"
 * DBB column is for the date of birth in MMDDCCYY format for USA and CCYYMMDD format for CAN
 */
fun parseDobFromPdf417(pdf417String: String): LocalDate? {
    val dcgCountry = "DCG([A-Z]{3})".toRegex().find(pdf417String)?.groupValues?.get(1)
    val dobRaw = "DBB(\\d{8})".toRegex().find(pdf417String)?.groupValues?.get(1)
    dobRaw?.let {
        val formatter = when (dcgCountry) {
            "USA" -> DateTimeFormatter.ofPattern("MMddyyyy") // MMDDCCYY for USA
            "CAN" -> DateTimeFormatter.ofPattern("yyyyMMdd") // CCYYMMDD for CAN
            else -> null
        }
        return try {
            formatter?.let { LocalDate.parse(dobRaw, it) }
        } catch (e: Exception) {
            // Handle the exception or log it
            null
        }
    }
    return null
}

fun isDlInput(barcode: String): Boolean {
    return barcode.indexOf("DBB") != -1 && barcode.indexOf("DCG") != -1
}