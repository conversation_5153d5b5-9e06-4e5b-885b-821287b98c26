package com.swiftsku.swiftpos.extension

import com.sunmi.printerx.api.CashDrawerApi
import com.sunmi.printerx.api.PrintResult
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

suspend fun CashDrawerApi.openAsync(): <PERSON><PERSON>an {
    return suspendCoroutine { continuation ->
        try {
            open(object : PrintResult() {
                override fun onResult(p0: Int, p1: String?) {
                    continuation.resumeWith(Result.success(true))
                }
            })
        } catch (e: Exception) {
            continuation.resumeWithException(e)
        }
    }
}