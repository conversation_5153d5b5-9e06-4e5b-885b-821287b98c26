package com.swiftsku.swiftpos.extension

import com.fdc.core.types.FDCMessageBaseType
import com.fdc.core.types.ServiceResponseBaseType

fun FDCMessageBaseType.isForCurrentDevice(
    workstationId: String,
): Boolean = this.workstationID == workstationId && this.applicationSender == applicationSender


fun ServiceResponseBaseType.isForCurrentDevice(
    workstationId: String,
): Boolean = this.workstationID == workstationId

fun ServiceResponseBaseType.isForCurrentDevice(
    workstationId: String,
    requestID: String,
): Boolean = this.workstationID == workstationId && this.requestID == requestID