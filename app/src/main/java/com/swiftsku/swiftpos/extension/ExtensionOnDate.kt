package com.swiftsku.swiftpos.extension

import android.icu.text.DateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Calendar
import java.util.Date

fun Date.toDate(): String = DateFormat.getDateInstance(DateFormat.SHORT).format(this)
fun Date.toTime(): String = DateFormat.getTimeInstance(DateFormat.SHORT).format(this)
fun Date.toDateTime(): String =
    DateFormat.getDateTimeInstance(DateFormat.SHORT, DateFormat.SHORT).format(this)


fun years(): List<String> {

    DateTimeFormatter.ISO_OFFSET_DATE_TIME

    val calendar = Calendar.getInstance()
    calendar.set(Calendar.YEAR, 2023)
    calendar.set(Calendar.MONTH, 0) // January (months are zero-based)
    calendar.set(Calendar.DAY_OF_MONTH, 1)
    val startDate = LocalDate.of(2023, 1, 1)
    val yearsBetween = ChronoUnit.YEARS.between(startDate, LocalDate.now())
    val startYear = startDate.year
    return (0..yearsBetween).map { (startYear + it.toInt()).toString() }
}


fun LocalTime.toTime(): String = DateTimeFormatter.ofPattern("HH:mm").format(this)
fun LocalTime.toEOYRequestTime(): String = DateTimeFormatter.ofPattern("HH:mm:ss").format(this)


fun LocalDate.toDate(): String = DateTimeFormatter.ofPattern("MM-dd-yyyy").format(this)

fun LocalDate.toEOYDate(): String = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(this)

fun Calendar.toLocalDate(): LocalDate =
    LocalDate.of(
        this.get(Calendar.YEAR),
        this.get(Calendar.MONTH) + 1,
        this.get(Calendar.DAY_OF_MONTH)
    )


fun LocalDate.toEODRequestDate(): String = DateTimeFormatter.ofPattern("yyMMdd").format(this)

fun dateTime(
    date: LocalDate,
    time: LocalTime
): Long = LocalDateTime
    .of(date, time)
    .atZone(ZoneId.systemDefault())
    .toInstant()
    .toEpochMilli()

fun getDateTimeInUtc(date: LocalDate, time: LocalTime): Long {
    return LocalDateTime
        .of(date, time)
        .atZone(ZoneOffset.UTC)
        .toInstant()
        .toEpochMilli()
}

fun LocalDateTime.toPOSTimeStamp(): String =
    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
        .format(this)