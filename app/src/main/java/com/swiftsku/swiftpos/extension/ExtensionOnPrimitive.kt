package com.swiftsku.swiftpos.extension

import android.util.Patterns
import androidx.compose.ui.text.capitalize
import androidx.compose.ui.text.intl.Locale
import com.lambdaworks.crypto.SCrypt
import com.swiftsku.swiftpos.utils.EventUtils
import java.util.Date
import com.swiftsku.swiftpos.utils.barcode.gs1utils.GTIN
import com.swiftsku.swiftpos.utils.upcEtoA
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.abs
import kotlin.math.roundToInt

fun Float.to2Decimal(): Float = (this * 100f).roundToInt() / 100f
fun Double.to2Decimal(): Double = (this * 100.0).roundToInt() / 100.0
fun Double.to3DecimalString(): String = String.format("%.3f", this)

fun Float.centsToDollar(): Float = this / 100f

fun Float.toDigitIntHack(): Int = (this.toString().toDouble() * 100.0).toInt()
fun Double.convertDollarToCentPrecisely(): Int {
    val bdAmount = BigDecimal.valueOf(this)
    val hundred = BigDecimal(100)
    val cents = bdAmount.multiply(hundred)
    // setScale(0, RoundingMode.HALF_EVEN) sets the scale to 0 (no decimal places)
    // and rounds the value using RoundingMode.HALF_EVEN (also known as "bankers' rounding").
    return cents.setScale(0, RoundingMode.HALF_EVEN).toInt()
}

fun Float.toDigitInt(): Int = (to2Decimal() * 100f).toInt()
fun String.isNumeric(): Boolean = this.toDoubleOrNull() != null
fun String.isEmail(): Boolean = Patterns.EMAIL_ADDRESS.matcher(this).matches()
fun String.sCryptHash(): String {
    val salt = ByteArray(0)
    val n = 16384
    val r = 8
    val p = 1
    return try {
        val derived = SCrypt.scrypt(this.toByteArray(charset("UTF-8")), salt, n, r, p, 64)
        String(com.lambdaworks.codec.Base64.encode(derived))
    } catch (e: Exception) {
        EventUtils.recordException(e)
        return ""
    }
}

fun Date.epochInSeconds(): Int = (this.time / 1000).toInt()

fun String.convertToDate(): String {

    val mm = this.subSequence(0, 2)
    val dd = this.subSequence(2, 4)
    val yyyy = this.substring(4)
    val yy = yyyy.substring(2)
    return "${yy}${mm}${dd}"
}

fun String.convertToEAN(): String {
    val barcodeToSearch: String = if (this.length == 8) {
        // upc e and convert to upc a
        if (this.startsWith("0")) {
            val upcA = upcEtoA(this)
            GTIN.toGTIN14(upcA)
        } else {
            GTIN.toGTIN14(this)
        }
    } else if (this.length == 12 || this.length == 13) {
        GTIN.toGTIN14(this)
    } else {
        this
    }
    return barcodeToSearch
}

fun String.convertToEANOrNull(): String? = try {
    this.convertToEAN()
} catch (e: Exception) {
    EventUtils.recordException(e)
    null
}

fun String.removeUnderscoreAndMakeAsTitle(): String =
    this.split("_").joinToString(separator = " ") { it.capitalize(Locale.current) }

fun Float.formattedAmount() = String.format("%.2f", this)

fun Float.toDollars() = "$${this.formattedAmount()}"

fun Double.toDollars() = "$${String.format("%.2f", this)}"

fun String?.orDefault(default: String) = this ?: default

fun Float.isNil() = this == 0f

fun Int?.orNil() = this ?: 0

fun Int?.toDollars(): Float = (this ?: 0) / 100f

fun Boolean?.isTrue() = this ?: false

fun Int.centsToDollarsString(): String {
    val numerical = (abs(this) / 100f).toDollars()
    return "${if (this < 0) "-" else ""}$numerical"
}
