package com.swiftsku.swiftpos.extension

const val TAIL_SCALE_APP_PACKAGE = "com.swiftsku.swiftscale"
const val TAIL_SCALE_VPN_START = "$TAIL_SCALE_APP_PACKAGE.CONNECT_VPN"


const val TAIL_SCALE_RECEIVER = "$TAIL_SCALE_APP_PACKAGE.IPNReceiver"

const val TAIL_SCALE_VPN_STOP = "$TAIL_SCALE_APP_PACKAGE.DISCONNECT_VPN"

const val TAIL_SCALE_VPN_STATUS =
    "$TAIL_SCALE_APP_PACKAGE.VPN_STATUS" // Boolean sanjay emit manju subscribe

const val TAIL_SCALE_VPN_GET_STATUS =
    "$TAIL_SCALE_APP_PACKAGE.GET_VPN_STATUS"  // sanjay subscribe manju emit


const val SG_REPLICATOR_STATUS = "com.swiftsku.swiftpos.SG_REPLICATOR_STATUS"
