package com.swiftsku.swiftpos.app

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import com.couchbase.lite.CouchbaseLite
import com.couchbase.lite.Database
import com.couchbase.lite.LogDomain
import com.couchbase.lite.LogFileConfigurationFactory
import com.couchbase.lite.LogLevel
import com.couchbase.lite.newConfig
import com.orhanobut.logger.AndroidLogAdapter
import com.orhanobut.logger.Logger
import com.posthog.android.PostHogAndroid
import com.posthog.android.PostHogAndroidConfig
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.HiltAndroidApp
import java.io.File
import java.util.EnumSet
import javax.inject.Inject

@HiltAndroidApp
class SwiftPOSApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    companion object {
        const val POSTHOG_API_KEY = "phc_u0oiY227xnnhJiYVI0xVRPTuQPDDMAAgCAEzISWcDTJ"
        const val POSTHOG_HOST = "https://us.i.posthog.com"
    }

    override fun onCreate() {
        super.onCreate()
        CouchbaseLite.init(this)
        Logger.addLogAdapter(AndroidLogAdapter())
        if (!BuildConfig.DEBUG) {
            initPostHog()
        }
        initSentry()
        WorkManager.getInstance(this).cancelUniqueWork("sync_worker")
        PaxPaymentService.startPaxLogging()
    }

    private fun initSentry() {
        val posSerialNo = DeviceIdentifier.getSerialNumber().orEmpty()
        EventUtils.setUserProperty(EventUtils.UserProp.SERIAL_NO, posSerialNo)
    }

    private fun initPostHog() {
        val config = PostHogAndroidConfig(
            apiKey = POSTHOG_API_KEY,
            host = POSTHOG_HOST
        ).apply {
            sessionReplay = true
            sessionReplayConfig.screenshot = true
            sessionReplayConfig.maskAllTextInputs = false
            sessionReplayConfig.maskAllImages = false
        }
        PostHogAndroid.setup(this, config)
    }

    private fun setCouchbaseLogs() {
        try {
            val log = Database.log
            log.console.level = LogLevel.DEBUG
            log.console.domains =
                EnumSet.of(LogDomain.DATABASE, LogDomain.REPLICATOR, LogDomain.NETWORK)
            val externalDir = getExternalFilesDir(null) // External storage, private to your app
            val logsDir = File(externalDir, "couchbase-logs")
            if (!logsDir.exists()) {
                logsDir.mkdirs()
            }
            log.file.let {
                it.config = LogFileConfigurationFactory.newConfig(
                    logsDir.absolutePath,
                    maxSize = 1024 * 1024, // 1MB
                    maxRotateCount = 5,
                    usePlainText = true,
                )
                it.level = LogLevel.DEBUG
            }
        } catch (ex: Exception) {
            EventUtils.recordException(ex)
        }
    }

    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
    }
}