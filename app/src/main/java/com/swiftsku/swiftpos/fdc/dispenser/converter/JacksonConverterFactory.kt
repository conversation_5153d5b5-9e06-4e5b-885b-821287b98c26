package com.swiftsku.swiftpos.fdc.dispenser.converter

import com.fasterxml.jackson.databind.ObjectMapper
import com.swiftsku.fdc.core.di.converter.ConverterFactory


class JacksonConverterFactory private constructor(private val mapper: ObjectMapper) :
    ConverterFactory {

    companion object {
        fun create(mapper: ObjectMapper): ConverterFactory = JacksonConverterFactory(mapper)
    }

    override suspend fun serialize(t: Any): String {
        return mapper.writeValueAsString(t)
    }

    override suspend fun deserialize(s: String): Any? {
        try {
            val jsonNode = mapper.readTree(s)
            if (!jsonNode.has("type")) {
                return null
            }
            val className = jsonNode.get("type").asText()
            if (className != null) {
                val json =
                    mapper.treeToValue(
                        jsonNode,
                        Class.forName(className)
                    )
                return json
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }
}