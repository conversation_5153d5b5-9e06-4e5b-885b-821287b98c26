package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.MenuIconButton
import com.swiftsku.swiftpos.ui.components.MiniTile
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel


@Composable
fun ColumnScope.MainHeader(
    modifier: Modifier = Modifier,
    totalSale: Float,
    customerCount: Int,
    onMenuClick: () -> Unit,
    dashboardVM: DashboardViewModel
) {
    val settings by dashboardVM.userSettings.collectAsState()
    val unreadNotes by dashboardVM.unreadNotes.collectAsState()
    val badgeCount = unreadNotes.size

    Card(shape = RoundedCornerShape(0)) {
        Row(
            modifier = modifier
                .weight(1f, true)
                .padding(start = 20.dp)
                .height(80.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {

                if (settings?.displayTotals.isTrue()) {
                    val formattedTotalSaleAmount = totalSale.toDollars()
                    MiniTile(
                        "Total Sales",
                        formattedTotalSaleAmount,
                        Modifier.padding(start = 15.dp, end = 15.dp)
                    )
                    MiniTile(
                        "Customer Count",
                        "$customerCount",
                        Modifier.padding(start = 15.dp, end = 15.dp)
                    )
                }

            }
            MenuIconButton(onClick = onMenuClick, badgeCount = badgeCount)
        }

    }
}