package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Red


@Composable
fun FuelPriceDiffDialog(
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
) {
    val uiState = dashboardVM.uiState.collectAsState().value
    val priceDiff = uiState.fuelPriceDiff
    Dialog(
        onDismissRequest = { dashboardVM.hideFuelPriceDiff() },
    ) {
        Surface(
            modifier = Modifier
                .padding(32.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(32.dp)
            ) {
                Text(
                    text = "Fuel Price Difference",
                    textAlign = TextAlign.Center,
                    fontSize = 32.sp
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Due to higher price for card payments, you'll be charged extra ${priceDiff.toDollars()}.\nDo you want to proceed?",
                    textAlign = TextAlign.Center,
                    fontSize = 24.sp

                )
                Spacer(modifier = Modifier.height(30.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(all = 16.dp)
                ) {
                    Button(
                        onClick = { dashboardVM.hideFuelPriceDiff() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            dashboardVM.updatePriceAndInitiateCard(fdcVM.fdcState.value)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        Text(text = "Okay")
                    }
                }
            }
        }
    }
}
