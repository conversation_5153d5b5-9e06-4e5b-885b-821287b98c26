package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite


@Composable
fun BatchCloseConfirmation(
    dashboardVM: DashboardViewModel
) {
    Dialog(
        onDismissRequest = { dashboardVM.hideBatchCloseConfirmation() },
    ) {
        Surface(
            modifier = Modifier
                .padding(20.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(modifier = Modifier.padding(20.dp)) {
                Text(
                    "Closing for the day?",
                    modifier = Modifier.fillMaxWidth(),
                    style = MaterialTheme.typography.h6,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Spacer(modifier = Modifier.height(24.dp))
                Text(
                    text = "Select Yes, if you are closing for the day and want to process the payments.\n\n" +
                            "Select No, if you are just closing the shift.",
                    color = MaterialTheme.colors.blackAndWhite
                )
                Spacer(modifier = Modifier.height(64.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1f))
                    OutlinedButton(
                        onClick = {
                            dashboardVM.hideBatchCloseConfirmation()
                            dashboardVM.onLogOutClick(skipBatchClose = true)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f),
                        border = BorderStroke(1.dp, Teal)
                    ) {
                        Text(text = "No\nJust close the shift", color = Teal)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            dashboardVM.hideBatchCloseConfirmation()
                            dashboardVM.closeDay(true)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f)
                    ) {
                        Text(text = "Yes\nProcess payments")
                    }
                }
            }
        }
    }
}