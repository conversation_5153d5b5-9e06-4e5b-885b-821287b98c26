package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun ColumnScope.CartHeader(
    modifier: Modifier = Modifier,
    quantity: Int = 0,
) {
    Card(shape = RoundedCornerShape(0)) {
        Row(
            modifier = modifier
                .weight(1f, true)
                .padding(start = 20.dp, end = 20.dp)
                .height(48.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Description",
                modifier = Modifier.weight(1f),
                color = MaterialTheme.colors.blackAndWhite
            )
            Text(
                text = "QTY ($quantity)", modifier = Modifier.weight(1f),
                color = MaterialTheme.colors.blackAndWhite,
                textAlign = TextAlign.Center
            )
            Text(
                text = "Amount", modifier = Modifier.weight(1f),
                color = MaterialTheme.colors.blackAndWhite,
                textAlign = TextAlign.End
            )

        }
    }

}