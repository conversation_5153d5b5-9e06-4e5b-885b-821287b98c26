package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.material.Text
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.ui.components.CenteredComposable

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColumnScope.ReportList(
    items: List<ReportListDetail>,
    loader: Boolean = false,
    onCurrentPageChange: (Int) -> Unit,
    onCardClick: (ReportListDetail) -> Unit
) {
    onCurrentPageChange(-1)
    if (loader) {
        CenteredComposable {
            CircularProgressIndicator()
        }
    } else {
        if (items.isEmpty()) {
            CenteredComposable {
                Text(
                    text = "No data found",
                )
            }
        }
        else {
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .background(color = Color.White)
            ) {
                items.forEachIndexed { index, item ->
                    item {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                                .let { it.clickable { onCardClick(item) } },
                            elevation = 8.dp,
                        ) {
                            // Display a box card with title and subtitle
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = 8.dp, vertical = 8.dp),
                                contentAlignment = Alignment.CenterStart
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.Start
                                ) {
                                    Text(
                                        text = item.title,
                                        style = MaterialTheme.typography.subtitle1,
                                        color = Color.Black,
                                        textAlign = TextAlign.Center,
                                        maxLines = 1,
                                        overflow = TextOverflow.Visible,
                                        modifier = Modifier.padding(
                                            top = 8.dp,
                                            bottom = 8.dp,
                                            start = 4.dp,
                                            end = 4.dp
                                        ),
                                        fontSize = 14.sp,
                                    )
                                    Text(
                                        text = item.subtitle,
                                        style = MaterialTheme.typography.subtitle2,
                                        color = Color.DarkGray,
                                        textAlign = TextAlign.Center,
                                        maxLines = 1,
                                        overflow = TextOverflow.Visible,
                                        modifier = Modifier.padding(
                                            top = 8.dp,
                                            bottom = 8.dp,
                                            start = 4.dp,
                                            end = 4.dp
                                        ),
                                        fontSize = 12.sp,
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            }
        }
    }
}
