package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.ui.components.ContainerBox
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import java.time.LocalDate


@Composable
fun HistoricalBatchDateFilterMenu(
    startDate: LocalDate?,
    endDate: LocalDate?,
    onDateSelect: (ReportDateResult) -> Unit,
) {
    Column {
        Row {
            ContainerBox(
                title = startDate?.toDate() ?: "Start Date",
                contentDescription = "Start Date",
                modifier = Modifier
                    .clickable {
                        onDateSelect(ReportDateResult.HistoricalBatchStartDate)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(200.dp),
                icon = {
                    Icon(
                        Icons.Rounded.DateRange,
                        contentDescription = "Start Date",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Row {
            ContainerBox(
                title = endDate?.toDate() ?: "End Date",
                contentDescription = "End Date",
                modifier = Modifier
                    .clickable {
                        onDateSelect(ReportDateResult.HistoricalBatchEndDate)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(200.dp),
                icon = {
                    Icon(
                        Icons.Rounded.DateRange,
                        contentDescription = "End Date",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
    }
}

