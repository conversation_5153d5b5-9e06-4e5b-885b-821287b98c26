package com.swiftsku.swiftpos.ui.dashboard.txnhistory


import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccessTime
import androidx.compose.material.icons.rounded.FilterAltOff
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.extension.toTime
import com.swiftsku.swiftpos.ui.components.SearchBar
import com.swiftsku.swiftpos.ui.components.SelectorBox
import com.swiftsku.swiftpos.ui.theme.GrayBackground

@Composable
fun TxnFilter(
    query: String,
    onQueryChange: (String) -> Unit,
    onDebounceQueryChange: (String) -> Unit,
    txnFilterState: TransactionHistoryUiState,
    onTimeClick: (TimePickerType) -> Unit,
    onDateClick: (DatePickerType) -> Unit,
    onClearFilter: () -> Unit,
) {

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 16.dp)
    ) {


        SearchBar(
            query = query,
            onQueryChange = onQueryChange,
            onDebounceQueryChange = onDebounceQueryChange,
        )


        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                SelectorBox(
                    title = txnFilterState.selectedStartDate.toDate(),
                    modifier = Modifier
                        .clickable { onDateClick(DatePickerType.Start) }
                        .background(GrayBackground)
                        .padding(8.dp)
                        .width(115.dp),
                    icon = {
                        Icon(
                            Icons.Rounded.AccessTime,
                            contentDescription = "Start Date",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
                Spacer(modifier = Modifier.height(8.dp))
                SelectorBox(
                    title = txnFilterState.selectedEndDate.toDate(),
                    contentDescription = "End Date",
                    modifier = Modifier
                        .clickable { onDateClick(DatePickerType.End) }
                        .background(GrayBackground)
                        .padding(8.dp)
                        .width(115.dp),
                    icon = {
                        Icon(
                            Icons.Rounded.AccessTime,
                            contentDescription = "End Date",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }
            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                SelectorBox(
                    title = txnFilterState.selectedStartTime.toTime(),
                    contentDescription = "Start Time",
                    modifier = Modifier
                        .clickable { onTimeClick(TimePickerType.Start) }
                        .background(GrayBackground)
                        .padding(8.dp)
                        .width(115.dp),
                    icon = {
                        Icon(
                            Icons.Rounded.AccessTime,
                            contentDescription = "Start Time",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
                Spacer(modifier = Modifier.height(8.dp))
                SelectorBox(
                    title = txnFilterState.selectedEndTime.toTime(),
                    contentDescription = "End Time",
                    modifier = Modifier
                        .clickable { onTimeClick(TimePickerType.End) }
                        .background(GrayBackground)
                        .padding(8.dp)
                        .width(115.dp),
                    icon = {
                        Icon(
                            Icons.Rounded.AccessTime,
                            contentDescription = "End Time",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }

            Spacer(modifier = Modifier.width(4.dp))
            IconButton(
                onClick = onClearFilter
            ) {
                Icon(
                    Icons.Rounded.FilterAltOff,
                    contentDescription = "Clear Filter"
                )
            }
        }

    }

}