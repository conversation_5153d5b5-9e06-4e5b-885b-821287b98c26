package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.ui.components.SearchBar
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Grey_5F


@Composable
fun AccountSelectionDialog(
    dashboardVM: DashboardViewModel, creditsVM: CreditsViewModel
) {
    val creditAccountsDoc = creditsVM.creditAccountsDoc.collectAsState().value
    var searchQuery by remember { mutableStateOf("") }

    val filteredAccounts = creditAccountsDoc?.creditAccounts.orEmpty().values.filter { account ->
        val name = account.name.lowercase()
        val phone = account.phone.lowercase()
        name.contains(searchQuery) || phone.contains(searchQuery)
    }

    fun dismissDialog() {
        dashboardVM.dismissAccountSelectionDialog()
    }

    Dialog(
        onDismissRequest = { dismissDialog() },
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .width(800.dp)
                .heightIn(max = 550.dp)
        ) {
            Column {
                TopAppBar(
                    elevation = 4.dp,
                    title = { Text("Select Credit Account") },
                    actions = {
                        IconButton(onClick = { dismissDialog() }) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    })
                Column(
                    Modifier
                        .background(Grey_5F)
                        .padding(8.dp)
                ) {
                    Spacer(Modifier.height(8.dp))
                    Column(
                        Modifier
                            .background(Color.White, RoundedCornerShape(8.dp))
                            .heightIn(min = 200.dp),
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            SearchBar(query = searchQuery,
                                onQueryChange = { searchQuery = it },
                                modifier = Modifier.weight(1f),
                                searchHint = "Search by account name or phone",
                                onDebounceQueryChange = { searchQuery = it })
                        }
                        AccountsHeader()
                        Divider()
                        if (filteredAccounts.isNotEmpty()) {
                            LazyColumn {
                                itemsIndexed(
                                    items = filteredAccounts,
                                    key = { _, item -> item.id }) { index, account ->
                                    AccountRow(
                                        creditAccount = account,
                                        background = if (index % 2 == 0) GrayBackground else Color.Transparent,
                                        onClick = { dashboardVM.selectCreditAccount(account) }
                                    )
                                }
                            }
                        } else {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "No accounts found.",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.align(Alignment.CenterHorizontally)
                            )
                        }
                        Spacer(modifier = Modifier.height(32.dp))
                    }
                }
            }
        }
    }
}
