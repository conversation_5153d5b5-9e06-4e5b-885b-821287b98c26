package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Tab
import androidx.compose.material.TabRow
import androidx.compose.material.TabRowDefaults
import androidx.compose.material.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.swiftsku.swiftpos.data.model.ReportTabLayout
import com.swiftsku.swiftpos.data.model.title
import com.swiftsku.swiftpos.ui.components.ReportDetail
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.TextCaption
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.ui.theme.blackAndWhiteReverse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ReportTabLayoutPager(
    modifier: Modifier = Modifier,
    details: List<ReportTabLayout>,
    scope: CoroutineScope = rememberCoroutineScope(),
    pagerState: PagerState
) {
    Column(modifier = modifier) {
        TabRow(
            selectedTabIndex = pagerState.currentPage,
            backgroundColor = Color.White,
            indicator = { tabPositions ->
                TabRowDefaults.Indicator(
                    Modifier
                        .tabIndicatorOffset(tabPositions[pagerState.currentPage]), color = Teal
                )
            }
        ) {
            details.forEachIndexed { index, detail ->
                Tab(
                    selected = pagerState.currentPage == index,
                    onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                    text = {
                        Text(text = detail.title())
                    },
                    selectedContentColor = MaterialTheme.colors.blackAndWhiteReverse,

                    unselectedContentColor = TextCaption,
                    modifier = Modifier.background(if (pagerState.currentPage == index) MaterialTheme.colors.blackAndWhite else MaterialTheme.colors.blackAndWhiteReverse)

                )
            }
        }
        HorizontalPager(
            verticalAlignment = Alignment.Top,
            state = pagerState,
            modifier = modifier
                .background(MaterialTheme.colors.blackAndWhiteReverse)
        ) { page ->
            // Our page content
            ReportDetail(
                details = details[page].base64Report,
                modifier = modifier,
            )
        }
    }
}