package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccessTime
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportTimeResult
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.extension.toTime
import com.swiftsku.swiftpos.ui.components.ContainerBox
import com.swiftsku.swiftpos.ui.components.SelectorBox
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import java.time.LocalDate
import java.time.LocalTime

@Composable
fun HistoricalShiftFieldFilterMenu(
    terminals: List<String> = emptyList(),
    terminal: String?,
    cashiers: List<String> = emptyList(),
    cashierId: String?,
    onCashierSelect: (String) -> Unit,
    onTerminalSelect: (String) -> Unit
) {
    Row(
        modifier = Modifier
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.weight(1f))
        SelectorBox(
            modifier = Modifier.height(40.dp),
            items = cashiers,
            selectedItem = if (cashierId.isNullOrEmpty()) "All Cashiers" else cashierId,
            onSelectedItemChange = onCashierSelect,
            title = "Select Cashier"
        )
        Spacer(modifier = Modifier.width(8.dp))
        SelectorBox(
            modifier = Modifier.height(40.dp),
            items = terminals,
            selectedItem = if (terminal.isNullOrEmpty()) "All Terminals" else terminal,
            onSelectedItemChange = onTerminalSelect,
            title = "Select Terminal"
        )
    }
}

