package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.UID
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.extension.formattedAmount
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Purple_Light
import org.burnoutcrew.reorderable.ItemPosition
import org.burnoutcrew.reorderable.ReorderableItem
import org.burnoutcrew.reorderable.detectReorderAfterLongPress
import org.burnoutcrew.reorderable.rememberReorderableLazyGridState
import org.burnoutcrew.reorderable.reorderable


@Composable
fun MenuItemsSection(
    onDismiss: () -> Unit,
    selectedMenuKeyId: String,
    dashboardVM: DashboardViewModel
) {
    val loadings by dashboardVM.loadings.collectAsState()
    val allMenuItems by dashboardVM.menuItems.collectAsState()
    val menuKeys by dashboardVM.menuKeys.collectAsState()

    val selectedMenuKey = menuKeys.find { it.id == selectedMenuKeyId }

    if (selectedMenuKey == null) {
        return
    }
    val menuItems = selectedMenuKey.items.mapNotNull { subMenuId ->
        allMenuItems.find { subMenuId == "${it.pluId}-${it.pluModifier}" }
    }

    Surface(
        modifier = Modifier
            .fillMaxHeight()
            .fillMaxWidth()
            .clickable { onDismiss() },
        color = Color.Black.copy(alpha = 0.8f),
        elevation = 8.dp
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxHeight()
                    .fillMaxWidth(0.36f)
                    .background(Color.White)
                    .clickable(enabled = false) {}
            ) {
                TopAppBar(
                    elevation = 4.dp,
                    title = { Text(selectedMenuKey.name) },
                    actions = {
                        IconButton(onClick = { onDismiss() }) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    },
                    backgroundColor = Color.White,
                    contentColor = Color.Black
                )

                SubMenuSection(
                    Modifier
                        .padding(12.dp)
                        .fillMaxHeight(),
                    selectedMenuKey,
                    menuItems,
                    dashboardVM = dashboardVM
                )

                if (loadings.contains(DashboardViewModel.Loadings.MENU_ITEMS)) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .padding(top = 16.dp),
                    )
                } else {
                    if (menuItems.isEmpty()) {
                        Text(
                            "No items found.",
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SubMenuSection(
    modifier: Modifier = Modifier,
    menuKey: MenuKey,
    menuItems: List<PluItem>,
    dashboardVM: DashboardViewModel
) {

    var subMenu by remember { mutableStateOf<List<PluItem>>(emptyList()) }
    val txnItems by dashboardVM.txnItems.collectAsState()

    LaunchedEffect(menuItems) { subMenu = menuItems }

    fun isDragEnabled(draggedOver: ItemPosition, dragging: ItemPosition) = true

    val state = rememberReorderableLazyGridState(
        onMove = { from, to ->
            subMenu = subMenu.toMutableList().apply {
                val temp = this[from.index]
                this[from.index] = this[to.index]
                this[to.index] = temp
            }
        },
        onDragEnd = { _, _ ->
            dashboardVM.saveReorderedSubMenu(menuKey, subMenu)
        },
        canDragOver = ::isDragEnabled
    )

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        state = state.gridState,
        modifier = modifier.reorderable(state)
    ) {
        items(subMenu, { it.UID() }) { item ->
            ReorderableItem(state, item.UID()) { isDragging ->
                val elevation = animateDpAsState(if (isDragging) 8.dp else 0.dp, label = "")
                val qty = txnItems.find { txnItem ->
                    if (txnItem is TransactionItemWithPLUItem) {
                        txnItem.pluItem.UID() == item.UID() && txnItem.status != TransactionItemStatus.Deleted
                    } else false
                }?.quantity
                MenuItem(
                    pluItem = item,
                    qty = qty,
                    onItemClick = { dashboardVM.onPriceBookItemClick(it) },
                    modifier = Modifier
                        .detectReorderAfterLongPress(state)
                        .shadow(elevation.value)
                )
            }
        }
    }
}

@Composable
fun MenuItem(
    pluItem: PluItem,
    qty: Int?,
    onItemClick: (pluItem: PluItem) -> Unit,
    modifier: Modifier
) {
    Card(
        backgroundColor = Purple_Light,
        modifier = modifier
            .padding(4.dp)
            .fillMaxWidth()
            .height(80.dp)
            .clickable { onItemClick(pluItem) }
    ) {
        Column(Modifier.padding(start = 8.dp, end = 8.dp, top = 4.dp, bottom = 4.dp)) {
            val isMultiline = pluItem.description.take(33).length > 10
            Text(
                text = pluItem.description.take(33),
                style = MaterialTheme.typography.subtitle2,
                color = Color.White,
                maxLines = if (isMultiline) 2 else 1,
                overflow = TextOverflow.Ellipsis,
                fontSize = 16.sp,
                modifier = Modifier.padding(bottom = 2.dp)
            )
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "$${pluItem.price.formattedAmount()}",
                    style = MaterialTheme.typography.subtitle2,
                    color = Color.White,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 16.sp
                )
                qty?.let {
                    Box(
                        modifier = Modifier
                            .background(color = Color.White, shape = RoundedCornerShape(4.dp))
                            .padding(top = 2.dp, bottom = 2.dp, start = 4.dp, end = 4.dp)
                    ) {
                        Text(
                            text = "x${qty}",
                            style = MaterialTheme.typography.subtitle2,
                            color = Purple_Light,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

