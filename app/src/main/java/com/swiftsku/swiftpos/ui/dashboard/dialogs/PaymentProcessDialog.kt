package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.extension.isNil
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.modules.websocket.SocketState
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentResponseWS
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.theme.Orange
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.utils.INSUFFICIENT_FUNDS
import com.swiftsku.swiftpos.utils.MAX_ATTEMPTS_REACHED
import com.swiftsku.swiftpos.utils.MAX_WS_CONNECTION_RETRIES


/**
 * Payment Process Dialog should show the progress on the ongoing payment.
 * It should also handle the case when web sockets disconnects abruptly during the payment.
 * Before connection is established, it may be cancelled from the client side.
 */
@Composable
fun PaymentProcessDialog(
    onDismissRequest: () -> Unit,
    paymentResponse: PaymentResponseWS,
    dashboardVM: DashboardViewModel
) {
    val socketState by dashboardVM.paymentProcessor.socketState.collectAsState(SocketState(connecting = true))
    val uiState by dashboardVM.uiState.collectAsState()
    // Handle the case when disconnects while the payment in process i.e. completed = false, isConnected = false && connecting = false
    val pendingAmt = uiState.transactionSummary.pendingAmount()
    val firstTimeConnecting = socketState.connecting && socketState.connectAttempts <= 1
    val reconnecting = socketState.connecting && socketState.connectAttempts > 1
    val connectionAttemptExhausted = socketState.connectAttempts >= MAX_WS_CONNECTION_RETRIES
    val socketFailure = !(socketState.errorMessage.isNullOrBlank())
    val insufficientFunds = paymentResponse.ecode == INSUFFICIENT_FUNDS
    val cardBalanceCents = uiState.paymentResponseWS?.info?.cardBalanceCents ?: 0f
    val icon = when {
        reconnecting || paymentResponse.isFailure() || connectionAttemptExhausted || socketFailure -> R.drawable.ic_error_outline_24
        paymentResponse.isSuccess() -> R.drawable.ic_check_circle_outline_24
        else -> R.drawable.ic_hourglass_empty_24
    }
    val buttonTitle: String? = when {
        insufficientFunds -> null
        paymentResponse.isFailure() || connectionAttemptExhausted -> "OK"
        paymentResponse.isSuccess() && pendingAmt > 0.001 -> "Amount Due: ${pendingAmt.toDollars()}"
        paymentResponse.isSuccess() -> "Done"
        firstTimeConnecting -> "Cancel"
        else -> null
    }
    val buttonColor = when {
        paymentResponse.isFailure() || connectionAttemptExhausted -> Red
        paymentResponse.isSuccess() && pendingAmt > 0.001 -> Orange
        paymentResponse.isSuccess() -> Teal
        else -> Orange
    }
    val onButtonClick: () -> Unit = when {
        paymentResponse.isSuccess() || paymentResponse.isFailure() || connectionAttemptExhausted -> {
            { dashboardVM.clearPaymentStreamState() }
        }

        firstTimeConnecting -> {
            { dashboardVM.cancelPaymentStream() }
        }

        else -> {
            { }
        }
    }
    val message = when {
        insufficientFunds -> "Your card has a balance of ${(cardBalanceCents / 100f).toDollars()}. Would you like to use this amount and pay the rest with another method?"
        paymentResponse.ecode == MAX_ATTEMPTS_REACHED || connectionAttemptExhausted -> "Retries exhausted"
        reconnecting || socketFailure -> "Error: ${socketState.errorMessage}"
        else -> paymentResponse.msg
    }
    val title = when {
        insufficientFunds -> "Insufficient Funds"
        reconnecting || connectionAttemptExhausted || socketFailure -> "Connection Error"
        else -> paymentResponse.title
    }

    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Surface(
            modifier = Modifier
                .padding(20.dp)
                .clip(shape = RoundedCornerShape(8.dp))
                .width(400.dp)
                .wrapContentHeight(),
            elevation = 8.dp,
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .wrapContentSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    ImageVector.vectorResource(id = icon),
                    null,
                    tint = buttonColor,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = title.orEmpty(),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.h6,
                    color = MaterialTheme.colors.blackAndWhite,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Transaction ID: ${uiState.transactionId.orEmpty()}",
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text = message.orEmpty(),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite,
                    maxLines = 5,
                    modifier = Modifier.fillMaxWidth()
                )
                if (reconnecting) {
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = "Reconnecting",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.subtitle1,
                        color = MaterialTheme.colors.blackAndWhite,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                Spacer(modifier = Modifier.height(20.dp))
                buttonTitle?.let {
                    Button(
                        onClick = onButtonClick,
                        modifier = Modifier.height(48.dp),
                        colors = ButtonDefaults.buttonColors(backgroundColor = buttonColor)
                    ) {
                        Text(text = it, color = Color.White)
                    }
                }
                if (insufficientFunds && !cardBalanceCents.isNil()) {
                    Row(
                        horizontalArrangement = Arrangement.SpaceAround,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Button(
                            onClick = { dashboardVM.clearPaymentStreamState() },
                            modifier = Modifier
                                .height(48.dp)
                                .padding(end = 4.dp)
                                .requiredWidth(100.dp),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                        ) {
                            Text(text = "No", color = Color.White)
                        }
                        Button(
                            onClick = { dashboardVM.onEBTClick() },
                            modifier = Modifier
                                .height(48.dp)
                                .padding(start = 4.dp)
                                .requiredWidth(100.dp),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
                        ) {
                            Text(text = "Yes", color = Color.White)
                        }
                    }
                }
            }
        }
    }
}

