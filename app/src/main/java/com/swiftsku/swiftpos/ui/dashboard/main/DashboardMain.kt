package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ChainStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel


@Composable
fun RowScope.DashboardMain(
    modifier: Modifier = Modifier,
    onCardClick: () -> Unit,
    onEBTClick: () -> Unit,
    onNoSaleClick: () -> Unit,
    onRedeemClick: () -> Unit,
    onSaveClick: () -> Unit,
    onVoidClick: () -> Unit,
    onCashClick: () -> Unit,
    onLoyaltyClick: () -> Unit,
    onCashAmountClick: (amount: Float) -> Unit,
    onPriceBookItemClick: (item: PluItem) -> Unit,
    onPriceCheckClick: (Int) -> Unit,
    onRecallClick: (Int) -> Unit,
    onTxnHistoryClick: (Int) -> Unit,
    totalSale: Float,
    customerCount: Int,
    priceBookItems: List<PluItem>,
    departmentList: List<Department>,
    onDepartmentClick: (item: Department) -> Unit,
    transactionSummary: TransactionSummary,
    onMenuClick: () -> Unit,
    onPayoutClick: () -> Unit,
    onReportClick: (Int) -> Unit,
    onInfoClick: () -> Unit,
    fuelPumps: List<FuelPumpState>,
    onPumpClick: (FuelPumpState) -> Unit,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
    ) {
    val menuKeysList by dashboardVM.menuKeys.collectAsState()
    val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()
    val showMenuSection = menuKeysList.any { it.active }
    Column(
        modifier = modifier
            .weight(3.5f, true),
    ) {
        if (storeLevelConfig?.newDashboardUi == false) {
            MainHeader(
                totalSale = totalSale,
                customerCount = customerCount,
                onMenuClick = onMenuClick,
                dashboardVM = dashboardVM
            )
        }
        ConstraintLayout(
            modifier = Modifier.weight(1f)
        ) {
            val (fuelPumpsUi, deptMenu, payment, priceBook) = createRefs()
            createHorizontalChain(
                payment,
                priceBook,
                chainStyle = ChainStyle.Spread
            )

            FuelPumps(
                fuelPumps = fuelPumps,
                onPumpClick = onPumpClick,
                fdcVM = fdcVM,
                modifier = Modifier.constrainAs(fuelPumpsUi) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.preferredWrapContent
                }
            )

            Row(
                modifier = Modifier.constrainAs(deptMenu) {
                    top.linkTo(fuelPumpsUi.bottom)
                    bottom.linkTo(payment.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                },
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                DepartmentSection(
                    onDepartmentClick = onDepartmentClick,
                    departmentList = departmentList,
                    modifier = Modifier
                        .weight(5f)
                        .gesturesDisabled(
                            transactionSummary.totalAmountCollected() != 0f
                        ),
                    dashboardVM = dashboardVM
                )
                if (showMenuSection) {
                    MenuKeysSection(
                        modifier = Modifier
                            .weight(3f)
                            .gesturesDisabled(
                                transactionSummary.totalAmountCollected() != 0f
                            ),
                        dashboardVM = dashboardVM
                    )
                }
            }

            DashboardPayment(
                modifier = Modifier.constrainAs(payment) {
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom)
                    width = Dimension.percent(0.5f)
                    height = Dimension.wrapContent
                },
                onCardClick = onCardClick,
                onEBTClick = onEBTClick,
                onNoSaleClick = onNoSaleClick,
                onRedeemClick = onRedeemClick,
                onSaveClick = onSaveClick,
                onVoidClick = onVoidClick,
                onRefundClick = { dashboardVM.showRefundDialog() },
                onCashAmountClick = onCashAmountClick,
                onCashClick = onCashClick,
                onLoyaltyClick = onLoyaltyClick,
                transactionSummary = transactionSummary,
                onPayoutClick = onPayoutClick,
                dashboardVM = dashboardVM,
                fdcVM = fdcVM
            )
            DashboardPriceBook(
                modifier = Modifier.constrainAs(priceBook) {
                    top.linkTo(payment.top)
                    bottom.linkTo(payment.bottom)
                    end.linkTo(parent.end)
                    width = Dimension.percent(0.5f)
                    height = Dimension.fillToConstraints
                },
                onPriceBookItemClick = onPriceBookItemClick,
                priceBookItems = priceBookItems,
                dashboardVM = dashboardVM
            )
        }
        if (storeLevelConfig?.newDashboardUi.isTrue()) {
            MainFooterNew(
                onPriceCheckClick = onPriceCheckClick,
                onRecallClick = onRecallClick,
                onTxnHistoryClick = onTxnHistoryClick,
                onReportClick = onReportClick,
                onInfoClick = onInfoClick,
                onMenuClick = onMenuClick,
                totalSale = totalSale,
                customerCount = customerCount,
                dashboardVM = dashboardVM

            )
        } else {
            MainFooter(
                onPriceCheckClick = onPriceCheckClick,
                onRecallClick = onRecallClick,
                onTxnHistoryClick = onTxnHistoryClick,
                onReportClick = onReportClick,
                onInfoClick = onInfoClick
            )
        }
    }
}