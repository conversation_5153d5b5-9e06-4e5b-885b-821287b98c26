package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue_Dark
import org.burnoutcrew.reorderable.ItemPosition
import org.burnoutcrew.reorderable.ReorderableItem
import org.burnoutcrew.reorderable.detectReorderAfterLongPress
import org.burnoutcrew.reorderable.rememberReorderableLazyGridState
import org.burnoutcrew.reorderable.reorderable


@Composable
fun DepartmentSection(
    onDepartmentClick: (item: Department) -> Unit,
    departmentList: List<Department>,
    modifier: Modifier = Modifier,
    dashboardVM: DashboardViewModel
) {

    var departments by remember { mutableStateOf<List<Department>>(emptyList()) }

    LaunchedEffect(departmentList) { departments = departmentList }

    val trxItems by dashboardVM.txnItems.collectAsState()

    fun isDragEnabled(draggedOver: ItemPosition, dragging: ItemPosition) = trxItems.isEmpty()

    val state = rememberReorderableLazyGridState(
        onMove = { from, to ->
            departments = departments.toMutableList().apply {
                val temp = this[from.index]
                this[from.index] = this[to.index]
                this[to.index] = temp
            }
        },
        onDragEnd = { _, _ ->
            /**
             * Save departments in the new order, here "departments" contains only the departments
             * that has "showInDashboard" set to true.
             */
            dashboardVM.saveReorderedDepartments(departments)
        },
        canDragOver = ::isDragEnabled
    )

    LazyVerticalGrid(
        columns = GridCells.Fixed(5),
        state = state.gridState,
        modifier = modifier.reorderable(state)
    ) {
        items(departments.take(20), { it.departmentId }) { item ->
            if (trxItems.isEmpty()) {
                ReorderableItem(state, item.departmentId) { isDragging ->
                    val elevation = animateDpAsState(if (isDragging) 8.dp else 0.dp, label = "")
                    CardItem(
                        modifier = if (trxItems.isEmpty()) {
                            Modifier
                                .detectReorderAfterLongPress(state)
                                .shadow(elevation.value)
                        } else {
                            Modifier
                        },
                        title = item.departmentName,
                        backgroundColor = Blue_Dark,
                        onClick = { onDepartmentClick(item) },
                        isMultiline = true,
                        fontSize = 16.sp,
                        onDetailClick = { dashboardVM.onDeptDetailSelected(item) }
                    )
                }
            } else {
                CardItem(
                    title = item.departmentName,
                    backgroundColor = Blue_Dark,
                    onClick = { onDepartmentClick(item) },
                    onLongClick = {
                        dashboardVM.showToast(
                            "Please clear cart to reorder", ToastMessageType.Info
                        )
                    },
                    isMultiline = true,
                    fontSize = 16.sp,
                    onDetailClick = { dashboardVM.onDeptDetailSelected(item) }
                )
            }
        }
    }
}