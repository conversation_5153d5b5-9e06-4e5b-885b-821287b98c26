package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Teal

fun headerTitle(payout: List<LotteryPayout>): String {
    return if (payout.isEmpty()) {
        ""
    } else {
        val size = payout.size
        "$size ${if (size == 1) "Lottery" else "Lotteries"} applied"
    }
}

@Composable
fun LotteryHeader(
    dashboardVM: DashboardViewModel
) {
    val uiState by dashboardVM.uiState.collectAsState()
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(top = 20.dp, start = 30.dp, end = 30.dp, bottom = 10.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = headerTitle(uiState.payout.lotteryPayouts), style = MaterialTheme.typography.body1, color = Teal)
        ClickableText(
            text = AnnotatedString("Modify"),
            onClick = { dashboardVM.showLotteryDetails() },
            style = TextStyle(color = Blue)
        )
    }
}