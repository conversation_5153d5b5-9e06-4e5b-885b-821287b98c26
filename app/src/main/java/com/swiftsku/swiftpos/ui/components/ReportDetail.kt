package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.extension.base64ToBitmap

@Composable
fun ReportDetail(
    modifier: Modifier = Modifier,
    details: String
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
    ) {
        item {
            Image(
                bitmap = base64ToBitmap(details).asImageBitmap(),
                contentDescription = "Report",
                modifier= Modifier.fillMaxWidth()
            )
        }
    }
}


