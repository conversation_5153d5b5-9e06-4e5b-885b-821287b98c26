package com.swiftsku.swiftpos.ui.theme

import androidx.compose.material.Colors
import androidx.compose.ui.graphics.Color

val Purple = Color(0xFF513DA3)
val Purple_Light = Color(0xFF882AFF)
val Pink = Color(0xFFE84B8E)
val Blue = Color(0xFF2A85FF)
val Blue_Dark = Color(0xFF161F94)
val Teal = Color(0xFF009778)
val Red = Color(0xFFE53935)
val Background = Color(0xFFF6F6F6)
val Orange = Color(0xFFEF693F)
val Green = Color(0xFF00C566)
val TextTitle = Color(0xFF273D52)
val TextCaption = Color(0xFF5C5C5C)
val TransparentGrey = Color(0x305C5C5C)
val LitePurple = Color(0xff4E58DD)
val Grey_5F = Color(0xFFF5F5F5)
val Grey_D6 = Color(0xFFD6D6D6)

val DisabledBackground = Color(0xFFE8ECEF)
val GrayBackground = Color(0xFFF6F6F6)
val Colors.clickableTextColor: Color get() = if (isLight) TextCaption else Color.White
val Colors.blackAndWhite: Color get() = if (isLight) Color.Black else Color.White
val Colors.blackAndWhiteReverse: Color get() = if (!isLight) Color.Black else Color.White
