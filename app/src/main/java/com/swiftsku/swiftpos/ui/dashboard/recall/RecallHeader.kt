package com.swiftsku.swiftpos.ui.dashboard.recall

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.SwiftPOSTheme

@Composable
fun RecallHeader() {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(text = "Transaction Id.", modifier = Modifier.weight(1f))
        Text(text = "Label", modifier = Modifier.weight(1f))
        Text(text = "Time", modifier = Modifier.weight(1f))
        Text(text = "#Items", modifier = Modifier.weight(0.5f), textAlign = TextAlign.Center)
        Text(text = "Total amount", modifier = Modifier.weight(1f).padding(end = 32.dp), textAlign = TextAlign.End)
        Box(modifier = Modifier.weight(1.5f)) {

        }
    }
}

@Preview(showBackground = true, device = Devices.AUTOMOTIVE_1024p, widthDp = 1920, heightDp = 1080)
@Composable
fun DefaultPreview() {
    SwiftPOSTheme {
        RecallHeader()
    }
}