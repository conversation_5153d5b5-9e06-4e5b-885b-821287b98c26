package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.GrayBackground


@Composable
fun SelectorBox(
    modifier: Modifier = Modifier,
    items: List<String> = emptyList(),
    selectedItem: String,
    onSelectedItemChange: (String) -> Unit,
    title: String,
) {
    DropDownMenu(
        items = items,
        selectedItem = selectedItem,
        onSelectedMenuChange = onSelectedItemChange,
        content = {
            ContainerBox(
                title = it,
                contentDescription = title,
                modifier = modifier
                    .background(GrayBackground)
                    .padding(12.dp)
                    .width(115.dp)
            )
        },
        dropDownContent = {
            Text(text = it, modifier = Modifier.width(115.dp))
        }
    )
}

@Composable
fun SelectorBox(
    modifier: Modifier = Modifier,
    title: String,
    titleStyle: TextStyle = LocalTextStyle.current,
    contentDescription: String? = null,
    icon: @Composable () -> Unit = {
        Icon(
            Icons.Rounded.KeyboardArrowDown,
            contentDescription = contentDescription
        )
    }
) {
    Surface(shape = RoundedCornerShape(4.dp)) {
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = title, style = titleStyle)
            Spacer(modifier = Modifier.width(16.dp))
            icon()
        }
    }
}