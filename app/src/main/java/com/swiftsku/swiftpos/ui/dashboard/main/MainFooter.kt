package com.swiftsku.swiftpos.ui.dashboard.main

import android.icu.text.DateFormat
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.clickableTextColor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

import java.util.Date

@Composable
fun ColumnScope.MainFooter(
    modifier: Modifier = Modifier,
    onPriceCheckClick: (Int) -> Unit,
    onRecallClick: (Int) -> Unit,
    onTxnHistoryClick: (Int) -> Unit,
    onReportClick: (Int) -> Unit,
    onInfoClick: () -> Unit

) {
    Card(shape = RoundedCornerShape(0)) {
        Row(
            modifier = modifier
                .weight(1f, true)
                .padding(start = 20.dp, end = 20.dp)
                .height(80.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(3f),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ClickableText(
                    modifier = Modifier.testTag("footer_price_check"),
                    text = AnnotatedString("Price Check"),
                    onClick = onPriceCheckClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_recall"),
                    text = AnnotatedString("Recall"), onClick = onRecallClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_txn_history"),
                    text = AnnotatedString("Txn History"), onClick = onTxnHistoryClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_reports"),
                    text = AnnotatedString("Reports"), onClick = onReportClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_info"),
                    text = AnnotatedString("Info"), onClick = { onInfoClick() },
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
            }
            Row(
                modifier = Modifier.weight(1f)
            ) {
            }
            DateComponent()
        }

    }
}

@Composable
fun DateComponent() {
    var date by remember { mutableStateOf("") }
    LaunchedEffect(Unit) {
        while (true) {
            var formattedDate: String
            var timeFormat: String

            withContext(Dispatchers.IO) {
                val currentDate = Date()
                val dateFormat = DateFormat.getDateInstance(DateFormat.FULL)
                val timeInstance = DateFormat.getTimeInstance(DateFormat.SHORT)

                formattedDate = dateFormat.format(currentDate)
                timeFormat = timeInstance.format(currentDate)
            }

            date = "$timeFormat,  $formattedDate"
            delay(1000)
        }
    }
    Text(text = date)
}