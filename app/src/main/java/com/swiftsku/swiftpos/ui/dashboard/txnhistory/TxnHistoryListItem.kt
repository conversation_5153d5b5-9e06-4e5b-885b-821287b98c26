package com.swiftsku.swiftpos.ui.dashboard.txnhistory

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Orange
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun TxnHistoryItem(
    transactionId: String,
    time: String,
    itemsCount: String,
    totalAmount: String,
    onItemClick: () -> Unit,
    type: TransactionType?,
    hasFuel: Boolean
) {
    val typeColor = when (type) {
        TransactionType.Sale -> Teal

        TransactionType.Payout,
        TransactionType.CashWithdrawal,
        TransactionType.CashDeposit,
        TransactionType.AccountEntry -> Blue

        TransactionType.SaleRefund,
        TransactionType.Refund -> Red

        TransactionType.NoSale, TransactionType.Void -> Color.DarkGray
        else -> Blue
    }
    val typeLabel = when (type) {
        TransactionType.Sale -> "Sale"
        TransactionType.Payout -> "Payout"
        TransactionType.SaleRefund, TransactionType.Refund -> "Refund"
        TransactionType.NoSale -> "No Sale"
        TransactionType.Void -> "Void"
        TransactionType.CashWithdrawal -> "Safe Drop"
        TransactionType.CashDeposit -> "Safe Loan"
        TransactionType.AccountEntry -> "Ledger Entry"
        else -> type?.tender.orEmpty()
    }
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier

            .clickable {
                onItemClick()
            }
            .padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(
            text = transactionId,
            modifier = Modifier.weight(3f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = time, modifier = Modifier.weight(2f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = itemsCount, modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = totalAmount, modifier = Modifier.weight(2f).padding(end = 32.dp),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Row(modifier = Modifier.weight(2f)) {
            Text(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .background(typeColor)
                    .padding(start = 6.dp, top = 2.dp, end = 6.dp, bottom = 2.dp),
                fontWeight = FontWeight.Medium,
                text = typeLabel,
                style = MaterialTheme.typography.body2,
                color = Color.White
            )
            if (hasFuel) {
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .background(Orange)
                        .padding(start = 6.dp, top = 2.dp, end = 6.dp, bottom = 2.dp),
                    fontWeight = FontWeight.Medium,
                    text = "Fuel",
                    style = MaterialTheme.typography.body2,
                    color = Color.White
                )
            }
        }
        Icon(
            Icons.Filled.ArrowForward,
            null,
            modifier = Modifier.size(24.dp),
            tint = Color.DarkGray
        )
    }
}