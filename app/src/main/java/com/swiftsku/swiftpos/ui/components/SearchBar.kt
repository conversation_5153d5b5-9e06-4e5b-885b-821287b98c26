package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onDebounceQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    searchHint: String? = null,
    coroutineScope: CoroutineScope = rememberCoroutineScope()
) {

    var hasFocus by remember { mutableStateOf(false) }

    DisposableEffect(query, hasFocus) {
        val debounce = coroutineScope.launch {
            delay(500)
            if (hasFocus) {
                onDebounceQueryChange(query)
            }
        }
        onDispose { debounce.cancel() }
    }
    OutlinedTextField(
        modifier = modifier
            .padding(16.dp)
            .onFocusChanged { hasFocus = it.hasFocus },
        value = query,
        onValueChange = onQueryChange,
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(
                    onClick = {
                        onQueryChange("")
                    }
                ) {
                    Icon(Icons.Filled.Clear, "Clear")
                }
            }
        },
        leadingIcon = {
            Icon(Icons.Filled.Search, "Search")
        },
        placeholder = { Text(searchHint ?: "Search by transaction Id") },
        maxLines = 1,
        singleLine = true
    )
}