package com.swiftsku.swiftpos.ui.dashboard.txnhistory

import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.type.TransactionStatus
import java.time.LocalDate
import java.time.LocalTime

data class TransactionHistoryUiState(
    val transactions: List<Transaction> = emptyList(),
    val isLoading: Boolean = false,
    val isPaginating: Boolean = false,
    val showTimePicker: TimePickerType? = null,
    val showDatePicker: DatePickerType? = null,
    val endReached: Boolean = false,
    val query: String = "",
    val page: Int = 0,
    val limit: Int = 20,
    val selectedStartDate: LocalDate = LocalDate.now(),
    val selectedEndDate: LocalDate = LocalDate.now(),
    val selectedStartTime: LocalTime = LocalTime.of(0, 0),
    val selectedEndTime: LocalTime = LocalTime.of(23, 59),
    val txnStatus: TransactionStatus = TransactionStatus.Complete
)

sealed class TimePickerType {
    object Start : TimePickerType()
    object End : TimePickerType()
}

sealed class DatePickerType {
    object Start : DatePickerType()
    object End : DatePickerType()
}