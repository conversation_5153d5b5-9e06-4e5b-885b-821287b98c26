package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.DateRangePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDateRangePicker(
    state: DateRangePickerState,
    onDismissDialog: () -> Unit = {},
    onDateRangeAvailable: (selectedStartDate: LocalDate, selectedEndDate: LocalDate) -> Unit
) {
    Dialog(onDismissRequest = onDismissDialog) {
        Surface(
            modifier = Modifier.padding(vertical = 50.dp)
        ) {
            Column {
                DateRangePicker(
                    state = state,
                    modifier = Modifier.weight(1f)
                )
                Row(
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {

                    TextButton(onClick = onDismissDialog) {
                        Text(text = "Cancel")
                    }
                    Spacer(modifier = Modifier.size(8.dp))
                    TextButton(
                        onClick = {
                            val endDateMillis = state.selectedEndDateMillis
                            val startDateMillis = state.selectedStartDateMillis
                            if (startDateMillis != null && endDateMillis != null) {
                                onDateRangeAvailable(
                                    Instant
                                        .ofEpochMilli(startDateMillis)
                                        .atOffset(ZoneOffset.UTC)
                                        .toLocalDate(),
                                    Instant
                                        .ofEpochMilli(endDateMillis)
                                        .atOffset(ZoneOffset.UTC)
                                        .toLocalDate()
                                )
                                onDismissDialog()
                            }
                        }) {
                        Text(text = "Confirm")
                    }
                    Spacer(modifier = Modifier.size(8.dp))

                }
            }
        }
    }

//    DatePickerDialog(
//        properties = DialogProperties(
//            dismissOnBackPress = false,
//            dismissOnClickOutside = false
//        ),
//        onDismissRequest = onDismissDialog,
//        confirmButton = {
//            TextButton(onClick = {
//                val endDateMillis = state.selectedEndDateMillis
//                val startDateMillis = state.selectedStartDateMillis
//                if (startDateMillis != null && endDateMillis != null) {
//                    onDateRangeAvailable(
//                        Instant
//                            .ofEpochMilli(startDateMillis)
//                            .atOffset(ZoneOffset.UTC)
//                            .toLocalDate(),
//                        Instant
//                            .ofEpochMilli(endDateMillis)
//                            .atOffset(ZoneOffset.UTC)
//                            .toLocalDate()
//                    )
//                    onDismissDialog()
//
//                }
//            }) {
//                Text(text = "Confirm")
//            }
//        },
//        dismissButton = {
//            TextButton(
//                onClick = onDismissDialog
//            ) {
//                Text(text = "Cancel")
//            }
//        }
//    ) {
//        DateRangePicker(state = state)
//    }
}