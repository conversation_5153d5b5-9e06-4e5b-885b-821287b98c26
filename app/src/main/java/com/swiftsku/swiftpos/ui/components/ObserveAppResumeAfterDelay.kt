package com.swiftsku.swiftpos.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import java.util.concurrent.TimeUnit


@Composable
fun OnActivityResumedAfterDelay(
    delayMinutes: Long = 15, onResumeAfterDelay: () -> Unit
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    var backgroundTimestamp by remember { mutableStateOf<Long?>(null) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_STOP -> {
                    backgroundTimestamp = System.currentTimeMillis()
                }

                Lifecycle.Event.ON_RESUME -> {
                    val now = System.currentTimeMillis()
                    val last = backgroundTimestamp
                    val minutesElapsed = if (last != null) {
                        TimeUnit.MILLISECONDS.toMinutes(now - last)
                    } else 0

                    if (minutesElapsed >= delayMinutes) {
                        onResumeAfterDelay()
                    }
                    backgroundTimestamp = null
                }

                else -> Unit
            }
        }

        val lifecycle = lifecycleOwner.lifecycle
        lifecycle.addObserver(observer)

        onDispose {
            lifecycle.removeObserver(observer)
        }
    }
}
