package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ExposedDropdownMenuBox
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp


@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AutoCompleteTextField(
    label: String,
    queryResult: List<String> = emptyList(),
    onQueryChange: (String) -> Unit,
    modifier: Modifier,
    onResultSelect: (String) -> Unit

) {

    var expand by remember { mutableStateOf(false) }
    var queryValue by remember { mutableStateOf("") }
    var selectedValue by remember { mutableStateOf("") }

    ExposedDropdownMenuBox(
        modifier = modifier.fillMaxWidth(),
        expanded = expand,
        onExpandedChange = {
            expand = !expand
        }
    ) {
        TextField(
            value = selectedValue,
            onValueChange = {
                queryValue = it
                onQueryChange(queryValue)
                selectedValue = it
            },
            label = { Text(text = "Select $label") },
            modifier = Modifier.fillMaxWidth()
        )

        ExposedDropdownMenu(
            expanded = expand,
            onDismissRequest = { expand = false }
        ) {

            queryResult.forEach { item ->
                DropdownMenuItem(
                    onClick = {

                        queryValue = item
                        selectedValue = queryValue
                        onResultSelect(item)
                        expand = false
                    },
                    content = {
                        Text(text = item)
                    }
                )
            }
        }
    }
}