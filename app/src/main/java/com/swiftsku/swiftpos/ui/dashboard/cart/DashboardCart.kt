package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.LoyaltyState
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import kotlinx.coroutines.launch

@Composable
fun RowScope.DashboardCart(
    modifier: Modifier = Modifier,
    onDeleteClick: (item: TransactionItem) -> Unit,
    onEditClick: (item: TransactionItem) -> Unit,
    cartItems: List<TransactionItem>,
    transactionSummary: TransactionSummary,
    coupons: List<Coupon>,
    onCouponRemoveClick: () -> Unit,
    loyaltyState: LoyaltyState,
    onApplyEBTClick: (TransactionItem) -> Unit,
    allowEditingCart: Boolean,
    dashboardVM: DashboardViewModel
) {
    val uiState by dashboardVM.uiState.collectAsState()
    Column(
        modifier = modifier
            .weight(1.25f, true)
            .gesturesDisabled(!allowEditingCart)
    ) {
        CartHeader(
            quantity = cartItems.sumOf { it.quantity }
        )
        CartItems(
            onDeleteClick = onDeleteClick,
            onEditClick = if (allowEditingCart) onEditClick else { _ -> },
            cartItems = cartItems,
            onApplyEBTClick = onApplyEBTClick,
            dashboardVM = dashboardVM
        )
        if (uiState.availableOffers.isNotEmpty()) {
            OffersSection(uiState.availableOffers)
        }
        CartSummary(
            transactionSummary = transactionSummary,
            coupons = coupons,
            onCouponRemoveClick = onCouponRemoveClick,
            loyaltyState = loyaltyState,
            dashboardVM = dashboardVM
        )
    }
}

@Composable
fun ColumnScope.CartItems(
    modifier: Modifier = Modifier,
    onDeleteClick: (item: TransactionItem) -> Unit,
    onEditClick: (item: TransactionItem) -> Unit,
    cartItems: List<TransactionItem>,
    onApplyEBTClick: (TransactionItem) -> Unit,
    dashboardVM: DashboardViewModel
) {

    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(cartItems.size) {
        if (cartItems.size > 5) {
            coroutineScope.launch { listState.animateScrollToItem(cartItems.size - 1) }
        }
    }

    LazyColumn(
        modifier = modifier
            .weight(1f),
        state = listState
    ) {
        items(items = cartItems, key = { it.transactionItemId }) { item ->
            CartItem(
                item = item,
                onDeleteClick = { onDeleteClick(it) },
                onEditClick = { onEditClick(it) },
                onApplyEBTClick = { onApplyEBTClick(it) },
                dashboardVM = dashboardVM
            )
        }
    }
}


//@Preview(showBackground = true)
//@Composable
//fun DashboardCartPreview() {
//    SwiftPOSTheme {
//        Row(
//            horizontalArrangement = Arrangement.SpaceBetween,
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            DashboardCart()
//
//        }
//    }
//}