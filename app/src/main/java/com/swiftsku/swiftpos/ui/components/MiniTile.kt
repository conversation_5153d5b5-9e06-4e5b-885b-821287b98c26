package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun MiniTile(title: String, caption: String, modifier: Modifier) {
    Column(modifier = modifier) {
        Text(text = title)
        Text(
            text = caption,
            style = MaterialTheme.typography.h6,
            color = MaterialTheme.colors.blackAndWhite,
            modifier = Modifier.align(alignment = Alignment.CenterHorizontally)
        )
    }
}