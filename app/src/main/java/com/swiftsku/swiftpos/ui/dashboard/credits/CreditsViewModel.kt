package com.swiftsku.swiftpos.ui.dashboard.credits

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.couchbase.credit.CreditRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.model.AccountLedgerTxn
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.CreditAccountDoc
import com.swiftsku.swiftpos.data.model.CreditStats
import com.swiftsku.swiftpos.data.model.LedgerDirection
import com.swiftsku.swiftpos.data.model.LedgerEntry
import com.swiftsku.swiftpos.data.model.LedgerEntryData
import com.swiftsku.swiftpos.data.model.LedgerReason
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.convertToSaleTransaction
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.domain.transaction.GetFeesUseCase
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.orNil
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundOption
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject
import kotlin.math.abs


@HiltViewModel
class CreditsViewModel @Inject constructor(
    private val userDataSource: UserDataSource,
    private val creditRepository: CreditRepository,
    private val paymentUseCase: PaymentUseCase,
    private val paxPaymentService: PaxPaymentService,
) : ViewModel() {

    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    val toastMessage: StateFlow<ToastMessage?> = _toastMessage
    private val _terminalConfig = MutableStateFlow<StoreConfig?>(null)
    private val _storeConfig = MutableStateFlow<StoreUsers?>(null)
    private val _creditStats = MutableStateFlow<CreditStats?>(null)
    val creditStats = _creditStats.asStateFlow()
    private val _creditAccountsDoc = MutableStateFlow<CreditAccountDoc?>(null)
    val creditAccountsDoc = _creditAccountsDoc
    private val _selectedAccountId = MutableStateFlow<String?>(null)
    val selectedAccountId = _selectedAccountId
    private val _ledgerEntries = MutableStateFlow<Result<List<LedgerEntry>>?>(null)
    val ledgerEntries = _ledgerEntries
    private val _loadings = MutableStateFlow<MutableSet<Loadings>>(mutableSetOf())
    val loadings: StateFlow<Set<Loadings>> = _loadings

    enum class Loadings {
        PAYMENT_PROCESS
    }

    fun updateTerminalConfig(terminalConfig: StoreConfig) = viewModelScope.launch {
        _terminalConfig.emit(terminalConfig)
        creditRepository.getCreditAccount(terminalConfig.storeCode).collectLatest {
            val sortedAccounts =
                it?.creditAccounts?.values?.sortedByDescending { it.currentOutstandingCents }
            val totalOutstanding = sortedAccounts?.sumOf { it.currentOutstandingCents }
            val topOutstandingAccounts = sortedAccounts?.take(5)
            val creditStats = CreditStats(
                totalOutstanding.orNil(),
                topOutstandingAccounts.orEmpty()
            )
            _creditStats.emit(creditStats)
            _creditAccountsDoc.emit(it)
        }
    }

    fun updateStoreConfig(storeConfig: StoreUsers) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    fun showToast(message: String, type: ToastMessageType) = viewModelScope.launch {
        _toastMessage.emit(
            ToastMessage(message = message, type = type)
        )
    }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }

    private suspend fun cashierId() = userDataSource.getCurrentUser()?.username ?: "cashierId"

    fun saveCreditAccount(creditAccountDoc: CreditAccountDoc) = viewModelScope.launch {
        if (creditRepository.saveCreditAccounts(creditAccountDoc)) {
            showToast("Credit accounts updated", ToastMessageType.Success)
        } else {
            showToast("Failed to update credit accounts", ToastMessageType.Success)
        }
    }

    fun setSelectedAccount(account: CreditAccount) = viewModelScope.launch {
        _selectedAccountId.emit(account.id)
        getLedgerEntries(account)
    }

    private fun getLedgerEntries(account: CreditAccount) = viewModelScope.launch {
        _ledgerEntries.emit(Result.Loading)
        creditRepository.getLedgerEntries(account.id).collectLatest {
            _ledgerEntries.emit(Result.Success(it))
        }
    }

    fun clearSelectedAccount() = viewModelScope.launch {
        _selectedAccountId.emit(null)
        _ledgerEntries.emit(null)
    }

    fun createFundEntry(
        fundOption: FundOption,
        amountCents: Int,
        txnId: String,
        storeCode: String,
        notes: String?,
        cardInfo: CardInfo? = null,
        appliedFees: List<AppliedFee>? = emptyList(),
    ) = viewModelScope.launch {
        val fundMop = when (fundOption) {
            FundOption.CASH -> TxnPaymentType.Cash
            FundOption.CHECK -> TxnPaymentType.Cheque
            FundOption.CARD -> TxnPaymentType.Card
            else -> null
        }
        val ledgerReason = when (fundOption) {
            FundOption.CASH, FundOption.CARD, FundOption.CHECK -> LedgerReason.ADD_FUNDS
            FundOption.ADJUSTMENT -> LedgerReason.ADJUSTMENT
        }
        val ledgerDirection = when (fundOption) {
            FundOption.CASH, FundOption.CARD, FundOption.CHECK -> LedgerDirection.CREDIT
            FundOption.ADJUSTMENT -> if (amountCents > 0) LedgerDirection.CREDIT else LedgerDirection.DEBIT
        }
        _creditAccountsDoc.value?.let { creditAccountDoc ->
            val selectedId = _selectedAccountId.value
            val originalAccount = creditAccountDoc.creditAccounts[selectedId]

            if (originalAccount != null && selectedId != null) {
                val newOutstandingCents = originalAccount.currentOutstandingCents.minus(amountCents)
                val now = System.currentTimeMillis()

                val updatedAccount = originalAccount.copy(
                    currentOutstandingCents = newOutstandingCents,
                    updatedAt = now
                )

                val updatedAccounts = creditAccountDoc.creditAccounts.toMutableMap()
                updatedAccounts[selectedId] = updatedAccount

                val updatedCreditAccountDoc =
                    creditAccountDoc.copy(creditAccounts = updatedAccounts)

                val ledgerEntry = LedgerEntry(
                    txnId = txnId,
                    accountId = updatedAccount.id,
                    linkedTxnId = txnId,
                    txnTime = System.currentTimeMillis(),
                    cashierId = cashierId(),
                    ledgerReason = ledgerReason,
                    ledgerDirection = ledgerDirection,
                    fundMop = fundMop,
                    notes = notes,
                    amountCents = abs(amountCents),
                    newOutstandingCents = newOutstandingCents,
                    storeCode = storeCode,
                    cardInfo = cardInfo,
                    appliedFees = appliedFees
                )

                val saved = creditRepository.createLedgerEntry(
                    creditAccount = updatedAccount,
                    creditAccountDoc = updatedCreditAccountDoc,
                    ledgerEntry = ledgerEntry
                )
                if (saved) {
                    showToast("Saved successfully", ToastMessageType.Success)
                }
            }
        }
    }

    fun processCardPayment(amountCents: Int, notes: String) = viewModelScope.launch {
        _creditAccountsDoc.value?.let { creditAccountDoc ->
            val selectedId = _selectedAccountId.value
            val originalAccount = creditAccountDoc.creditAccounts[selectedId]

            if (originalAccount != null && selectedId != null) {
                if (_loadings.value.contains(Loadings.PAYMENT_PROCESS)) {
                    return@launch
                }
                _loadings.update { it.toMutableSet().apply { add(Loadings.PAYMENT_PROCESS) } }
                val storeConfig = _storeConfig.value
                val terminalConfig = _terminalConfig.value
                val storeCode = terminalConfig?.storeCode.orEmpty()
                val posNo = terminalConfig?.posNumber.orEmpty()
                val txnId = generateTransactionId(storeCode = storeCode, posNumber = posNo)

                val amount = (amountCents / 100f)

                val cardFee = GetFeesUseCase.getCardProcessingFee(amount, storeConfig?.feeConfig)
                val appliedFees = mutableListOf<AppliedFee>()
                cardFee?.let { appliedFees.add(it) }
                val fees = GetFeesUseCase.getTotalAppliedFee(appliedFees)
                val totalAmount = (amount.plus(fees))
                val totalAmountCents = totalAmount.toDouble().convertDollarToCentPrecisely()
                val newOutstandingCents = originalAccount.currentOutstandingCents.minus(amountCents)

                val accountLedgerTxn = AccountLedgerTxn(
                    txnId = txnId,
                    txnStartTime = Date(),
                    txnStatus = TransactionStatus.Pending,
                    statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.Pending),
                    cashierId = cashierId(),
                    data = LedgerEntryData(
                        accountId = originalAccount.id,
                        accountName = originalAccount.name,
                        amountCents = abs(totalAmountCents),
                        entryType = LedgerReason.ADD_FUNDS,
                        fundMop = TxnPaymentType.Card,
                        creditLimitCents = originalAccount.creditLimitCents,
                        newOutstandingCents = newOutstandingCents,
                        notes = notes
                    )
                )

                val txnSummary = TransactionSummary(
                    transactionTotalNetAmount = totalAmount,
                    transactionTotalGrandAmount = totalAmount,
                    transactionTotalGrossAmount = totalAmount
                )

                val paxPaymentInput = PaxPaymentInput(
                    txnId,
                    paxPaymentService.getCurrentBatchId(),
                    totalAmountCents.toString(),
                    cashierId(),
                    Date().epochInSeconds()
                )
                paymentUseCase.processEpxCreditRequest(
                    paxPaymentInput,
                    convertToSaleTransaction(accountLedgerTxn, txnSummary)
                ).stateIn(viewModelScope).collect { result ->
                    when (result) {
                        is Result.Loading -> {}
                        is Result.Error -> {
                            _loadings.update {
                                it.toMutableSet().apply { remove(Loadings.PAYMENT_PROCESS) }
                            }
                            _toastMessage.emit(
                                ToastMessage(
                                    result.errorMessage,
                                    ToastMessageType.Success
                                )
                            )
                        }

                        is Result.Success -> {
                            result.data?.let {
                                val cardInfo = CardInfo(
                                    merchantId = it.hostInformation?.paymentAccountReferenceId.orEmpty(),
                                    approvalCode = it.hostInformation?.authorizationCode.orEmpty(),
                                    logo = it.accountInformation?.cardType?.name.orEmpty(),
                                    transactionId = txnId,
                                    account = it.accountInformation?.account.orEmpty(),
                                    entry = it.accountInformation?.entryMode?.name.orEmpty(),
                                    label = it.paymentEmvTag?.appLabel.orEmpty(),
                                )
                                _toastMessage.emit(
                                    ToastMessage(
                                        it.responseMessage ?: "Payment Successful",
                                        ToastMessageType.Success
                                    )
                                )
                                _loadings.update {
                                    it.toMutableSet().apply { remove(Loadings.PAYMENT_PROCESS) }
                                }
                                createFundEntry(
                                    FundOption.CARD,
                                    amountCents,
                                    txnId,
                                    storeCode,
                                    notes,
                                    cardInfo,
                                    appliedFees
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}