package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material3.DatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.dialog
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserType
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.data.type.reportDefaults
import com.swiftsku.swiftpos.ui.components.AppDatePicker
import com.swiftsku.swiftpos.ui.components.TimePickerDialog
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.TransparentGrey
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.ui.theme.blackAndWhiteReverse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.util.Date


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportDialog(
    reportVM: ReportViewModel,
    datePickerState: DatePickerState = rememberDatePickerState(
        initialSelectedDateMillis = Instant.now().toEpochMilli()
    ),
    timePickerState: TimePickerState = rememberTimePickerState(is24Hour = true),
    coroutineScope: CoroutineScope,
    onDismissDialog: () -> Unit
) {


    val uiState by reportVM.uiState.collectAsState()

    LaunchedEffect(Unit) {
        reportVM.reset()
        reportVM.initReportVM()
    }

    Box(
        contentAlignment = Alignment.CenterEnd,
        modifier = Modifier
            .semantics { dialog() }
            .fillMaxSize(),
    ) {

        Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(TransparentGrey)
                    .zIndex(1f)
                    .weight(2f)
                    .clickable(enabled = false, onClick = {})
            )
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxSize()
                    .background(Color.White)
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxSize()
                ) {
                    var reportTypes = reportDefaults
                    if (uiState.currentUser?.userType != UserType.OWNER.value) {
                        reportTypes = reportTypes.filter {
                            it != ReportType.HistoricalShift && it != ReportType.HistoricalBatch
                        }
                    }
                    ReportHeader(
                        onReportTypeChange = { reportVM.onReportTypeChange(it) },
                        reportUIState = uiState,
                        onMonthChange = { reportVM.onSelectedMonthChange(it) },
                        onYearChange = { reportVM.onSelectedYearChange(it) },
                        onDateSelect = { reportVM.selectDate(it) },
                        onTimeSelect = { reportVM.selectTime(it) },
                        onPresetSelect = { reportVM.selectPreset(it) },
                        onHistoricalShiftCashierSelect = { reportVM.selectHistoricalShiftCashier(it) },
                        onHistoricalShiftTerminalSelect = {
                            reportVM.selectHistoricalShiftTerminal(it)
                        },
                        onHistoricalBatchTerminalSelect = {
                            reportVM.selectHistoricalBatchTerminal(it)
                        },
                        items = reportTypes
                    )


                    Surface(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxSize()
                            .background(GrayBackground)
                    ) {
                        when (uiState.selectedReportType) {
                            ReportType.AdHoc -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onAdHocPageChange(it)
                                    },
                                    loader = uiState.adHocState.loader,
                                    stats = uiState.adHocState.adHocReport?.stats ?: emptyList(),
                                )
                            }

                            ReportType.EOD -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onEODPageChange(it)
                                    },
                                    loader = uiState.eodState.loader,
                                    bmp = uiState.eodState.eodReport?.bmp ?: emptyMap(),
                                )
                            }

                            ReportType.EOM -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onEOMPageChange(it)
                                    },
                                    loader = uiState.eomState.loader,
                                    bmp = uiState.eomState.eomReport?.bmp ?: emptyMap(),
                                )
                            }

                            ReportType.EOY -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onEOYPageChange(it)
                                    },
                                    loader = uiState.eoyState.loader,
                                    stats = uiState.eoyState.eoyReport?.stats ?: emptyList(),
                                )
                            }

                            ReportType.Shift -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onShiftPageChange(it)
                                    },
                                    loader = uiState.shiftState.loader,
                                    stats = uiState.shiftState.shiftReport?.stats ?: emptyList(),
                                )
                            }

                            ReportType.Preset -> {
                                ReportBody(
                                    onCurrentPageChange = {
                                        reportVM.onPresetPageChange(it)
                                    },
                                    loader = uiState.presetState.loader,
                                    stats = uiState.presetState.presetReport?.stats ?: emptyList(),
                                )
                            }

                            ReportType.HistoricalShift -> {
                                if (uiState.historicalShiftState.viewingListScreen) {
                                    ReportList(
                                        onCurrentPageChange = {
                                            reportVM.onHistoricalShiftPageChange(it)
                                        },
                                        loader = uiState.historicalShiftState.shiftListLoader,
                                        items = uiState.historicalShiftState.historicalShifts.map { it.buildListView() },
                                        onCardClick = { reportVM.selectHistoricalShift(it) }
                                    )
                                } else {
                                    ReportBodyWithBackButton(
                                        onCurrentPageChange = {
                                            reportVM.onHistoricalShiftPageChange(it)
                                        },
                                        loader = uiState.historicalShiftState.selectedShiftLoader,
                                        stats = uiState.historicalShiftState.selectedHistoricalShiftReport?.stats
                                            ?: emptyList(),
                                        onBackClick = { reportVM.returnToHistoricalShiftList() }
                                    )
                                }
                            }

                            ReportType.HistoricalBatch -> {
                                if (uiState.historicalBatchState.viewingListScreen) {
                                    ReportList(
                                        onCurrentPageChange = {
                                            reportVM.onHistoricalBatchPageChange(it)
                                        },
                                        loader = uiState.historicalBatchState.batchListLoader,
                                        items = uiState.historicalBatchState.historicalBatches.map { it.buildListView() },
                                        onCardClick = { reportVM.selectHistoricalBatch(it) }
                                    )
                                } else {
                                    ReportBodyWithBackButton(
                                        onCurrentPageChange = {
                                            reportVM.onHistoricalBatchPageChange(it)
                                        },
                                        loader = uiState.historicalBatchState.selectedBatchLoader,
                                        stats = uiState.historicalBatchState.selectedHistoricalBatchReport?.stats ?: emptyList(),
                                        onBackClick = { reportVM.returnToHistoricalBatchList() }
                                    )
                                }
                            }
                        }
                    }

                    Footer(
                        onCancelClick = onDismissDialog,
                        onPrintClick = { reportVM.onPrintClick() },
                        showPrintButton = !(uiState.selectedReportType == ReportType.HistoricalShift && uiState.historicalShiftState.viewingListScreen)
                                && !(uiState.selectedReportType == ReportType.HistoricalBatch && uiState.historicalBatchState.viewingListScreen)
                    )
                }
            }
        }

        if (uiState.selectTime != null) {
            TimePickerDialog(
                onCancel = {
                    reportVM.dismissTimePicker()

                },
                onConfirm = {
                    timePickerState.let {
                        reportVM.onTimeSelected(
                            LocalTime.of(it.hour, it.minute),
                            uiState.selectTime!!
                        )
                        reportVM.dismissTimePicker()
                        coroutineScope.launch {
                            timePickerState.settle()
                        }
                    }
                }
            ) {
                TimePicker(state = timePickerState)
            }
        }

        if (uiState.selectDate != null) {
            AppDatePicker(
                onDateChange = { selectedDate: LocalDate, reportDateResult: ReportDateResult ->
                    reportVM.onDateSelected(selectedDate, reportDateResult)
                },
                onDismissDialog = {
                    reportVM.dismissDatePicker()
                    datePickerState.setSelection(Date().time)
                },
                datePickerState = datePickerState,
                requiredDateResult = uiState.selectDate!!
            )
        }


    }

}


@Composable
private fun Footer(
    onCancelClick: () -> Unit,
    onPrintClick: () -> Unit,
    showPrintButton: Boolean = true
) {
    Surface {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = 16.dp)
        ) {
            Box(modifier = Modifier.weight(1f))
            Button(
                onClick = onCancelClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                colors = ButtonDefaults.buttonColors(backgroundColor = MaterialTheme.colors.blackAndWhiteReverse),
                elevation = ButtonDefaults.elevation(0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colors.blackAndWhite)
            ) {
                Text(text = "Cancel", color = MaterialTheme.colors.blackAndWhite)
            }
            Spacer(modifier = Modifier.width(10.dp))
            if (showPrintButton) {
                Button(
                    onClick = onPrintClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    colors = ButtonDefaults.buttonColors(backgroundColor = MaterialTheme.colors.blackAndWhite),
                    elevation = ButtonDefaults.elevation(0.dp)
                ) {
                    Text(text = "Print", color = MaterialTheme.colors.blackAndWhiteReverse)
                }
            }
        }
    }
}
