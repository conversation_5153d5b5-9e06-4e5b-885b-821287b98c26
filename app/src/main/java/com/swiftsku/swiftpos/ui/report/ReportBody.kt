package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import com.swiftsku.swiftpos.data.model.ReportDetail
import com.swiftsku.swiftpos.data.model.ReportTabLayout
import com.swiftsku.swiftpos.ui.components.CenteredComposable
import com.swiftsku.swiftpos.ui.components.ReportDetail

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColumnScope.ReportBody(
    loader: Boolean = false,
    bmp: Map<String, String> = emptyMap(),
    onCurrentPageChange: (Int) -> Unit
) {
    if (bmp.isEmpty()) {
        onCurrentPageChange(-1)
        if (loader) {
            CenteredComposable {
                CircularProgressIndicator()
            }
        } else {
            CenteredComposable {
                Text(
                    text = "No data found",
                )
            }
        }
    } else {
        if (bmp.size == 1) {
            onCurrentPageChange(0)
            ReportDetail(
                modifier = Modifier.weight(1f),
                details = bmp.values.first()
            )
        } else {
            val pagerState = rememberPagerState(pageCount = { bmp.size })

            LaunchedEffect(pagerState.currentPage) {
                onCurrentPageChange(pagerState.currentPage)
            }

            ReportTabLayoutPager(
                modifier = Modifier.weight(1f),
                bmp.map { ReportTabLayout(it.key, it.value) },
                pagerState = pagerState
            )
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColumnScope.ReportBody(
    stats: List<ReportDetail>,
    loader: Boolean = false,
    onCurrentPageChange: (Int) -> Unit
) {

    if (stats.isEmpty()) {
        onCurrentPageChange(-1)
        if (loader) {
            CenteredComposable {
                CircularProgressIndicator()
            }
        } else {
            CenteredComposable {
                Text(
                    text = "No data found",
                )
            }
        }
    } else {
        if (stats.size == 1) {
            onCurrentPageChange(0)
            ReportDetail(
                modifier = Modifier.weight(1f),
                details = stats.first().terminalData
            )
        } else {
            val pagerState = rememberPagerState(pageCount = { stats.size })

            LaunchedEffect(pagerState.currentPage) {
                onCurrentPageChange(pagerState.currentPage)
            }

            ReportTabLayoutPager(
                modifier = Modifier.weight(1f),
                stats.map { ReportTabLayout(it.terminalId, it.terminalData) },
                pagerState = pagerState
            )
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColumnScope.ReportBodyWithBackButton(
    stats: List<ReportDetail>,
    loader: Boolean = false,
    onCurrentPageChange: (Int) -> Unit,
    onBackClick: () -> Unit
) {
    Column {
        // Display the back button
        BackButton(onBackClick = onBackClick)
        ReportBody(stats = stats, loader = loader, onCurrentPageChange = onCurrentPageChange)
    }
}

@Composable
fun BackButton(onBackClick: () -> Unit) {
    IconButton(onClick = onBackClick) {
        Icon(
            imageVector = Icons.Default.ArrowBack,
            contentDescription = "Back"
        )
    }
}
