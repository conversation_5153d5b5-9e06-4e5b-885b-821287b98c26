package com.swiftsku.swiftpos.ui.dashboard.pricebook

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.model.PricebookSearchResponse
import com.swiftsku.swiftpos.data.remote.repositories.app.AppRepository
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class PricebookViewModel @Inject constructor(
    private val appRepository: AppRepository
) : ViewModel() {

    private val _searchResults = MutableStateFlow<Result<PricebookSearchResponse>?>(null)
    val searchResults = _searchResults.asStateFlow()
    private val _currentQuery = MutableStateFlow<Pair<String, String>?>(null)

    init {
        viewModelScope.launch {
            _currentQuery
                .filterNotNull()
                .filter { it.second.isNotBlank() && it.second.length > 2 }
                .collectLatest { (storeCode, query) ->
                    _searchResults.emit(Result.Loading)
                    val result = try {
                        appRepository.searchPricebook(storeCode, query)
                    } catch (e: Exception) {
                        Result.Error(e.message ?: "Couldn't query Pricebook")
                    }
                    _searchResults.emit(result)
                }
        }
    }

    fun searchPricebook(storeCode: String, searchQuery: String) {
        EventUtils.logEvent(
            EventUtils.Events.PLU_SEARCH,
            mapOf(EventUtils.EventProp.SEARCH_QUERY to searchQuery)
        )
        _currentQuery.value = Pair(storeCode, searchQuery)
    }

    fun clearSearchResults() = viewModelScope.launch {
        _searchResults.emit(null)
    }
}