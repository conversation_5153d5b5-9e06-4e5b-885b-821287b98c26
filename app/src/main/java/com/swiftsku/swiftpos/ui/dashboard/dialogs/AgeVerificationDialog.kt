package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.data.model.isUnderAge
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.visualizers.DateVisualTransformation
import com.swiftsku.swiftpos.utils.getAgeInYearsAndMonths
import com.swiftsku.swiftpos.utils.parseDate
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun AgeVerificationDialog(
    onDismissRequest: () -> Unit,
    onSubmitRequest: (date: String) -> Unit,
    onSkipRequest: () -> Unit,
    dashboardVM: DashboardViewModel
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/AgeVerificationIcon.svg")
            .decoderFactory(SvgDecoder.Factory()).build()
    )
    var date by remember { mutableStateOf("") }
    var buyersAge by remember { mutableStateOf<String?>(null) }
    var isDateCriteriaMatched by remember { mutableStateOf(false) }
    val focusManager: FocusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(Unit) { focusRequester.requestFocus() }
    val selectedPLUItem by dashboardVM.selectedPLUItem.collectAsState()
    val selectedDepartment by dashboardVM.selectedDepartment.collectAsState()
    val minCustomerAge =
        selectedPLUItem?.minimumCustomerAge ?: selectedDepartment?.minimumCustomerAge
    val formattedDate: String? = minCustomerAge?.let {
        val today = LocalDate.now()
        val ageLimitDate = today.minusYears(it.toLong())
        ageLimitDate.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))
    }
    val storeConfig by dashboardVM.storeLevelConfig.collectAsState()

    fun handleDateInput(input: String) {
        date = input
        input.parseDate()?.let {
            isDateCriteriaMatched = !isUnderAge(minCustomerAge, input)
            buyersAge = getAgeInYearsAndMonths(it)
        } ?: run {
            isDateCriteriaMatched = false
            buyersAge = null
        }
    }
    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        Box(
            modifier = Modifier
                .pointerInput(Unit) {
                    detectTapGestures(onTap = { focusRequester.requestFocus() })
                }
                .focusRequester(focusRequester)
                .focusable(true)
                .focusProperties { canFocus = true }
                .fillMaxWidth()
                .onKeyEvent { event ->
                    dashboardVM.onKeyEvent(event)
                    true
                }) {
            Surface(
                modifier = Modifier
                    .padding(24.dp)
//                    .width(500.dp)
                    .clip(shape = RoundedCornerShape(8.dp))

            ) {
                Column(
//                horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(24.dp)
                ) {

                    Box(modifier = Modifier.fillMaxWidth()) {
                        Image(
                            painter = painter,
                            contentDescription = null,
                            modifier = Modifier
                                .size(115.dp)
                                .align(Alignment.Center)
                        )
                        if (!storeConfig?.mandatoryIdCheck.isTrue()) {
                            ClickableText(
                                text = AnnotatedString(text = "Skip"),
                                onClick = { onSkipRequest() },
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(10.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Note: You can scan driver's license on this screen",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.subtitle2
                    )

                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = "Enter buyer's date of birth",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.caption
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    TextField(
                        value = date,
                        placeholder = { Text(text = "MM/DD/YYYY") },
                        modifier = Modifier.fillMaxWidth(),
                        visualTransformation = DateVisualTransformation,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword),
                        onValueChange = { input -> handleDateInput(input) })
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Entered age: ${buyersAge ?: ""}",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.caption,
                        color = if (buyersAge != null && !isDateCriteriaMatched) Red else Color.Unspecified
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Minimum age required: $minCustomerAge years",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.caption
                    )
                    formattedDate?.let {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Buyer must be born on or before: $formattedDate",
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.caption
                        )
                    }
                    Spacer(modifier = Modifier.height(24.dp))

                    Row(modifier = Modifier.fillMaxWidth()) {
                        Button(
                            onClick = onDismissRequest,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                        ) {
                            Text(text = "Cancel", color = Color.White)
                        }
                        Spacer(modifier = Modifier.width(10.dp))
                        Button(
                            enabled = isDateCriteriaMatched,
                            onClick = { onSubmitRequest(date) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            Text(text = "Submit")
                        }
                    }
                }
            }
        }
    }
}

