package com.swiftsku.swiftpos.ui.components

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerState
import androidx.compose.runtime.Composable
import com.swiftsku.swiftpos.data.type.ReportTimeResult

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppTimePicker(
    requiredDateResult: ReportTimeResult,
    timePickerState: TimePickerState,
    onTimeChange: (selectedDate: String, ReportTimeResult) -> Unit,
    onDismissDialog: () -> Unit,
) {


//    DatePickerDialog(
//        properties = DialogProperties(
//            dismissOnBackPress = false,
//            dismissOnClickOutside = false
//        ),
//        onDismissRequest = onDismissDialog,
//        confirmButton = {
//            TextButton(onClick = {
//                timePickerState.let {
//                    onTimeChange("${it.hour}:${it.minute}", requiredDateResult)
//                    onDismissDialog()
//                }
//            }) {
//                Text(text = "Confirm")
//            }
//        },
//        dismissButton = {
//            TextButton(
//                onClick = onDismissDialog
//            ) {
//                Text(text = "Cancel")
//            }
//        }
//    ) {
//    }

}