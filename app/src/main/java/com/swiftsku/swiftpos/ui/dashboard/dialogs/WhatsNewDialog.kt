package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.AsyncImage
import coil.decode.ImageDecoderDecoder
import coil.request.ImageRequest
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.AbstractYouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
import com.swiftsku.swiftpos.data.model.ContentType
import com.swiftsku.swiftpos.data.model.ReleaseNotesContent
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Grey_5F
import com.swiftsku.swiftpos.ui.theme.Grey_D6
import com.swiftsku.swiftpos.utils.extractYoutubeVideoId


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun WhatsNewDialog(
    onDismissRequest: () -> Unit,
    dashboardVM: DashboardViewModel
) {
    val unreadNotes by dashboardVM.unreadNotes.collectAsState()
    val releaseNotes by dashboardVM.releaseNotes.collectAsState()
    var selectedRelease by remember { mutableStateOf<String?>(null) }

    LaunchedEffect(releaseNotes) {
        val first = releaseNotes.firstOrNull()?.version ?: return@LaunchedEffect
        if (selectedRelease == null || releaseNotes.none { it.version == selectedRelease }) {
            selectedRelease = first
        }
        dashboardVM.addReadNotificationId(first)
    }

    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(modifier = Modifier
            .width(700.dp)
            .height(500.dp)
            .padding(32.dp)
            .clip(shape = RoundedCornerShape(8.dp))
            .semantics { testTagsAsResourceId = true }) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "What's New",
                        style = MaterialTheme.typography.h6,
                        modifier = Modifier.padding(
                            start = 12.dp, end = 12.dp, top = 8.dp, bottom = 8.dp
                        ),
                        color = Color.Black
                    )
                    IconButton(
                        onClick = onDismissRequest,
                        modifier = Modifier.padding(end = 12.dp)
                    ) {
                        Icon(Icons.Default.Close, contentDescription = "Close")
                    }
                }
                Divider(color = Color.DarkGray)
                Row(modifier = Modifier.fillMaxWidth()) {
                    LazyColumn(
                        modifier = Modifier.weight(0.4f)
                    ) {
                        releaseNotes.forEachIndexed { _, releaseData ->
                            item {
                                val unread = unreadNotes.contains(releaseData.version)
                                Row(
                                    modifier = Modifier
                                        .background(if (selectedRelease == releaseData.version) Grey_D6 else Color.Transparent)
                                        .clickable(onClick = {
                                            dashboardVM.addReadNotificationId(releaseData.version)
                                            selectedRelease = releaseData.version
                                        })
                                        .padding(
                                            start = 12.dp, end = 12.dp, top = 6.dp, bottom = 6.dp
                                        ), verticalAlignment = Alignment.CenterVertically
                                ) {
                                    val boxBg = if (unread) Blue else Color.Transparent
                                    Box(
                                        modifier = Modifier
                                            .padding(6.dp)
                                            .size(12.dp)
                                            .background(boxBg, shape = CircleShape)
                                    )
                                    Column {
                                        Text(
                                            text = releaseData.date,
                                            style = MaterialTheme.typography.body1,
                                            modifier = Modifier.fillMaxWidth(),
                                            color = if (selectedRelease == releaseData.version) Blue else Color.DarkGray
                                        )
                                        Text(
                                            text = releaseData.version,
                                            style = MaterialTheme.typography.h6,
                                            modifier = Modifier.fillMaxWidth(),
                                            color = if (selectedRelease == releaseData.version) Blue else Color.DarkGray
                                        )
                                    }
                                }
                            }
                        }
                    }
                    Divider(
                        color = Color.DarkGray, modifier = Modifier
                            .fillMaxHeight()
                            .width(1.dp)
                    )
                    Column(
                        modifier = Modifier
                            .weight(0.6f)
                            .padding(top = 8.dp, bottom = 16.dp)
                    ) {
                        releaseNotes.find { it.version == selectedRelease }?.notes?.let { notes ->
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, end = 16.dp)
                            ) {
                                if (notes.features.isNotEmpty()) {
                                    item {
                                        ReleaseSection(
                                            title = "Features", items = notes.features
                                        )
                                    }
                                }
                                if (notes.changes.isNotEmpty()) {
                                    item {
                                        ReleaseSection(
                                            title = "Changes", items = notes.changes
                                        )
                                    }
                                }
                                if (notes.bugs.isNotEmpty()) {
                                    item {
                                        ReleaseSection(
                                            title = "Bugfixes", items = notes.bugs
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun ReleaseSection(
    title: String, items: List<ReleaseNotesContent>
) {
    Text(
        text = title,
        style = MaterialTheme.typography.h6,
        modifier = Modifier.padding(bottom = 8.dp),
        color = Color.Black
    )
    items.forEach { item ->
        Column(
            modifier = Modifier.padding(bottom = 4.dp), horizontalAlignment = Alignment.Start
        ) {
            Text("• ${item.info}")
            Spacer(modifier = Modifier.height(4.dp))
            when (item.contentType) {
                ContentType.IMAGE -> {
                    item.link?.let { imageUrl ->
                        ContentWrapper {
                            AsyncImage(
                                model = imageUrl,
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Fit
                            )
                        }
                    }
                }

                ContentType.GIF -> {
                    item.link?.let { gifUrl ->
                        ContentWrapper {
                            AsyncImage(
                                model = ImageRequest.Builder(LocalContext.current).data(gifUrl)
                                    .decoderFactory(ImageDecoderDecoder.Factory()).build(),
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Fit
                            )
                        }
                    }
                }

                ContentType.VIDEO -> {
                    item.link?.let {
                        ContentWrapper { YouTubeVideoPlayer(it) }
                    }
                }

                ContentType.LINK -> {
                    item.link?.let { url -> WebLink(url = url) }
                }

                else -> {}
            }
        }
    }
    Spacer(modifier = Modifier.height(16.dp))
}

@Composable
fun ContentWrapper(
    content: @Composable () -> Unit
) {
    Spacer(modifier = Modifier.height(4.dp))
    Box(
        modifier = Modifier
            .size(250.dp, 140.dp)
            .background(Grey_5F, RoundedCornerShape(4.dp))
            .padding(4.dp), contentAlignment = Alignment.Center
    ) {
        content()
    }
}

@Composable
fun YouTubeVideoPlayer(videoUrl: String) {
    val videoId = extractYoutubeVideoId(videoUrl)
    val lifecycleOwner = LocalLifecycleOwner.current
    if (videoId != null) {
        AndroidView(
            factory = { context ->
                YouTubePlayerView(context).apply {
                    lifecycleOwner.lifecycle.addObserver(this)
                    addYouTubePlayerListener(object : AbstractYouTubePlayerListener() {
                        override fun onReady(youTubePlayer: YouTubePlayer) {
                            youTubePlayer.cueVideo(videoId, 0f)
                        }
                    })
                }
            }, modifier = Modifier.fillMaxSize()
        )
    } else {
        VideoLink(url = videoUrl)
    }
}

@Composable
fun VideoLink(url: String) {
    val uriHandler = LocalUriHandler.current

    Row(modifier = Modifier
        .clickable { uriHandler.openUri(url) }
        .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically) {
        Icon(
            imageVector = Icons.Default.PlayArrow,
            contentDescription = "Play Video",
            tint = Blue,
            modifier = Modifier.size(20.dp)
        )
        Text(
            text = "Watch Video", color = Blue, style = MaterialTheme.typography.body2.copy(
                textDecoration = TextDecoration.Underline
            )
        )
    }
}

@Composable
fun WebLink(url: String) {
    val uriHandler = LocalUriHandler.current
    Box(
        modifier = Modifier.padding(start = 8.dp, end = 8.dp)
    ) {
        ClickableText(text = buildAnnotatedString {
            append("Read more")
            addStyle(
                style = SpanStyle(
                    color = Blue, textDecoration = TextDecoration.Underline
                ), start = 0, end = length
            )
        }, onClick = { uriHandler.openUri(url) }, style = MaterialTheme.typography.body2
        )
    }
}
