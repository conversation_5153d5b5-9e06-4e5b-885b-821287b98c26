package com.swiftsku.swiftpos.ui.activity.base

import androidx.compose.ui.graphics.Color
import com.couchbase.lite.ReplicatorActivityLevel
import com.couchbase.lite.ReplicatorActivityLevel.BUSY
import com.couchbase.lite.ReplicatorActivityLevel.CONNECTING
import com.couchbase.lite.ReplicatorActivityLevel.IDLE
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.TerminalConfig
import com.swiftsku.swiftpos.services.network.NetworkStatus
import com.swiftsku.swiftpos.services.replicator.ReplicatorStatus
import com.swiftsku.swiftpos.services.vpn.VPNStatus
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Green
import com.swiftsku.swiftpos.ui.theme.Red

data class BaseAppState(
    val networkStatus: NetworkStatus = NetworkStatus.Unknown,
    val sgCredentials: SgCreds? = null,
    val vpnStatus: VPNStatus = VPNStatus.Unknown,
    val replicatorStatus: ReplicatorStatus = ReplicatorStatus.Unknown,
    val replicatorChange: ReplicatorActivityLevel = ReplicatorActivityLevel.OFFLINE,
    val unSyncedTransactions: Int = 0,
    val storeConfig: StoreConfig? = null,
    val storeLevelConfig: StoreUsers? = null,
    val terminalConfig: TerminalConfig? = null,
    val fdcConfig: FDCConfig? = null
)


fun BaseAppState.sgConnectionColor(): Color = when (replicatorChange) {
    IDLE -> Blue
    BUSY -> Green
    CONNECTING -> Color.Yellow
    else -> Red
}

