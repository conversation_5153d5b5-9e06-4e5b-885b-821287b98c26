package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportTimeResult
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.data.type.reportDefaults
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.ui.components.ContainerBox
import com.swiftsku.swiftpos.ui.components.SelectorBox
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.utils.months
import com.swiftsku.swiftpos.utils.years

@Composable
fun ReportHeader(
    modifier: Modifier = Modifier,
    reportUIState: ReportUIState,
    onReportTypeChange: (ReportType) -> Unit,
    onMonthChange: (String) -> Unit,
    onYearChange: (String) -> Unit,
    onDateSelect: (ReportDateResult) -> Unit,
    onTimeSelect: (ReportTimeResult) -> Unit,
    onPresetSelect: (String) -> Unit,
    onHistoricalShiftCashierSelect: (String) -> Unit,
    onHistoricalShiftTerminalSelect: (String) -> Unit,
    onHistoricalBatchTerminalSelect: (String) -> Unit,
    items: List<ReportType> = reportDefaults,
) {
    Surface(
        modifier = modifier
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .padding(horizontal = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                ReportTypeMenu(
                    reportType = reportUIState.selectedReportType,
                    onSelectedMenuChange = onReportTypeChange,
                    items = items
                )

                when (reportUIState.selectedReportType) {
                    ReportType.AdHoc -> {
                        DateTimeSelectionMenu(
                            startDate = reportUIState.adHocState.selectedAdHocStartDate,
                            endDate = reportUIState.adHocState.selectedAdHocEndDate,
                            startTime = reportUIState.adHocState.selectedAdHocStartTime,
                            endTime = reportUIState.adHocState.selectedAdHocEndTime,
                            onDateSelect = onDateSelect,
                            onTimeClick = onTimeSelect
                        )
                    }

                    ReportType.EOD -> {
                        ContainerBox(
                            title = reportUIState.eodState.selectedEODDate.toDate(),
                            contentDescription = "Select Date",
                            modifier = Modifier
                                .clickable {
                                    onDateSelect(ReportDateResult.EODDate)
                                }
                                .background(GrayBackground)
                                .padding(12.dp)
                                .width(115.dp),
                            icon = {
                                Icon(
                                    Icons.Rounded.DateRange,
                                    contentDescription = "Select Date",
                                )
                            }
                        )
                    }

                    ReportType.EOM -> {
                        Row {
                            SelectorBox(
                                selectedItem = reportUIState.eoyState.selectedEOYDate,
                                onSelectedItemChange = onYearChange,
                                title = "Select Year",
                                items = years
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            SelectorBox(
                                selectedItem = reportUIState.eomState.selectedEOMDate,
                                onSelectedItemChange = onMonthChange,
                                title = "Select Month",
                                items = months
                            )
                        }
                    }

                    ReportType.EOY -> {
                        SelectorBox(
                            selectedItem = reportUIState.eoyState.selectedEOYDate,
                            onSelectedItemChange = onYearChange,
                            title = "Select Year",
                            items = years
                        )
                    }

                    ReportType.Shift -> {}
                    ReportType.Preset -> {
                        SelectorBox(
                            items = reportUIState.presetState.presets,
                            selectedItem = reportUIState.presetState.selectedPreset
                                ?: "Select Preset",
                            onSelectedItemChange = onPresetSelect,
                            title = "Select Preset"
                        )
                    }

                    ReportType.HistoricalShift -> {
                        HistoricalShiftDateFilterMenu(
                            startDate = reportUIState.historicalShiftState.selectedShiftStartDate,
                            endDate = reportUIState.historicalShiftState.selectedShiftEndDate,
                            onDateSelect = onDateSelect,
                        )
                    }

                    ReportType.HistoricalBatch -> {
                        HistoricalBatchDateFilterMenu(
                            startDate = reportUIState.historicalBatchState.selectedBatchStartDate,
                            endDate = reportUIState.historicalBatchState.selectedBatchEndDate,
                            onDateSelect = onDateSelect,
                        )
                    }
                }

            }

            if (reportUIState.selectedReportType == ReportType.HistoricalShift) {
                HistoricalShiftFieldFilterMenu(
                    terminals = reportUIState.historicalShiftState.terminals,
                    terminal = reportUIState.historicalShiftState.selectedTerminal,
                    cashiers = reportUIState.historicalShiftState.cashiers,
                    cashierId = reportUIState.historicalShiftState.selectedCashier,
                    onCashierSelect = onHistoricalShiftCashierSelect,
                    onTerminalSelect = onHistoricalShiftTerminalSelect
                )
            }

            if (reportUIState.selectedReportType == ReportType.HistoricalBatch) {
                HistoricalBatchFieldFilterMenu(
                    terminals = reportUIState.historicalBatchState.terminals,
                    terminal = reportUIState.historicalBatchState.selectedTerminal,
                    onTerminalSelect = onHistoricalBatchTerminalSelect
                )
            }

            Divider()
        }
    }
}




