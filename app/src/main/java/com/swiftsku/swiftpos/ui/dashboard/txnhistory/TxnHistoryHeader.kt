package com.swiftsku.swiftpos.ui.dashboard.txnhistory

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.SwiftPOSTheme

@Composable
fun TxnHistoryHeader() {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(text = "Transaction Id.", modifier = Modifier.weight(3f))
        Text(text = "Time", modifier = Modifier.weight(2f))
        Text(text = "#Items", modifier = Modifier.weight(1f), textAlign = TextAlign.Center)
        Text(text = "Total amount", modifier = Modifier.weight(2f).padding(end = 32.dp), textAlign = TextAlign.End)
        Text(text = "Type", modifier = Modifier.weight(2f))
        Icon(
            Icons.Filled.ArrowForward,
            null,
            modifier = Modifier.size(24.dp),
            tint = Color.White
        )

    }
}

