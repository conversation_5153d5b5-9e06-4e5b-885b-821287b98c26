package com.swiftsku.swiftpos.ui.activity.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.couchbase.lite.ReplicatorActivityLevel
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.UserRepositoryCache
import com.swiftsku.swiftpos.domain.config.GetTerminalConfigUseCase
import com.swiftsku.swiftpos.domain.config.ListenForStoreConfigUseCase
import com.swiftsku.swiftpos.domain.couchbase.GetSGCredentialUseCase
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import com.swiftsku.swiftpos.services.network.NetworkConnectivityService
import com.swiftsku.swiftpos.services.network.isConnected
import com.swiftsku.swiftpos.services.replicator.CouchbaseReplicatorService
import com.swiftsku.swiftpos.services.replicator.ReplicatorStatus
import com.swiftsku.swiftpos.services.replicator.isConnected
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConfigViewModel @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val networkConnectivityService: NetworkConnectivityService,
    private val replicatorService: CouchbaseReplicatorService,
    private val getSGCredentialUseCase: GetSGCredentialUseCase,
    private val getTerminalConfigUseCase: GetTerminalConfigUseCase,
    private val listenStoreConfig: ListenForStoreConfigUseCase,
    @UserRepositoryCache private val userRepository: UserRepository,
) : ViewModel() {


    private val _appState = MutableStateFlow(BaseAppState())
    val appState = _appState


    init {
        initNetworkListener()
        fetchTerminalConfig()
        initStoreConfigListener()
        initSGListener()
        initReplicatorListener()
    }

    private fun initStoreLevelConfigListener(storeCode: String) = viewModelScope.launch(dispatcher) {
        userRepository.getStoreConfigStream(storeCode).collectLatest { storeLevelConfig ->
            _appState.update { state -> state.copy(storeLevelConfig = storeLevelConfig) }
        }
    }

    private fun initStoreConfigListener() = viewModelScope.launch(dispatcher) {
        val serialKey = DeviceIdentifier.getSerialNumber() ?: return@launch
        listenStoreConfig(serialKey).collectLatest { storeConfig ->
            initStoreLevelConfigListener(storeConfig.storeCode)
            _appState.update { state -> state.copy(storeConfig = storeConfig) }
        }
    }

    private fun initReplicatorListener() = viewModelScope.launch {
        launch(dispatcher) {
            combine(
                replicatorService.replicatorChange,
                replicatorService.replicatorStatus
            ) { replicatorChange, replicatorStatus ->
                _appState.update { state ->
                    state.copy(
                        replicatorChange = replicatorChange,
                        replicatorStatus = replicatorStatus
                    )
                }
            }.collect()
        }
        launch(dispatcher) {
            replicatorService.unSyncedTxns.collectLatest { count ->
                _appState.update { it.copy(unSyncedTransactions = count) }
            }
        }
    }

    private fun initNetworkListener() = viewModelScope.launch(dispatcher) {
        networkConnectivityService.networkStatus.collectLatest { networkStatus ->
            _appState.update { state ->
                state.copy(networkStatus = networkStatus)
            }
        }
    }


    private fun initSGListener() = viewModelScope.launch {
        combine(
            networkConnectivityService.networkStatus,
            _appState
        ) { networkStatus, state ->
            val hasNetwork = networkStatus.isConnected()
            val sgCredentials = state.sgCredentials

            if (!hasNetwork) {
                return@combine
            }

            if (sgCredentials == null) {
                fetchSGCredentials()
            }

            if (sgCredentials != null) {
                setUpReplicator(
                    replicatorChange = state.replicatorChange,
                    replicatorStatus = state.replicatorStatus,
                    sgCredentials = sgCredentials
                )
            }
        }.collect()
    }

    fun fetchTerminalConfig() = viewModelScope.launch(dispatcher) {
        val serialKey = DeviceIdentifier.getSerialNumber() ?: return@launch
        val terminalConfig = getTerminalConfigUseCase.get(serialKey)
        _appState.update { it.copy(terminalConfig = terminalConfig) }
    }

    private fun setUpReplicator(
        replicatorChange: ReplicatorActivityLevel,
        replicatorStatus: ReplicatorStatus?,
        sgCredentials: SgCreds
    ) = viewModelScope.launch(dispatcher) {

        if (replicatorChange == ReplicatorActivityLevel.STOPPED) {
            replicatorService.reset()
        }

        val replicator = replicatorService.replicator.value

        if (replicator == null || replicatorChange == ReplicatorActivityLevel.OFFLINE) {
            replicatorService.initReplicator(sgCredentials)
        }
        val replicatorConnected = replicatorStatus?.isConnected() ?: false
        if (!replicatorConnected) {
            replicatorService.start()
        }

    }

    private fun fetchSGCredentials() = viewModelScope.launch {
        val credResult = getSGCredentialUseCase()
        var sgCredentials: SgCreds? = null
        when (credResult) {
            is Result.Error -> {
                EventUtils.recordException(Exception("Error in fetching SG Credentials code: ${credResult.errorCode}, message: ${credResult.errorMessage}"))
            }
            Result.Loading -> {}
            is Result.Success -> {
                sgCredentials = credResult.data
            }
        }
        _appState.update { it.copy(sgCredentials = sgCredentials) }
    }

    override fun onCleared() {
        // this will also stop the replicator7
        viewModelScope.launch { replicatorService.reset() }
        super.onCleared()
    }

}