package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.data.type.PayoutType
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.PayoutState
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun PayoutDialog(
    onDismissRequest: () -> Unit,
    onSubmitRequest: (data: Payout) -> Unit,
    payout: PayoutState,
    dashboardVM: DashboardViewModel
) {

    var type by remember { mutableStateOf<PayoutType>(PayoutType.Lottery) }

    Dialog(
        onDismissRequest = onDismissRequest
    ) {

        Box(
            modifier = Modifier
                .padding(vertical = 100.dp)
                .background(Color.White)
                .width(600.dp)
                .height(450.dp),
        ) {
            Surface(
                modifier = Modifier
                    .padding(20.dp)
                    .clip(shape = RoundedCornerShape(8.dp))
                    .semantics { testTagsAsResourceId = true }
            ) {
                Column(modifier = Modifier.padding(8.dp)) {

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedButton(
                            modifier = Modifier
                                .weight(1f)
                                .height(60.dp),
                            onClick = {
                                type = PayoutType.Vendor
                            },
                            colors = ButtonDefaults.outlinedButtonColors(
                                backgroundColor = if (payout.enableVendor) if (type == PayoutType.Vendor) Blue else Color.White else GrayBackground,
                            ),
                            enabled = payout.enableVendor
                        ) {
                            Text(
                                text = "${PayoutType.Vendor.type} Payout",
                                color = if (type == PayoutType.Vendor) Color.White else Color.Black
                            )
                        }
                        Spacer(modifier = Modifier.width(10.dp))
                        OutlinedButton(
                            modifier = Modifier
                                .weight(1f)
                                .height(60.dp),
                            onClick = {
                                type = PayoutType.Lottery
                            },
                            colors = ButtonDefaults.outlinedButtonColors(backgroundColor = if (type == PayoutType.Lottery) Blue else Color.White)
                        ) {
                            Text(
                                text = "${PayoutType.Lottery.type} Payout",
                                color = if (type == PayoutType.Lottery) Color.White else Color.Black
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(30.dp))
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(3),
                        ) {
                            items(if (type == PayoutType.Vendor) payout.vendorNames else payout.lotteryNames) { item ->
                                CardItem(
                                    title = item,
                                    backgroundColor = Teal,
                                    onClick = {
                                        onSubmitRequest(Payout(info = item, category = type))
                                        onDismissRequest()
                                    },
                                    isMultiline = true,
                                    fontSize = 16.sp
                                )
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(30.dp))
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Box(modifier = Modifier.weight(1f))
                        if (type == PayoutType.Vendor) {
                            Button(
                                onClick = { dashboardVM.showVendorDialog() },
                                modifier = Modifier
                                    .height(60.dp)
                                    .weight(1f)
                                    .padding(end = 4.dp)
                            ) {
                                Text(text = "Add New", color = Color.White)
                            }
                        } else {
                            Box(modifier = Modifier.weight(1f))
                        }
                        Button(
                            onClick = onDismissRequest,
                            modifier = Modifier
                                .height(60.dp)
                                .weight(1f),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                        ) {
                            Text(text = "Cancel", color = Color.White)
                        }
                    }
                }
            }
        }
    }
}
