package com.swiftsku.swiftpos.ui.visualizers

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation

object DollarVisualTransformation : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {
        // Make transformation from cents to dollars
        val input = text.text
        val inDollar = "$ " + String.format("%.2f", (input.toLongOrNull() ?: 0L) / 100f)

        val dollarTranslator = object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                return inDollar.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                return input.length
            }
        }
        return TransformedText(
            text = AnnotatedString(inDollar),
            offsetMapping = dollarTranslator
        )
    }
}