package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.components.SelectorBox

@Composable
fun HistoricalBatchFieldFilterMenu(
    terminals: List<String> = emptyList(),
    terminal: String?,
    onTerminalSelect: (String) -> Unit
) {
    Row(
        modifier = Modifier
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.weight(1f))
        SelectorBox(
            modifier = Modifier.height(40.dp),
            items = terminals,
            selectedItem = if (terminal.isNullOrEmpty()) "All Terminals" else terminal,
            onSelectedItemChange = onTerminalSelect,
            title = "Select Terminal"
        )
    }
}

