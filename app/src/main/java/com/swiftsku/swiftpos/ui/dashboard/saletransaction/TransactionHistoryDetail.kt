package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.swiftsku.swiftpos.data.model.AccountLedgerTxn
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.data.model.enableRefund
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.model.promotionAmount
import com.swiftsku.swiftpos.data.model.showReprint
import com.swiftsku.swiftpos.data.model.subtotalAmount
import com.swiftsku.swiftpos.data.model.totalAmount
import com.swiftsku.swiftpos.data.model.totalTaxAmount
import com.swiftsku.swiftpos.data.model.typeName
import com.swiftsku.swiftpos.data.model.unitPrice
import com.swiftsku.swiftpos.data.model.unitPriceString
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.AppToast
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.dialogs.ConfirmDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.RequestCashDrawerClose
import com.swiftsku.swiftpos.ui.navigation.Routes
import kotlinx.coroutines.delay

@Composable
fun TransactionHistoryDetail(
    navController: NavController,
    transactionId: String,
    transactionHistoryDetailVM: TxnHistoryDetailViewModel,
    dashboardVM: DashboardViewModel
) {

    val toast by transactionHistoryDetailVM.toastMessage.collectAsState()
    val uiState by transactionHistoryDetailVM.uiState.collectAsState()

    LaunchedEffect(Unit) {
        transactionHistoryDetailVM.fetchTransaction(transactionId)
    }

    LaunchedEffect(toast) {
        if (toast != null) {
            delay(2000)
            transactionHistoryDetailVM.clearToast()
        }
    }

    Surface(
        modifier = Modifier
            .padding(top = 100.dp, bottom = 100.dp)
            .background(Color.White)
            .width(750.dp)
    ) {
        Box {
            Column {
                TopAppBar(
                    elevation = 4.dp,
                    title = {
                        Text("${uiState.transaction?.typeName() ?: ""} Transaction")
                    },
                    actions = {
                        IconButton(onClick = {
                            navController.popBackStack(Routes.Dashboard.route, false)
                        }) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            navController.popBackStack()
                        }) {
                            Icon(Icons.Filled.ArrowBack, null)
                        }
                    }
                )

                val appliedFees = (uiState.transaction as? SaleTransaction)?.appliedFees
                    ?: (uiState.transaction as? AccountLedgerTxn)?.appliedFees

                if (uiState.transaction != null) {
                    LazyColumn(modifier = Modifier.padding(20.dp)) {
                        item {
                            TransactionHeader(
                                name = uiState.transaction!!.cashierId,
                                transactionId = uiState.transaction!!.txnId,
                                totalAmount = uiState.transaction!!.totalAmount(),
                                date = uiState.transaction!!.txnStartTime.toDateTime(),
                                txnType = uiState.transaction!!.typeName(),
                                showRefund = uiState.transaction!!.enableRefund(),
                                onRefundClick = { transactionHistoryDetailVM.showConfirmDialog() },
                                isRefunded = uiState.transaction!!.txnType == TransactionType.SaleRefund,
                                loading = uiState.refunding,
                                onReprintClick = { transactionHistoryDetailVM.reprint() },
                                showReprint = uiState.transaction!!.showReprint(),
                                cardPayment = uiState.cardPayment(),
                                cashPayment = uiState.cashPayment(),
                                ebtPayment = uiState.ebtPayment(),
                                chequePayment = uiState.checkPayment(),
                                creditPayment = uiState.creditPayment(),
                                ebtInfo = (uiState.transaction as? SaleTransaction)?.ebtInfo,
                                isIntegrated = uiState.transaction!!.isIntegrated,
                                txn = uiState.transaction,
                                appliedFees = appliedFees
                            )
                        }
                        item {
                            TransactionItemHeader(
                                totalQuantity = uiState.transaction!!.txnItems.sumOf { it.quantity }
                            )
                        }
                        items(uiState.transaction!!.txnItems) { item ->
                            TransactionItem(
                                description = item.itemName(),
                                qty = item.quantity,
                                volume = (item as? TransactionItemWithFuel)?.let {
                                    it.postFuel?.volume ?: it.preFuel.volume
                                },
                                price = item.unitPriceString(),
                                amount = item.totalItemPrice(),
                                deleted = item.status == TransactionItemStatus.Deleted
                            )
                        }
                        item {
                            TransactionSummaryDetails(
                                total = uiState.transaction!!.totalAmount(),
                                subTotal = uiState.transaction!!.subtotalAmount(),
                                tax = uiState.transaction!!.totalTaxAmount(),
                                promotion = uiState.transaction!!.promotionAmount(),
                                lotteryPayout = (uiState.transaction as? SaleTransaction)?.lotteryPayout,
                                lotteryPayouts = (uiState.transaction as? SaleTransaction)?.lotteryPayouts,
                                txnDiscount = (uiState.transaction as? SaleTransaction)?.txnDiscount
                            )
                        }
                    }
                }


            }

            if (uiState.refundState != null) {
                RequestCashDrawerClose(
                    onDismissRequest = { transactionHistoryDetailVM.onCashDrawerCloseClick() },
                    txnHistoryDetailVM = transactionHistoryDetailVM,
                    message = {

                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = " ${uiState.refundState!!.refundAmount.toDollars()} ",
                                style = MaterialTheme.typography.h6
                            )
                            Text(
                                text = "Please give the refund amount and close the drawer to complete the transaction."
                            )
                        }
                    }
                )
            }

            toast?.let { AppToast(message = it) }

            if (uiState.showConfirm) {
                ConfirmDialog(
                    onDismissRequest = { transactionHistoryDetailVM.onDismissConfirm() },
                    message = "Are you sure you want to refund this transaction?",
                    onSubmitRequest = {
                        transactionHistoryDetailVM.onDismissConfirm()
                        transactionHistoryDetailVM.onRefundTransaction()
                    }
                )
            }
        }

    }
}


//@Preview(showBackground = true, device = Devices.AUTOMOTIVE_1024p, widthDp = 1920, heightDp = 1080)
//@Composable
//fun TransactionHistoryDetailPreview() {
//    SwiftPOSTheme {
//        TransactionHistoryDetail(onDismissRequest = {})
//    }
//}