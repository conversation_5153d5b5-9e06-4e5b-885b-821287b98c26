package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.data.model.LoyaltyState
import com.swiftsku.swiftpos.data.model.maskedId
import com.swiftsku.swiftpos.data.model.title
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.grandTotal
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun ColumnScope.CartSummary(
    modifier: Modifier = Modifier,
    transactionSummary: TransactionSummary,
    coupons: List<Coupon>,
    onCouponRemoveClick: () -> Unit,
    loyaltyState: LoyaltyState,
    dashboardVM: DashboardViewModel
) {
    Card(shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)) {
        Column(
            modifier = modifier
                .weight(1f, true)
                .padding(bottom = 20.dp),
            ) {

            if (loyaltyState.status != null || loyaltyState.loyaltyId != null) {
                LoyaltyLoader(loyaltyState)
            }

            if (transactionSummary.lotteryAmount > 0f) {
                LotteryHeader(dashboardVM)
            }

            if (coupons.isNotEmpty()) {
                CouponHeader(coupons, onCouponRemoveClick)
                Divider()
            }
            Spacer(modifier = Modifier.size(10.dp))
            CartSummaryItem(
                "Sub Total",
                transactionSummary.transactionTotalNetAmount.toDollars()
            )
            CartSummaryItem(
                "Tax",
                transactionSummary.transactionTotalTaxNetAmount.toDollars(),
            )
            if (transactionSummary.lotteryAmount > 0f) {
                CartSummaryItem(
                    title = "Lottery Amount",
                    formattedAmount = (transactionSummary.lotteryAmount * -1).toDollars(),
                    textColor = Teal
                )
            }
            if (transactionSummary.promotionApplied != 0f) {
                CartSummaryItem(
                    title = "Promotion Applied",
                    textColor = Teal,
                    formattedAmount = (transactionSummary.promotionApplied * -1).toDollars()
                )
            }
            if (coupons.isNotEmpty()) {
                CartSummaryItem(
                    "Coupon Amount",
                    (transactionSummary.couponAmount * -1).toDollars(),
                    textColor = Teal,
                )
            }
            if(transactionSummary.pendingAmount() > 0f) {
                CartSummaryItem(
                    "Amount Due",
                    transactionSummary.pendingAmount().toDollars(),
                    textColor = Red
                )

            }
            CartSummaryItem(
                "Grand Total",
                transactionSummary.grandTotal().toDollars(),
                textColor = MaterialTheme.colors.blackAndWhite
            )
            if (transactionSummary.totalAmountCollected() > 0) {
                CartSummaryItem(
                    "Amount Collected",
                    transactionSummary.totalAmountCollected().toDollars(),
                    textColor = MaterialTheme.colors.blackAndWhite
                )
            }
            transactionSummary.calculatedFees.find { it.type == FeeType.CARD_PROCESSING }?.let {
                CartSummaryItem(
                    "Card Fee(${it.info})",
                    it.amount.toDollars()
                )
                CartSummaryItem(
                    "Card Total",
                    transactionSummary.pendingAmount().plus(it.amount).toDollars()
                )
            }
        }
    }
}

@Composable
fun LoyaltyLoader(loyaltyState: LoyaltyState) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(top = 10.dp, start = 30.dp, end = 30.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = loyaltyState.title())
        Spacer(modifier = Modifier.size(10.dp))

        if (loyaltyState.status != null) {
            CircularProgressIndicator(modifier = Modifier.size(24.dp))
        } else if (loyaltyState.loyaltyId != null)
            Text(text = loyaltyState.maskedId())
    }
}


@Composable
fun CartSummaryItem(title: String, formattedAmount: String, textColor: Color = Color.Unspecified) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(top = 6.dp, start = 30.dp, end = 30.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = title, style = MaterialTheme.typography.body1, fontSize = 16.sp, color = textColor)
        Text(text = formattedAmount, color = textColor, fontSize = 16.sp)
    }
}