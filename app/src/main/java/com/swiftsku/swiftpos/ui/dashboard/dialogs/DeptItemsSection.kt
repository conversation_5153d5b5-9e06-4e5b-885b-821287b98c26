package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.UID
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel


@Composable
fun DeptItemsSection(
    onDismiss: () -> Unit,
    selectedDepartment: Department,
    dashboardVM: DashboardViewModel
) {
    val loadings by dashboardVM.loadings.collectAsState()
    val deptPluItems by dashboardVM.deptPluItems.collectAsState()

    Surface(
        modifier = Modifier
            .fillMaxHeight()
            .fillMaxWidth()
            .clickable { onDismiss() },
        color = Color.Black.copy(alpha = 0.8f),
        elevation = 8.dp
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxHeight()
                    .fillMaxWidth(0.36f)
                    .background(Color.White)
                    .clickable(enabled = false) {}
            ) {
                TopAppBar(
                    elevation = 4.dp,
                    title = { Text(selectedDepartment.departmentName) },
                    actions = {
                        IconButton(onClick = { onDismiss() }) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    },
                    backgroundColor = Color.White,
                    contentColor = Color.Black
                )

                SubPluItemsSection(deptPluItems, dashboardVM = dashboardVM)

                if (loadings.contains(DashboardViewModel.Loadings.DEPT_PLU_ITEMS)) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .padding(top = 16.dp),
                    )
                } else {
                    if (deptPluItems.isEmpty()) {
                        Text(
                            "No items found.",
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SubPluItemsSection(
    pluItems: List<PluItem>,
    dashboardVM: DashboardViewModel
) {
    val txnItems by dashboardVM.txnItems.collectAsState()

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
    ) {
        items(pluItems, { it.UID() }) { item ->
            val qty = txnItems.find { txnItem ->
                if (txnItem is TransactionItemWithPLUItem) {
                    txnItem.pluItem.UID() == item.UID() && txnItem.status != TransactionItemStatus.Deleted
                } else false
            }?.quantity
            MenuItem(
                pluItem = item,
                qty = qty,
                onItemClick = { dashboardVM.onPriceBookItemClick(it) },
                modifier = Modifier
            )
        }
    }
}
