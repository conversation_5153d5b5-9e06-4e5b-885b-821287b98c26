package com.swiftsku.swiftpos.ui.dashboard.dialogs

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.dialog
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.swiftsku.swiftpos.data.model.Info
import com.swiftsku.swiftpos.data.model.PinPadConnectionType
import com.swiftsku.swiftpos.data.model.fqmStatusColor
import com.swiftsku.swiftpos.data.model.map
import com.swiftsku.swiftpos.data.model.sgColor
import com.swiftsku.swiftpos.extension.TAIL_SCALE_VPN_GET_STATUS
import com.swiftsku.swiftpos.services.network.NetworkStatus
import com.swiftsku.swiftpos.ui.components.DialogHeader
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.TransparentGrey

@Composable
fun InfoDialog(
    info: Info, onDismiss: () -> Unit,
    onInfoTypeClick: (infoType: String) -> Unit,
    onInfoTitleClick: (infoType: String) -> Unit,
    dashboardVM: DashboardViewModel
) {


    val context = LocalContext.current
    val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()
    val storeConfig by dashboardVM.storeConfig.collectAsState()

    LaunchedEffect(Unit) {
        context.sendBroadcast(Intent(TAIL_SCALE_VPN_GET_STATUS))
    }

    Box(
        contentAlignment = Alignment.CenterEnd,
        modifier = Modifier
            .semantics { dialog() }
            .fillMaxSize(),
    ) {

        Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(TransparentGrey)
                    .zIndex(1f)
                    .weight(2f)
                    .clickable(enabled = false, onClick = {})
            )
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .background(Color.White)
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween
                ) {
                    DialogHeader(title = {
                        Text(text = "Info")
                    }, content = {
                        Row(horizontalArrangement = Arrangement.End) {
                            IconButton(onClick = onDismiss) {
                                Icon(Icons.Default.Close, contentDescription = "Close")
                            }
                        }
                    })

                    Surface(
                        modifier = Modifier
                            .background(GrayBackground)
                            .padding(20.dp)
                            .weight(1f),
                        elevation = 2.dp, shape = RoundedCornerShape(8.dp)
                    ) {
                        LazyColumn(
                            modifier = Modifier
                                .background(Color.White)
                        ) {
                            var toggle = false
                            item {
                                InfoItem(
                                    modifier = Modifier.background(GrayBackground),
                                    left = {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier.weight(1f),
                                        ) {
                                            Text(
                                                text = "Network Status",
                                                color = Color.Black
                                            )
                                        }
                                    },
                                    right = {
                                        Text(
                                            text = info.networkStatus,
                                            color = if (info.networkStatus == NetworkStatus.Connected.name) Teal else Red,
                                            modifier = Modifier.weight(1f),
                                            textAlign = TextAlign.End
                                        )
                                    }
                                )
                            }
                            info.map().forEach { (title, value) ->
                                item {
                                    var valueColor = statusColor(title, info)
                                    var titleText = title
                                    if (title == "Pax Terminal") {
                                        valueColor = if (value == "Connected") {
                                            Teal
                                        } else Red
                                        if (storeLevelConfig?.payfac != "epx") {
                                            return@item
                                        }
                                        storeConfig?.pinpadConfig?.let {
                                            titleText =
                                                "$title\n${it.ctype} ${if (it.ctype == PinPadConnectionType.TCP) it.ip else ""}"
                                        }
                                    }
                                    toggle = !toggle
                                    InfoItem(
                                        modifier = Modifier.background(if (toggle) Color.White else GrayBackground),
                                        left = {
                                            Text(
                                                text = titleText,
                                                modifier = Modifier
                                                    .weight(1f)
                                                    .clickable(onClick = { onInfoTitleClick(title) }),
                                                color = Color.Black
                                            )
                                        },
                                        right = {
                                            Text(
                                                text = value,
                                                modifier = Modifier
                                                    .weight(1f)
                                                    .clickable(onClick = { onInfoTypeClick(title) }),
                                                textAlign = TextAlign.End,
                                                color = valueColor
                                            )
                                        }
                                    )
                                }
                            }
                        }
                    }

                }
            }
        }
    }
}

@Composable
fun InfoItem(
    left: @Composable () -> Unit,
    right: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,

        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 16.dp),
    ) {
        left()
        right()
    }
}

fun statusColor(
    status: String,
    info: Info
): Color =
    if (status.contains("Cloud Status")) {
        info.sgColor
    } else if (status == "FQM Status") {
        info.fqmStatusColor
    } else {
        Color.Black
    }
