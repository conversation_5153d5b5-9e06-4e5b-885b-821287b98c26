package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.OutlinedTextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.text.isDigitsOnly
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.extension.centsToDollarsString
import com.swiftsku.swiftpos.extension.orNil
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.visualizers.DollarVisualTransformation
import com.swiftsku.swiftpos.ui.visualizers.NanpVisualTransformation
import com.swiftsku.swiftpos.utils.ACCOUNT_NAME_LENGTH


@Composable
fun CreditAccountFormDialog(
    creditAccount: CreditAccount?,
    onSaveAccount: (creditAccount: CreditAccount?, name: String, phone: String, address: String, limitInCents: Int) -> Unit,
    onCancel: () -> Unit,
    maxCreditLimitCents: Int
) {
    var name by remember { mutableStateOf(creditAccount?.name.orEmpty()) }
    var phone by remember { mutableStateOf(creditAccount?.phone.orEmpty()) }
    var creditLimit by remember { mutableStateOf("${creditAccount?.creditLimitCents ?: maxCreditLimitCents}") }
    var address by remember { mutableStateOf(creditAccount?.address.orEmpty()) }
    val numericRegex = Regex("[^0-9]")
    val usPhoneRegex = Regex(
        pattern = "^\\+?1?[-.\\s]?\\(?([2-9][0-9]{2})\\)?[-.\\s]?([2-9][0-9]{2})[-.\\s]?([0-9]{4})\$"
    )
    val isPhoneNumberValid = phone.trim().matches(usPhoneRegex)
    val creditLimitCents = creditLimit.toIntOrNull().orNil()
    val isCreditLimitValid = creditLimitCents <= maxCreditLimitCents
    val saveEnabled = name.isNotBlank() && isPhoneNumberValid && isCreditLimitValid

    Dialog(
        onDismissRequest = { onCancel() }, properties = DialogProperties(
            dismissOnBackPress = true, dismissOnClickOutside = true
        )
    ) {
        Surface(modifier = Modifier.width(700.dp)) {
            Column {
                TopAppBar(elevation = 4.dp, title = { Text("New credit account") }, actions = {
                    IconButton(onClick = { onCancel() }) {
                        Icon(Icons.Filled.Clear, null)
                    }
                })
                Row(Modifier.padding(16.dp)) {
                    Column(Modifier.weight(1f)) {
                        Row(horizontalArrangement = Arrangement.SpaceBetween) {
                            OutlinedTextField(
                                value = name,
                                onValueChange = {
                                    if (it.length <= ACCOUNT_NAME_LENGTH) {
                                        name = it
                                    }
                                },
                                modifier = Modifier.weight(1f),
                                label = { Text("Enter account name") },
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(
                                    capitalization = KeyboardCapitalization.Words
                                ),
                                supportingText = {
                                    Text("Max char $ACCOUNT_NAME_LENGTH (${name.length}/$ACCOUNT_NAME_LENGTH)")
                                }
                            )
                            Spacer(modifier = Modifier.width(10.dp))
                            OutlinedTextField(
                                value = creditLimit,
                                onValueChange = {
                                    if (it.isDigitsOnly()) {
                                        creditLimit = it
                                    }
                                },
                                modifier = Modifier.weight(1f),
                                label = { Text("Credit limit") },
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                visualTransformation = DollarVisualTransformation,
                                supportingText = { Text("Max limit ${maxCreditLimitCents.centsToDollarsString()}") }
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        OutlinedTextField(
                            value = phone,
                            onValueChange = {
                                // Remove non-numeric characters.
                                val stripped = numericRegex.replace(it, "")
                                phone = if (stripped.length >= 10) {
                                    stripped.substring(0..9)
                                } else {
                                    stripped
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                            label = { Text("Enter phone number (required)") },
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                            visualTransformation = NanpVisualTransformation()
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        OutlinedTextField(
                            value = address,
                            onValueChange = { address = it },
                            modifier = Modifier.fillMaxWidth(),
                            label = { Text("Enter address (optional)") },
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(
                                capitalization = KeyboardCapitalization.Words
                            )
                        )
                    }
                    Column(
                        Modifier
                            .padding(horizontal = 8.dp)
                            .width(IntrinsicSize.Min)
                    ) {
                        Button(
                            onClick = { onCancel() },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                        ) {
                            Text("Cancel", color = Color.White)
                        }
                        Spacer(Modifier.height(8.dp))
                        Button(
                            onClick = {
                                onSaveAccount(
                                    creditAccount,
                                    name,
                                    phone,
                                    address,
                                    creditLimit.toIntOrNull().orNil()
                                )
                                onCancel()
                            },
                            enabled = saveEnabled,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
                        ) {
                            Text("Save", color = Color.White)
                        }
                    }
                }
            }
        }
    }
}
