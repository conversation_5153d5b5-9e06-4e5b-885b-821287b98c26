package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.pax.poslinksemiintegration.constant.EbtCountType
import com.swiftsku.swiftpos.data.couchbase.credit.CreditRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.RefundInfo
import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.fuelAmount
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.model.getRefundableAmounts
import com.swiftsku.swiftpos.data.model.getTotalRefundAmount
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.openAsync
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject
import com.pax.poslinkadmin.constant.TransactionType as PaxTransactionType


@HiltViewModel
class TxnHistoryDetailViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    @PrinterX private val printerXManager: ISunMiPrinter,
    private val printTransactionUseCase: PrintTransactionUseCase,
    private val userDataSource: UserDataSource,
    private val paymentUseCase: PaymentUseCase,
    private val paxPaymentService: PaxPaymentService,
    private val creditRepository: CreditRepository

) : ViewModel() {

    private val _transaction: MutableStateFlow<Transaction?> = MutableStateFlow(null)
    private val _loading: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val _showConfirm: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    private val _refundState = MutableStateFlow<RefundState?>(null)
    val toastMessage = _toastMessage
    private var epxData: EpxData? = null
    private var epxEbtData: EpxData? = null

    val uiState: StateFlow<TxnHistoryState> =
        combine(
            _transaction,
            _loading,
            _refundState,
            _showConfirm
        ) { transaction, loading, refundState, showConfirm ->
            TxnHistoryState(
                transaction,
                refunding = loading,
                refundState = refundState,
                showConfirm = showConfirm
            )
        }.stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(),
            TxnHistoryState()
        )

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)
    private var storeLevelConfig: StoreUsers? = null


    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    fun updateStoreLevelConfig(config: StoreUsers) = viewModelScope.launch {
        storeLevelConfig = config
    }


    private suspend fun cashierId() = userDataSource.getCurrentUser()?.username ?: "cashierId"


    fun fetchTransaction(transactionId: String) = viewModelScope.launch {
        val transaction = transactionRepository.getTransaction(transactionId)
        _transaction.emit(transaction)
    }

    fun reprint() = viewModelScope.launch {
        try {
            var transaction = _transaction.value

            val storeConfig =
                _storeConfig.value

            if (transaction == null || storeConfig == null) {
                return@launch
            }

            if (transaction is RefundTransaction && transaction.refundInfo.linkedTxnId.isNotEmpty()) {
                val linkedTransaction =
                    transactionRepository.getTransaction(transaction.refundInfo.linkedTxnId)

                if (linkedTransaction is SaleTransaction) {
                    transaction = linkedTransaction
                }
            }
            val printTxnReceiptInput = printTransactionUseCase.getPrintTxnReceiptInput(
                storeConfig, transaction
            )
            printTransactionUseCase(printTxnReceiptInput)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }

    private fun processRefundWithEpx(
        transaction: SaleTransaction, startTime: Date
    ) = viewModelScope.launch {
        val refundableAmounts = getRefundableAmounts(transaction)
        val totalRefundableAmount = refundableAmounts.cardAmount
        val refundAmount = totalRefundableAmount.convertDollarToCentPrecisely()
        paymentUseCase.processEpxCreditRequest(
            PaxPaymentInput(
                transaction.txnId,
                paxPaymentService.getCurrentBatchId(),
                refundAmount.toString(), cashierId(),
                startTime.epochInSeconds(), null,
                PaxTransactionType.RETURN
            ),
            transaction
        ).stateIn(viewModelScope).collectLatest { result ->
            when (result) {
                is Result.Loading -> _loading.emit(true)
                is Result.Error -> {
                    _loading.emit(false)
                    _toastMessage.emit(
                        ToastMessage(message = result.errorMessage, type = ToastMessageType.Error)
                    )
                }

                is Result.Success -> {
                    result.data?.let {
                        epxData = EpxData(
                            transactionType = PaxTransactionType.RETURN,
                            amount = totalRefundableAmount.toFloat(),
                            authorizationCode = it.hostInformation?.authorizationCode,
                            hostResponseMessage = it.hostInformation?.hostResponseMessage,
                            hostResponseNumber = it.hostInformation?.hostReferenceNumber
                        )
                        onRefundTransaction(processedCardRefund = true)
                    }
                    _loading.emit(false)
                }
            }
        }
    }

    private fun processEbtRefundWithEpx(
        transaction: SaleTransaction, startTime: Date
    ) = viewModelScope.launch {
        val refundableAmounts = getRefundableAmounts(transaction)
        val totalRefundableAmount = refundableAmounts.ebtAmount
        val refundAmount = totalRefundableAmount.convertDollarToCentPrecisely()
        val ebtType = transaction.ebtInfo?.ebtType ?: EbtType.FOOD_STAMP
        val ebtCountType: EbtCountType = when (ebtType) {
            EbtType.FOOD_STAMP -> EbtCountType.FOOD_STAMP
            else -> EbtCountType.CASH_BENEFITS
        }
        paymentUseCase.processEpxEbtRequest(
            PaxPaymentInput(
                transaction.txnId,
                paxPaymentService.getCurrentBatchId(),
                refundAmount.toString(), cashierId(),
                startTime.epochInSeconds(), ebtCountType,
                PaxTransactionType.RETURN
            ),
            transaction
        ).stateIn(viewModelScope).collectLatest { result ->
            when (result) {
                is Result.Loading -> _loading.emit(true)
                is Result.Error -> {
                    _loading.emit(false)
                    _toastMessage.emit(
                        ToastMessage(message = result.errorMessage, type = ToastMessageType.Error)
                    )
                }

                is Result.Success -> {
                    result.data?.let {
                        epxEbtData = EpxData(
                            transactionType = PaxTransactionType.RETURN,
                            amount = totalRefundableAmount.toFloat(),
                            authorizationCode = it.hostInformation?.authorizationCode,
                            hostResponseMessage = it.hostInformation?.hostResponseMessage,
                            hostResponseNumber = it.hostInformation?.hostReferenceNumber
                        )
                        onRefundTransaction(processedCardRefund = true, processedEbtRefund = true)
                    }
                    _loading.emit(false)
                }
            }
        }
    }

    private fun refundCashTransaction(
        transaction: SaleTransaction, startTime: Date, refundTxnId: String?
    ) = viewModelScope.launch {
        val refundableAmounts = getRefundableAmounts(transaction)
        val refundAmount = refundableAmounts.cashAmount.toFloat()

        printerXManager.validateCashDrawer(onCashDrawerAvailable = {
            try {
                it.openAsync()
                _refundState.emit(RefundState(startTime, refundAmount, refundTxnId))
            } catch (e: Exception) {
                _toastMessage.emit(
                    ToastMessage(
                        message = "Failed to open cash drawer",
                        type = ToastMessageType.Error
                    )
                )
                EventUtils.recordException(e)
            }
        }, onCashDrawerNotAvailable = {
            _toastMessage.emit(
                ToastMessage(
                    message = "Cash drawer not available",
                    type = ToastMessageType.Error
                )
            )
        })
    }

    fun onRefundTransaction(
        processedCardRefund: Boolean = false,
        processedEbtRefund: Boolean = false,
        processedCreditRefund: Boolean = false,
        processedCashRefund: Boolean = false,
        refundTxnId: String? = null
    ): Job = viewModelScope.launch {
        val storeConfig = _storeConfig.value ?: return@launch
        _transaction.value?.let { transaction ->
            if (transaction is SaleTransaction) {
                val startTime = Date()
                val storeCode = storeConfig.storeCode
                val refundableAmounts = getRefundableAmounts(transaction)
                val totalRefundableAmount = refundableAmounts.getTotalRefundAmount()
                when {
                    refundableAmounts.cardAmount > 0 && !processedCardRefund -> {
                        processRefundWithEpx(transaction, startTime)
                    }

                    refundableAmounts.ebtAmount > 0 && !processedEbtRefund -> {
                        processEbtRefundWithEpx(transaction, startTime)
                    }

                    refundableAmounts.creditAmount > 0 && !processedCreditRefund -> {
                        processCreditRefund(transaction, storeCode)
                    }

                    refundableAmounts.cashAmount > 0 && !processedCashRefund -> {
                        refundCashTransaction(transaction, startTime, refundTxnId)
                    }

                    else -> {
                        saveTransaction(
                            totalRefundableAmount.toFloat(),
                            transaction,
                            storeCode,
                            startTime,
                            refundTxnId
                        )
                    }
                }
            }
        }
    }

    private fun processCreditRefund(transaction: SaleTransaction, storeCode: String) =
        viewModelScope.launch {
            val refundableAmounts = getRefundableAmounts(transaction)
            val creditRefund = refundableAmounts.creditAmount
            (transaction.txnPayment[TxnPaymentType.Credit] as? CreditPayment)?.let {
                val refundTxnId = generateTransactionId(
                    storeCode,
                    _storeConfig.value?.posNumber ?: "1"
                )
                val refunded = creditRepository.createLedgerEntryForRefund(
                    transaction,
                    refundTxnId,
                    it.accountId,
                    creditRefund
                )
                if (refunded) {
                    onRefundTransaction(
                        processedCardRefund = true,
                        processedEbtRefund = true,
                        processedCreditRefund = true,
                        refundTxnId = refundTxnId
                    )
                } else {
                    _toastMessage.emit(
                        ToastMessage(
                            message = "Failed to refund credit amount.",
                            type = ToastMessageType.Error
                        )
                    )
                }
            }
        }

    private fun saveTransaction(
        refundAmount: Float,
        transaction: SaleTransaction,
        storeId: String,
        startTime: Date,
        refundTxnId: String?
    ) = viewModelScope.launch {

        val cashierId = cashierId()

        val fuelAmt = transaction.fuelAmount()
        val txnId = refundTxnId ?: generateTransactionId(
            storeId, _storeConfig.value?.posNumber ?: "1"
        )

        transactionRepository.saveTransaction(
            RefundTransaction(
                txnEndTime = Date(),
                txnStartTime = startTime,
                txnTotalGrandAmount = (transaction.txnTotalGrandAmount - fuelAmt).toFloat()
                    .to2Decimal(),
                txnTotalGrossAmount = (transaction.txnTotalGrossAmount - fuelAmt).toFloat()
                    .to2Decimal(),
                txnTotalNetAmount = (transaction.txnTotalNetAmount - fuelAmt).toFloat()
                    .to2Decimal(),
                txnTotalTaxNetAmount = transaction.txnTotalTaxNetAmount,
                cashierId = cashierId,
                txnStatus = TransactionStatus.Complete,
                statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.Complete),
                txnItems = transaction.txnItems.filter { it !is TransactionItemWithFuel },
                txnId = txnId,
                refundInfo = RefundInfo(
                    linkedTxnId = transaction.txnId,
                    reason = "REFUND",
                    epxData = epxData,
                    epxEbtData = epxEbtData
                ),
                txnPayment = transaction.txnPayment,
            )
        )
        val transactionToUpdate =
            transaction.copy(txnType = TransactionType.SaleRefund)
        transactionRepository.saveTransaction(transactionToUpdate)
        _transaction.emit(transactionToUpdate)
        _toastMessage.emit(
            ToastMessage(
                message = "${refundAmount.toDollars()} refunded successfully",
                type = ToastMessageType.Success
            )
        )
        val printCardReceipt = _storeConfig.value?.receiptInfo?.printCardReceipt
        if (printCardReceipt.isTrue()) {
            reprint()
        }
        epxData = null
        epxEbtData = null
    }

    fun onCashDrawerCloseClick() = viewModelScope.launch {
        printerXManager.validateCashDrawer(onCashDrawerAvailable = {

            val refundState = uiState.value.refundState
            val transaction = _transaction.value

            if (!it.isOpen && refundState != null && transaction is SaleTransaction) {
                onRefundTransaction(
                    processedCardRefund = true,
                    processedEbtRefund = true,
                    processedCreditRefund = true,
                    processedCashRefund = true,
                    refundTxnId = refundState.refundTxnId
                )
                _refundState.emit(null)
            } else {
                _toastMessage.emit(
                    ToastMessage(
                        message = "Please close the cash drawer",
                        type = ToastMessageType.Error
                    )
                )
            }
        }, onCashDrawerNotAvailable = {
            _toastMessage.emit(
                ToastMessage(
                    message = "Cash drawer not available",
                    type = ToastMessageType.Error
                )
            )
        })
    }

    fun onDismissConfirm() = viewModelScope.launch {
        _showConfirm.emit(false)
    }


    fun showConfirmDialog() = viewModelScope.launch {
        _showConfirm.emit(true)
    }

}