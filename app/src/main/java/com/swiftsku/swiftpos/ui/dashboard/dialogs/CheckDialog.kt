package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.core.text.isDigitsOnly
import com.swiftsku.swiftpos.ui.components.AppTextField
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.visualizers.DollarVisualTransformation


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CheckDialog(
    onDismissRequest: () -> Unit,
    onSubmitRequest: (checkNumber: String, checkAmount: Float) -> Unit,
    onSubmitCents: ((checkAmountCents: Int, checkNumber: String) -> Unit)? = null
) {

    var checkAmount by remember { mutableStateOf("") }
    var checkNumber by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismissRequest) {
        Surface(modifier = Modifier
            .padding(20.dp)
            .clip(shape = RoundedCornerShape(8.dp))
            .semantics { testTagsAsResourceId = true }) {
            Column(modifier = Modifier.padding(32.dp)) {
                AppTextField(
                    header = "Check Number (Required)",
                    value = checkNumber,
                    onValueChange = { newValue -> checkNumber = newValue.uppercase() },
                    label = "",
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                )
                Spacer(modifier = Modifier.height(20.dp))
                AppTextField(
                    header = "Check Amount",
                    value = checkAmount,
                    onValueChange = { newValue ->
                        if (newValue.isDigitsOnly()) {
                            checkAmount = newValue
                        }
                    },
                    label = "",
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    visualTransformation = DollarVisualTransformation
                )
                Spacer(modifier = Modifier.height(20.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1f))
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            val priceValue = checkAmount.toIntOrNull()
                            if (priceValue != null && priceValue > 0 && priceValue <= Int.MAX_VALUE / 2) {
                                if (onSubmitCents != null) {
                                    onSubmitCents(priceValue, checkNumber)
                                } else {
                                    onSubmitRequest(checkNumber, priceValue / 100f)
                                }
                                onDismissRequest()
                            }
                        },
                        enabled = (checkAmount.toIntOrNull() ?: 0) > 0 && checkNumber.isNotBlank(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f)
                            .testTag("check_submit")
                    ) {
                        Text(text = "Add")
                    }
                }
            }
        }
    }
}

