package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel

@Composable
fun FuelPumps(
    fuelPumps: List<FuelPumpState>,
    onPumpClick: (FuelPumpState) -> Unit,
    fdcVM: FDCViewModel,
    modifier: Modifier
) {
    if (fuelPumps.isEmpty()) {
        Box(modifier = modifier.height(0.dp).fillMaxWidth())
    } else {
        LazyVerticalGrid(
            modifier = modifier,
            columns = GridCells.Fixed(6),
        ) {
            items(count = fuelPumps.size, { pump -> pump }) { item ->
                val pumpState = fuelPumps[item]
                FuelCardItem(
                    pumpState = pumpState,
                    onClick = { onPumpClick(pumpState) },
                    fdcVM = fdcVM
                )
            }
        }
    }
}