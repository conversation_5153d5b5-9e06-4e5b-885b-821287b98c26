package com.swiftsku.swiftpos.ui.activity.base

import android.content.Context
import android.hardware.display.DisplayManager
import androidx.activity.ComponentActivity
import com.swiftsku.swiftpos.ui.presentation.CustomerPresentation

abstract class BaseActivity : ComponentActivity()

abstract class ActivityWithSecondaryDisplay : BaseActivity()

val ActivityWithSecondaryDisplay.secondaryDisplay: CustomerPresentation?
    get() {
        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val presentationDisplays =
            displayManager.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
        if (presentationDisplays.isNotEmpty()) {
            return CustomerPresentation(this, presentationDisplays[0]).also { it.show() }
        }
        return null
    }