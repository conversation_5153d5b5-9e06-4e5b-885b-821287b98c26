package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ExposedDropdownMenuBox
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.core.text.isDigitsOnly
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.type.PluItemProps
import com.swiftsku.swiftpos.ui.components.AppTextField
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.visualizers.DollarVisualTransformation

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun GUPCDialog(
    onDismissRequest: () -> Unit,
    onSubmitRequest: (PluItem) -> Unit,
    pluItem: PluItem,
    taxRateList: List<Tax> = emptyList(),
    onReportClick: (PluItem) -> Unit,
    showReport: Boolean = true,
    onError: (ToastMessage) -> Unit,
    dashboardVM: DashboardViewModel
) {
    val painter = if (pluItem.imgUrl != null) {
        rememberAsyncImagePainter(
            model = ImageRequest.Builder(LocalContext.current)
                .data(pluItem.imgUrl)
                .build()
        )
    } else {
        rememberAsyncImagePainter(
            model = ImageRequest.Builder(LocalContext.current)
                .data("file:///android_asset/svg/AddItem.svg")
                .decoderFactory(SvgDecoder.Factory())
                .build()
        )
    }

    var inputPrice by remember {
        val price = (pluItem.price * 100).toInt()
        mutableStateOf(if (price > 0) "$price" else "")
    }
    var itemName by remember { mutableStateOf(pluItem.description) }
    var itemModifier by remember { mutableStateOf(pluItem.pluModifier) }

    var taxExpand by remember { mutableStateOf(false) }
    var taxRate by remember { mutableStateOf<Tax?>(null) }

    var deptExpand by remember { mutableStateOf(false) }
    val departments = dashboardVM.departments.filter { it.showInMobileApp }
    val defaultDept = departments.find { it.departmentId == pluItem.merchandiseCode }
    var selectedDept by remember { mutableStateOf(defaultDept) }

    val idCheckAgeOptions = listOf(21, 18)
    var idCheckAge by remember { mutableStateOf<Int?>(null) }
    var idCheckExpand by remember { mutableStateOf(false) }

    val ebtSupported = "EBT supported"
    val ebtNotSupported = "EBT not supported"
    val ebtOptions = listOf(ebtNotSupported, ebtSupported)
    var ebtOptionSelected by remember { mutableStateOf(ebtOptions[0]) }
    var ebtExpand by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        Surface(
            modifier = Modifier
                .padding(16.dp)
//                    .width(500.dp)
                .clip(shape = RoundedCornerShape(8.dp))

        ) {
            Column(
//                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(16.dp)
            ) {

                Box(modifier = Modifier.fillMaxWidth()) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .size(64.dp)
                            .align(Alignment.Center)
                    )
                    if (showReport) {
                        ClickableText(
                            text = AnnotatedString("Report Item"),
                            onClick = { onReportClick(pluItem) },
                            modifier = Modifier.align(Alignment.TopEnd),
                            style = TextStyle(color = Red)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Row(horizontalArrangement = Arrangement.SpaceBetween) {
                    AppTextField(
                        modifier = Modifier.weight(1f),
                        header = "PLU modifier",
                        value = itemModifier,
                        onValueChange = { newValue ->
                            if (newValue.isDigitsOnly() && newValue.length <= 3) {
                                itemModifier = newValue
                            }
                        },
                        label = "Enter modifier",
                    )
                    Spacer(modifier = Modifier.width(10.dp))
                    AppTextField(
                        modifier = Modifier.weight(2f),
                        header = "PLU Id",
                        value = pluItem.pluId,
                        onValueChange = { _ -> },
                        label = "Enter Id",
                        enabled = false
                    )

                }
                Spacer(modifier = Modifier.height(8.dp))

                AppTextField(
                    header = "Item Name",
                    value = itemName,
                    onValueChange = { newValue -> itemName = newValue },
                    label = "Enter Name"
                )
                Spacer(modifier = Modifier.height(8.dp))
                ExposedDropdownMenuBox(
                    expanded = deptExpand,
                    onExpandedChange = {
                        deptExpand = !deptExpand
                    }
                ) {
                    AppTextField(
                        header = "Department",
                        value = selectedDept?.departmentName ?: "",
                        onValueChange = {},
                        label = "Select Department",
                        enabled = false
                    )
                    ExposedDropdownMenu(
                        expanded = deptExpand,
                        onDismissRequest = { deptExpand = false }
                    ) {
                        departments.forEach { item ->
                            DropdownMenuItem(
                                onClick = {
                                    selectedDept = item
                                    deptExpand = false
                                }, content = {
                                    Text(text = item.departmentName)
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    AppTextField(
                        modifier = Modifier.weight(1f),
                        header = "Enter Item Price",
                        value = inputPrice,
                        onValueChange = { newValue ->
                            if (newValue.isDigitsOnly()) {
                                inputPrice = newValue
                            }
                        },
                        label = "Enter Price",
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        visualTransformation = DollarVisualTransformation
                    )
                    Spacer(modifier = Modifier.width(10.dp))

                    ExposedDropdownMenuBox(
                        modifier = Modifier.weight(1f),
                        expanded = taxExpand,
                        onExpandedChange = {
                            taxExpand = !taxExpand
                        }
                    ) {
                        AppTextField(
                            header = "Tax Rate",
                            value = taxRate?.name ?: "",
                            onValueChange = {},
                            label = "Select Tax",
                            enabled = false
                        )

                        ExposedDropdownMenu(
                            expanded = taxExpand,
                            onDismissRequest = { taxExpand = false }
                        ) {
                            taxRateList.forEach { item ->
                                DropdownMenuItem(
                                    onClick = {
                                        taxRate = item
                                        taxExpand = false
                                    }, content = {
                                        Text(text = item.name)
                                    }
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    ExposedDropdownMenuBox(
                        modifier = Modifier.weight(1f),
                        expanded = idCheckExpand,
                        onExpandedChange = { idCheckExpand = !idCheckExpand }
                    ) {
                        AppTextField(
                            header = "ID Check",
                            value = if (idCheckAge != null) "Minimum age $idCheckAge" else "",
                            onValueChange = {},
                            label = "Select minimum age required",
                            enabled = false
                        )
                        ExposedDropdownMenu(
                            expanded = idCheckExpand,
                            onDismissRequest = { idCheckExpand = false }
                        ) {
                            idCheckAgeOptions.forEach { age ->
                                DropdownMenuItem(
                                    onClick = {
                                        idCheckAge = age
                                        idCheckExpand = false
                                    }, content = {
                                        Text(text = "Minimum age $age")
                                    }
                                )
                            }
                        }
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    ExposedDropdownMenuBox(
                        modifier = Modifier.weight(1f),
                        expanded = ebtExpand,
                        onExpandedChange = { ebtExpand = !ebtExpand }
                    ) {
                        AppTextField(
                            header = "EBT option",
                            value = ebtOptionSelected,
                            onValueChange = {},
                            label = "Select if item is EBT supported",
                            enabled = false
                        )
                        ExposedDropdownMenu(
                            expanded = ebtExpand,
                            onDismissRequest = { ebtExpand = false }
                        ) {
                            ebtOptions.forEach { ebtOption ->
                                DropdownMenuItem(
                                    onClick = {
                                        ebtOptionSelected = ebtOption
                                        ebtExpand = false
                                    }, content = {
                                        Text(text = ebtOption)
                                    }
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1f))
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            val priceValue = inputPrice.toIntOrNull() ?: 0
                            if (priceValue <= 0) {
                                onError(ToastMessage("Price must be greater than 0"))
                                return@Button
                            }

                            if (itemName.isEmpty()) {
                                onError(ToastMessage("Item name cannot be empty"))
                                return@Button
                            }
                            val merchandiseCode = when {
                                selectedDept != null -> selectedDept?.departmentId ?: "000"
                                pluItem.merchandiseCode.isEmpty() -> "000"
                                else -> "000"
                            }
                            val props = if (ebtOptionSelected == ebtSupported
                                && !pluItem.props.contains(PluItemProps.FoodStamp)
                            ) {
                                pluItem.props.toMutableList().apply { add(PluItemProps.FoodStamp) }
                            } else pluItem.props
                            onSubmitRequest(
                                pluItem.copy(
                                    price = priceValue / 100f,
                                    description = itemName,
                                    department = selectedDept?.departmentName ?: "",
                                    taxIds = mutableListOf<String>().also {
                                        it.addAll(pluItem.taxIds)
                                        if (taxRate != null) {
                                            it.add(taxRate!!.taxId)
                                        }
                                    },
                                    pluModifier = (if (itemModifier == "") "000" else itemModifier),
                                    merchandiseCode = merchandiseCode,
                                    minimumCustomerAge = idCheckAge ?: 0,
                                    props = props
                                )
                            )
                            onDismissRequest()

                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f)
                    ) {
                        Text(text = "Add Item")
                    }
                }
            }
        }
    }
}

