package com.swiftsku.swiftpos.ui.report

import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import com.swiftsku.swiftpos.data.model.AdHocReport
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EOMReport
import com.swiftsku.swiftpos.data.model.EOYReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReport
import com.swiftsku.swiftpos.data.model.HistoricalShiftReport
import com.swiftsku.swiftpos.data.model.PresetReport
import com.swiftsku.swiftpos.data.model.ShiftReport
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportTimeResult
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.months
import com.swiftsku.swiftpos.utils.years
import java.time.LocalDate
import java.time.LocalTime


data class AdHocState(
    val selectedAdHocStartDate: LocalDate = LocalDate.now(),
    val selectedAdHocEndDate: LocalDate = LocalDate.now(),
    val selectedAdHocStartTime: LocalTime = LocalTime.now(),
    val selectedAdHocEndTime: LocalTime = LocalTime.now(),
    val adHocReport: AdHocReport? = null,
    val currentPage: Int = -1,
    val loader: Boolean = false

)

data class EODState(
    val selectedEODDate: LocalDate = LocalDate.now(),
    val eodReport: EODReport? = null,
    val currentPage: Int = -1,
    val loader: Boolean = false

)

data class EOMState(
    val selectedEOMDate: String = months[LocalDate.now().monthValue - 1],
    val eomReport: EOMReport? = null,
    val currentPage: Int = -1,
    val loader: Boolean = false
)

data class EOYState(
    val selectedEOYDate: String = years[LocalDate.now().year - 2023],
    val eoyReport: EOYReport? = null,
    val currentPage: Int = -1,
    val loader: Boolean = false
)

data class ShiftReportState(
    val loader: Boolean = false,
    val shiftReport: ShiftReport? = null,
    val currentPage: Int = -1
)

data class PresetReportState(
    val selectedPreset: String? = null,
    val presetReport: PresetReport? = null,
    val currentPage: Int = -1,
    val loader: Boolean = false,
    val presets: List<String> = emptyList()
)

data class ReportListDetail(
    val key: String = "",
    val title: String,
    val subtitle: String,
)

data class HistoricalShiftReportItem(
    val shiftId: String,
    val terminal: String,
    val cashierId: String,
    val shiftStart: String,
    val shiftEnd: String,
)

data class HistoricalBatchReportItem(
    val batchId: String,
    val terminal: String,
    val batchStart: String,
    val batchClose: String,
    val batchEndCashier: String
)

fun trimDateString(date: String): String {
    return date.substringBefore('.').replace('T', ' ').replace('Z', ' ')
}

fun HistoricalShiftReportItem.buildListView() = ReportListDetail(
    key = shiftId,
    title = "Terminal: $terminal, Cashier: $cashierId",
    subtitle = "${trimDateString(shiftStart)} - ${trimDateString(shiftEnd)}"
)

fun HistoricalBatchReportItem.buildListView(): ReportListDetail {
    val batchIdParts = batchId.split(":")
    val posBatchId = try {
        if (batchIdParts.size >= 5) {
            "${batchIdParts[2]}:${batchIdParts[3]}:${batchIdParts[4]}"
        } else if (batchIdParts.isNotEmpty()) {
            batchIdParts.takeLast(3).joinToString(":")
        } else batchId
    } catch (ex: Exception) {
        EventUtils.recordException(ex)
        batchId
    }
    return ReportListDetail(
        key = batchId,
        title = "Batch Id: $posBatchId, Closed by: $batchEndCashier",
        subtitle = "${trimDateString(batchStart)} - ${trimDateString(batchClose)}"
    )
}

data class HistoricalReportState(
    val currentPage: Int = -1,
    val shiftListLoader: Boolean = false,
    val selectedShiftStartDate: LocalDate = LocalDate.now(),
    val selectedShiftEndDate: LocalDate = LocalDate.now(),
    val terminals: List<String> = listOf("All Terminals"),
    val selectedTerminal: String = "",
    val cashiers: List<String> = emptyList(),
    val selectedCashier: String = "",
    val historicalShifts: List<HistoricalShiftReportItem> = emptyList(),
    val viewingListScreen: Boolean = true,
    val selectedShiftId: String = "",
    val selectedShiftLoader: Boolean = false,
    val selectedHistoricalShiftReport: HistoricalShiftReport? = null,
)

data class HistoricalBatchState(
    val currentPage: Int = -1,
    val batchListLoader: Boolean = false,
    val selectedBatchStartDate: LocalDate = LocalDate.now(),
    val selectedBatchEndDate: LocalDate = LocalDate.now(),
    val terminals: List<String> = listOf("All Terminals"),
    val selectedTerminal: String = "",
    val historicalBatches: List<HistoricalBatchReportItem> = emptyList(),
    val viewingListScreen: Boolean = true,
    val selectedBatchId: String = "",
    val selectedBatchLoader: Boolean = false,
    val selectedHistoricalBatchReport: HistoricalBatchReport? = null,
)

data class ReportUIState(
    val selectedReportType: ReportType = ReportType.Shift,
    val adHocState: AdHocState = AdHocState(),
    val eodState: EODState = EODState(),
    val eomState: EOMState = EOMState(),
    val eoyState: EOYState = EOYState(),
    val selectTime: ReportTimeResult? = null,
    val selectDate: ReportDateResult? = null,
    val shiftState: ShiftReportState = ShiftReportState(),
    val presetState: PresetReportState = PresetReportState(),
    val historicalShiftState: HistoricalReportState = HistoricalReportState(),
    val historicalBatchState: HistoricalBatchState = HistoricalBatchState(),
    val currentUser: UserDTO? = null,
)
