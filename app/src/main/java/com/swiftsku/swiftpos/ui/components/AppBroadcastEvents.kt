package com.swiftsku.swiftpos.ui.components

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import com.swiftsku.swiftpos.extension.SG_REPLICATOR_STATUS
import com.swiftsku.swiftpos.extension.TAIL_SCALE_VPN_GET_STATUS
import com.swiftsku.swiftpos.extension.TAIL_SCALE_VPN_STATUS

@Composable
fun AppBroadcastEvents(
    onVpnStatus: (status: Boolean) -> Unit,
) {

    val context = LocalContext.current

    LaunchedEffect(Unit) {
        context.sendBroadcast(Intent(TAIL_SCALE_VPN_GET_STATUS))
    }

    SystemBroadcastReceiver(TAIL_SCALE_VPN_STATUS) {
        onVpnStatus(
            it?.getBooleanExtra("status", false) ?: false
        )
    }
}