package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun LotteryPayoutDialog(
    dashboardVM: DashboardViewModel
) {
    val lotteryPayouts = dashboardVM.uiState.collectAsState().value.payout.lotteryPayouts
    Dialog(
        properties = DialogProperties(usePlatformDefaultWidth = false),
        onDismissRequest = { dashboardVM.hideLotteryDetails() },
    ) {
        Surface(
            modifier = Modifier
                .width(450.dp)
                .heightIn(max = 400.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Column {
                    TopAppBar(
                        elevation = 4.dp,
                        title = { Text("Lottery Payouts") },
                        actions = {
                            IconButton(onClick = { dashboardVM.hideLotteryDetails() }) {
                                Icon(Icons.Filled.Clear, null)
                            }
                        }
                    )
                    LazyColumn {
                        items(items = lotteryPayouts) { payout ->
                            LotteryPayoutInfo(payout, dashboardVM = dashboardVM)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun LotteryPayoutInfo(
    payout: LotteryPayout,
    dashboardVM: DashboardViewModel
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(top = 8.dp, bottom = 8.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(
            text = payout.info,
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = payout.amount.toDollars(),
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Spacer(
            modifier = Modifier.width(50.dp)
        )
        Box(
            modifier = Modifier
                .weight(1f)
                .padding(end = 4.dp)
        ) {
            Button(
                onClick = {
                    dashboardVM.onLotteryRemove(payout)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(backgroundColor = Red)
            ) {
                Text(text = "Remove", color = Color.White)
            }
        }
    }
}
