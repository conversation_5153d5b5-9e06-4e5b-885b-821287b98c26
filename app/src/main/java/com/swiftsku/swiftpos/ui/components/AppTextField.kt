package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    header: String,
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    placeholder: @Composable (() -> Unit)? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    enabled: Boolean = true
) {
    Column(modifier = modifier) {
        Text(
            text = header,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.caption
        )
        Spacer(modifier = Modifier.height(5.dp))
        TextField(
            singleLine = true,
            value = value,
            placeholder = placeholder,
            modifier = Modifier.fillMaxWidth().testTag(header),
            keyboardOptions = keyboardOptions,
            onValueChange = onValueChange,
            label = { Text(label) },
            enabled = enabled,
            visualTransformation = visualTransformation
        )
    }
}