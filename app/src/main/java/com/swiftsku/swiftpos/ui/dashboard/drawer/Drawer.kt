package com.swiftsku.swiftpos.ui.dashboard.drawer

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.DrawerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Search
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.dialog
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.swiftsku.swiftpos.data.type.NumPadResult
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.TransparentGrey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun Drawer(
    onLogOutClick: () -> Unit,
    onCashAdjustment: (numPadResult: NumPadResult) -> Unit,
    modifier: Modifier = Modifier,
    drawerState: DrawerState,
    coroutineScope: CoroutineScope,
    fdcVM: FDCViewModel,
    dashboardVM: DashboardViewModel
) {
    val settings by dashboardVM.userSettings.collectAsState()
    val currentUser by dashboardVM.currentUser.collectAsState()
    val unreadNotes by dashboardVM.unreadNotes.collectAsState()
    val badgeCount = unreadNotes.size

    Box(
        modifier = modifier
            .fillMaxSize()
            .semantics { dialog() }
            .background(Color(0xA0B4B4B4)),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxSize()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(TransparentGrey)
                    .zIndex(1f)
                    .weight(3f)
                    .clickable(enabled = false, onClick = {})
            )
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .background(Color.White)
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween
                ) {
                    DrawerHeader(onCloseClick = { coroutineScope.launch { drawerState.close() } })
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "PoS Options",
                        modifier = Modifier.padding(start = 16.dp, end = 16.dp),
                        style = MaterialTheme.typography.body2
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = {
                            coroutineScope.launch { drawerState.close() }
                            dashboardVM.showPbSearchDialog()
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, end = 16.dp)
                    ) {
                        Icon(
                            Icons.Rounded.Search,
                            contentDescription = "Search",
                            modifier = Modifier.size(16.dp),
                            tint = Teal
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(text = "Pricebook Search")
                    }

                    if (settings?.canAdjustCash.isTrue()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Cash Adjustments",
                            modifier = Modifier.padding(start = 16.dp, end = 16.dp),
                            style = MaterialTheme.typography.body2
                        )
                        OutlinedButton(
                            onClick = { onCashAdjustment(NumPadResult.CashWithdrawal) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 16.dp, end = 16.dp)
                        ) {
                            Text(text = "Safe Drop")
                        }
                        OutlinedButton(
                            onClick = { onCashAdjustment(NumPadResult.CashDeposit) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 16.dp, end = 16.dp)
                        ) {
                            Text(text = "Safe Loan")
                        }
                    }

                    if (settings?.canCloseDay.isTrue() || settings?.canManageCredits.isTrue()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Payment",
                            modifier = Modifier.padding(start = 16.dp, end = 16.dp),
                            style = MaterialTheme.typography.body2
                        )
                        if (settings?.canCloseDay.isTrue()) {
                            OutlinedButton(
                                onClick = {
                                    coroutineScope.launch { drawerState.close() }
                                    /**
                                     * Verify that all the fuel pumps are in FDC_READY state
                                     */
                                    if (fdcVM.areAllPumpsReady()) {
                                        dashboardVM.closeDay()
                                    } else {
                                        dashboardVM.showToast(
                                            "Please ensure all pumps are in Ready state",
                                            ToastMessageType.Info
                                        )
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, end = 16.dp)
                            ) {
                                Text(text = "Close Day")
                            }
                        }
                        if (settings?.canManageCredits.isTrue()) {
                            OutlinedButton(
                                onClick = {
                                    coroutineScope.launch { drawerState.close() }
                                    dashboardVM.showCreditAccountsDialog()
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, end = 16.dp)
                            ) {
                                Text(text = "Credit Accounts")
                            }
                        }
                    }

                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Product Updates",
                        modifier = Modifier.padding(start = 16.dp, end = 16.dp),
                        style = MaterialTheme.typography.body2
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = {
                            coroutineScope.launch { drawerState.close() }
                            dashboardVM.showWhatsNew()
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, end = 16.dp)
                    ) {
                        Text(text = "What's New")
                        if (badgeCount > 0) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .size(20.dp)
                                    .background(Color.Red, shape = CircleShape)
                            ) {
                                Text(
                                    text = badgeCount.toString(),
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.weight(1f))
                    Text(
                        text = "Login user: ${currentUser?.username}",
                        modifier = Modifier.padding(start = 16.dp, end = 16.dp),
                        style = MaterialTheme.typography.body2
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = onLogOutClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        border = BorderStroke(1.dp, Color.Red)
                    ) {
                        Text(text = "Log out", color = Color.Red)
                    }
                }
            }
        }
    }

}