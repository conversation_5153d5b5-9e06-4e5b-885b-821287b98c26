package com.swiftsku.swiftpos.ui.dispenser

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.deviceIsDisabled
import com.swiftsku.swiftpos.ui.dashboard.main.state.dialogTitle
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableApprove
import com.swiftsku.swiftpos.ui.dashboard.main.state.disablePrepay
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableMovePrepay
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableUnlock
import com.swiftsku.swiftpos.ui.dashboard.main.state.getFirstOngoingRequest
import com.swiftsku.swiftpos.ui.dashboard.main.state.isFueling
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun FuelPumpDialog(
    onDismissRequest: () -> Unit,
    onViewTransactionsClick: () -> Unit,
    onPresetClick: () -> Unit,
    onResetInGasClick: () -> Unit,
    onPrepayClick: () -> Unit,
    onPrepayClickAmount: (Double) -> Unit,
    onMovePrepayClick: () -> Unit,
    onWatchPumpClick: () -> Unit,
    onReprintClick: () -> Unit,
    onStopClick: () -> Unit,
    onResumeClick: () -> Unit,
    onApprove: () -> Unit,
    onUnlockPump: () -> Unit,
    fpState: FuelPumpState,
    fdcVM: FDCViewModel
) {
    val fdcState by fdcVM.fdcState.collectAsState()
    val ongoingRequest = fdcState.getFirstOngoingRequest(fpState.deviceId)
    val requestLabel = ongoingRequest?.getRequestLabel()
    Dialog(
        onDismissRequest = onDismissRequest
    ) {
        Surface(
            modifier = Modifier
                .width(400.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                Column(
                    horizontalAlignment = Alignment.Start
                ) {
                    Text(
                        text = "Pump: ${fpState.pumpNo} - ${fpState.dialogTitle} ${if (requestLabel != null) "($requestLabel)" else ""}",
                        modifier = Modifier
                            .padding(16.dp),
                        color = MaterialTheme.colors.blackAndWhite
                    )
                    Divider()
                }

                Column(
                    modifier = Modifier
                        .padding(8.dp)
                ) {
                    Row {
                        CardItem(
                            title = "Prepay Pump",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(fpState.disablePrepay),
                            onClick = onPrepayClick,
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Blue
                        )
                        CardItem(
                            title = "Approve",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(fpState.disableApprove),
                            onClick = onApprove,
                            backgroundColor = if (fpState.disableApprove) Color.Gray else Blue
                        )
                        CardItem(
                            title = "Unlock Pump",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(fpState.disableUnlock),
                            onClick = onUnlockPump,
                            backgroundColor = if (fpState.disableUnlock) Color.Gray else Blue
                        )
                    }
                    fpState.fuelSaleTrxState?.let {
                        FuelSummary(state = it)
                    }
                    Row(
                        modifier = Modifier
                            .gesturesDisabled(fpState.disablePrepay)
                    ) {
                        CardItem(
                            title = "$10 Prepay",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onPrepayClickAmount(10.0)
                            },
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                        CardItem(
                            title = "$20 Prepay",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onPrepayClickAmount(20.0)
                            },
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                        CardItem(
                            title = "$30 Prepay",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onPrepayClickAmount(30.0)
                            },
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                    }
                    Row(
                        modifier = Modifier
                            .gesturesDisabled(fpState.disablePrepay)
                    ) {
                        CardItem(
                            title = "$40 Prepay",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onPrepayClickAmount(40.0)
                            },
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                        CardItem(
                            title = "$50 Prepay",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onPrepayClickAmount(50.0)
                            },
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                        CardItem(
                            title = "Rest in Gas",
                            modifier = Modifier.weight(1f),
                            onClick = onResetInGasClick,
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Teal
                        )
                    }
                    Row {
                        CardItem(
                            title = "Preset",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(fpState.disablePrepay),
                            onClick = onPresetClick,
                            backgroundColor = if (fpState.disablePrepay) Color.Gray else Blue
                        )
                        CardItem(
                            title = "Move Prepay",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(fpState.disableMovePrepay),
                            onClick = onMovePrepayClick,
                            backgroundColor = if (fpState.disableMovePrepay) Color.Gray else Blue
                        )
                        CardItem(
                            title = "Watch Pump",
                            modifier = Modifier
                                .weight(1f)
                                .gesturesDisabled(!fpState.isFueling),
                            onClick = onWatchPumpClick,
                            backgroundColor = if (fpState.isFueling) Blue else Color.Gray
                        )
                    }
                    Row {
                        CardItem(
                            title = "View Transactions",
                            modifier = Modifier.weight(1f),
                            onClick = onViewTransactionsClick,
                            backgroundColor = Color.Black
                        )
                        CardItem(
                            title = "Reprint",
                            modifier = Modifier.weight(1f),
                            onClick = onReprintClick,
                            backgroundColor = Blue
                        )
                        CardItem(
                            title = if (fpState.deviceIsDisabled) "Resume" else "Stop",
                            modifier = Modifier.weight(1f),
                            onClick = {
                                if (fpState.deviceIsDisabled) {
                                    onResumeClick()
                                } else {
                                    onStopClick()
                                }
                            },
                            backgroundColor = Red
                        )
                    }
                }

            }
        }
    }
}