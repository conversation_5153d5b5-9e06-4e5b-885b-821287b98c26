package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccessTime
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportTimeResult
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.extension.toTime
import com.swiftsku.swiftpos.ui.components.ContainerBox
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import java.time.LocalDate
import java.time.LocalTime

@Composable
fun DateTimeSelectionMenu(
    startDate: LocalDate?,
    endDate: LocalDate?,
    startTime: LocalTime?,
    endTime: LocalTime?,
    onDateSelect: (ReportDateResult) -> Unit,
    onTimeClick: (ReportTimeResult) -> Unit,
) {
    Column {
        Row {
            ContainerBox(
                title = startDate?.toDate() ?: "Start Date",
                contentDescription = "Start Date",
                modifier = Modifier
                    .clickable {
                        onDateSelect(ReportDateResult.AdHocStartDate)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(115.dp),
                icon = {
                    Icon(
                        Icons.Rounded.DateRange,
                        contentDescription = "Start Date",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
            Spacer(modifier = Modifier.width(4.dp))
            ContainerBox(
                title = startTime?.toTime() ?: "Start Time",
                contentDescription = "Start Time",
                modifier = Modifier
                    .clickable {
                        onTimeClick(ReportTimeResult.AdHocStartTime)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(115.dp),
                icon = {
                    Icon(
                        Icons.Rounded.AccessTime,
                        contentDescription = "Start Time",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Row {
            ContainerBox(
                title = endDate?.toDate() ?: "End Date",
                contentDescription = "End Date",
                modifier = Modifier
                    .clickable {
                        onDateSelect(ReportDateResult.AdHocEndDate)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(115.dp),
                icon = {
                    Icon(
                        Icons.Rounded.DateRange,
                        contentDescription = "End Date",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
            Spacer(modifier = Modifier.width(4.dp))
            ContainerBox(
                title = endTime?.toTime() ?: "End Time",
                contentDescription = "End Time",
                modifier = Modifier
                    .clickable {
                        onTimeClick(ReportTimeResult.AdHocEndTime)
                    }
                    .background(GrayBackground)
                    .padding(4.dp)
                    .width(115.dp),
                icon = {
                    Icon(
                        Icons.Rounded.AccessTime,
                        contentDescription = "End Time",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
    }
}

