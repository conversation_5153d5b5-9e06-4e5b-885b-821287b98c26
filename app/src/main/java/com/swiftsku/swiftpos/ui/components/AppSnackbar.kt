package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material3.Snackbar
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.blackAndWhiteReverse

@Composable
fun AppSnackBar(
    onActionClick: () -> Unit,
    actionLabel: String,
    description: String
) {
    Box(modifier = Modifier.fillMaxSize()) {
        Box(
            modifier = Modifier
                .width(500.dp)
                .padding(20.dp)
                .align(Alignment.TopCenter)
        ) {
            Snackbar(
                action = {
                    TextButton(onClick = onActionClick) {
                        Text(text = actionLabel, color = MaterialTheme.colors.blackAndWhiteReverse)
                    }
                },

                ) {
                Text(text = description, color = MaterialTheme.colors.blackAndWhiteReverse)
            }
        }
    }
}