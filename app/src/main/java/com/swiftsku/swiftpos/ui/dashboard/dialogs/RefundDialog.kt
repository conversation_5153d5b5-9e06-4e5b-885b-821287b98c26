package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ExposedDropdownMenuBox
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.blackAndWhite


val refundReasons = listOf(
    "Damaged or defective item", "Wrong item purchased", "Expired product", "Other"
)

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RefundDialog(
    dashboardVM: DashboardViewModel
) {
    var expanded by remember { mutableStateOf(false) }
    val mopOptions = mutableListOf(TxnPaymentType.Cash.type)
    val (selectedMop, onMopSelected) = remember { mutableStateOf(mopOptions.first()) }
    val (refundReason, onReasonSelected) = remember { mutableStateOf(refundReasons.first()) }
    val uiState by dashboardVM.uiState.collectAsState()
    val loadings by dashboardVM.loadings.collectAsState()
    val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()
    val refundAmount = uiState.transactionSummary.transactionTotalGrandAmount
    if (storeLevelConfig?.payfac == "epx") {
        mopOptions.add(TxnPaymentType.Card.type)
        storeLevelConfig?.ebtOptions?.forEach { mopOptions.add(it.value) }
    }
    Dialog(
        onDismissRequest = { dashboardVM.hideRefundDialog() },
    ) {
        Surface(
            modifier = Modifier
                .padding(20.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(modifier = Modifier.padding(20.dp)) {
                Text(
                    "Refund ${refundAmount.toDollars()}",
                    modifier = Modifier.fillMaxWidth(),
                    style = MaterialTheme.typography.h6,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Spacer(modifier = Modifier.height(16.dp))
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    TextField(
                        modifier = Modifier.fillMaxWidth(),
                        value = refundReason,
                        onValueChange = {},
                        enabled = false,
                        textStyle = TextStyle(color = MaterialTheme.colors.blackAndWhite),
                        label = {
                            Text(
                                "Select Reason",
                                color = MaterialTheme.colors.blackAndWhite
                            )
                        },
                    )
                    ExposedDropdownMenu(
                        modifier = Modifier.fillMaxWidth(),
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        refundReasons.forEach { refundReason ->
                            DropdownMenuItem(
                                onClick = {
                                    expanded = false
                                    onReasonSelected(refundReason)
                                },
                                text = {
                                    Text(
                                        text = refundReason,
                                        color = MaterialTheme.colors.blackAndWhite
                                    )
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(12.dp))
                Text(text = "Select Mode of Refund", color = MaterialTheme.colors.blackAndWhite)
                Row {
                    mopOptions.forEach { txnPaymentType ->
                        Row(
                            Modifier
                                .height(56.dp)
                                .selectable(
                                    selected = (txnPaymentType == selectedMop),
                                    onClick = { onMopSelected(txnPaymentType) },
                                    role = Role.RadioButton
                                )
                                .padding(horizontal = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (txnPaymentType == selectedMop),
                                onClick = null
                            )
                            Text(
                                text = txnPaymentType.replaceFirstChar(Char::titlecase),
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.blackAndWhite,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
                if (loadings.contains(DashboardViewModel.Loadings.REFUNDING)) {
                    Spacer(modifier = Modifier.height(20.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(modifier = Modifier.size(24.dp))
                        Text(
                            text = "Refunding",
                            modifier = Modifier.padding(start = 8.dp),
                            color = MaterialTheme.colors.blackAndWhite
                        )
                    }
                }
                Spacer(modifier = Modifier.height(20.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1f))
                    Button(
                        onClick = { dashboardVM.hideRefundDialog() },
                        enabled = (!loadings.contains(DashboardViewModel.Loadings.REFUNDING)),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            dashboardVM.processRefund(selectedMop, refundReason)
                        },
                        enabled = (!loadings.contains(DashboardViewModel.Loadings.REFUNDING)),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f)
                    ) {
                        Text(text = "Refund")
                    }
                }
            }
        }
    }
}