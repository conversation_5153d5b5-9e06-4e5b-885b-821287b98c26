package com.swiftsku.swiftpos.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.outlined.Done
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import kotlinx.coroutines.delay

@Composable
fun BoxScope.AppToast(
    message: ToastMessage,
    delay: Long = 3000,
) {

    var visible by remember { mutableStateOf(true) }

    LaunchedEffect(true) {
        delay(delay)
        visible = false
    }

    AnimatedVisibility(
        visible = visible,
        modifier = Modifier
            .align(Alignment.TopCenter)
            .padding(top = 30.dp),
        enter = fadeIn(
            // Overwrites the initial value of alpha to 0.4f for fade in, 0 by default
            initialAlpha = 0.4f
        ),
        exit = fadeOut(
            // Overwrites the default animation with tween
            animationSpec = tween(durationMillis = 250)
        )
    ) {
        Surface(
            elevation = 18.dp,
            shape = RoundedCornerShape(16.dp),

            ) {
            Row(modifier = Modifier.padding(12.dp)) {
                Icon(
                    when (message.type) {
                        ToastMessageType.Error -> Icons.Default.Info
                        ToastMessageType.Success -> Icons.Default.Info
                        ToastMessageType.Info -> Icons.Default.Info
                    },
                    null,
                    modifier = Modifier.size(24.dp),
                    tint = when (message.type) {
                        ToastMessageType.Error -> Red
                        ToastMessageType.Success -> Teal
                        ToastMessageType.Info -> Color.Yellow
                    }
                )
                Spacer(modifier = Modifier.width(10.dp))
                Column {
                    Text(text = message.type.message)
                    Text(text = message.message)
                }
            }
        }
    }
}
