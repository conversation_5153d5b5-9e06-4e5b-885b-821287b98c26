package com.swiftsku.swiftpos.ui.components

import android.view.ViewConfiguration
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalViewConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.ui.theme.Blue_Dark
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.utils.CustomViewConfiguration

//
//@Composable
//fun CardItem(
//    backgroundColor: Color = Teal,
//    title: String,
//    onClick: () -> Unit,
//    modifier: Modifier = Modifier
//) {
//    Card(
//        backgroundColor = backgroundColor,
//        modifier = modifier
//            .padding(4.dp)
//            .fillMaxWidth()
//            .clickable(onClick = onClick),
//        elevation = 8.dp,
//    ) {
//        Text(
//            text = title,
//            style = MaterialTheme.typography.subtitle1,
//            color = Color.White,
//            textAlign = TextAlign.Center,
//            maxLines = 1,
//            overflow = TextOverflow.Visible,
//            modifier = Modifier.padding(
//                top = 20.dp,
//                bottom = 20.dp,
//                start = 10.dp,
//                end = 10.dp
//            ),
//            fontSize = 12.sp,
//
//        )
//    }
//}

enum class DetailButtonStyle {
    TopRightRounded, Right33p
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CardItem(
    modifier: Modifier = Modifier,
    backgroundColor: Color = Teal,
    title: String,
    onClick: () -> Unit,
    isMultiline: Boolean = false,
    fontSize: TextUnit = 16.sp,
    onLongClick: (() -> Unit)? = null,
    onDetailClick: (() -> Unit)? = null,
    detailButtonStyle: DetailButtonStyle? = DetailButtonStyle.TopRightRounded
) {
    Card(
        backgroundColor = backgroundColor,
        modifier = modifier
            .padding(4.dp)
            .fillMaxWidth()
            .height(60.dp)
            .let {
                if (onLongClick != null) it.combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                ) else it.clickable(onClick = onClick)
            }
            .testTag(title),
        elevation = 8.dp,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            val paddingModifier =
                if (onDetailClick != null && detailButtonStyle == DetailButtonStyle.Right33p) {
                    Modifier.padding(start = 3.dp, end = 24.dp)
                } else Modifier.padding(3.dp)
            Text(
                text = title,
                style = MaterialTheme.typography.subtitle1,
                color = Color.White,
                textAlign = if (isMultiline) TextAlign.Center else TextAlign.Center,
                maxLines = if (isMultiline) Int.MAX_VALUE else 1,
                overflow = TextOverflow.Clip,
                fontSize = fontSize,
                modifier = paddingModifier
            )
            onDetailClick?.let {
                when (detailButtonStyle) {
                    DetailButtonStyle.TopRightRounded -> {
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .clip(RoundedCornerShape(bottomStart = 16.dp))
                                .background(Color.White.copy(alpha = .12f))
                                .align(Alignment.TopEnd)
                                .clickable { onDetailClick() },
                            contentAlignment = Alignment.Center,
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_forward_24),
                                contentDescription = "See Details",
                                tint = Color.White,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                    DetailButtonStyle.Right33p -> {
                        CompositionLocalProvider(
                            LocalViewConfiguration provides CustomViewConfiguration(
                                ViewConfiguration.get(LocalContext.current)
                            )
                        ) {
                            Box(
                                modifier = Modifier
                                    .width(28.dp)
                                    .fillMaxHeight()
                                    .clip(RoundedCornerShape(bottomStart = 8.dp, topStart = 8.dp))
                                    .background(Blue_Dark)
                                    .align(Alignment.CenterEnd)
                                    .clickable { onDetailClick() },
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = "$",
                                    style = MaterialTheme.typography.h6,
                                    color = Color.White,
                                )
                            }
                        }
                    }

                    null -> {}
                }
            }
        }
    }
}
