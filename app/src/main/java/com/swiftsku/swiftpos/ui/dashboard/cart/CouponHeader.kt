package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal

fun headerTitle(coupons: List<Coupon>): String {
    return if (coupons.isEmpty()) {
        ""
    } else {
        val size = coupons.size
        "$size Coupon${if (size == 1) "" else "s"} applied"
    }
}

@Composable
fun CouponHeader(
    coupons: List<Coupon>,
    onRemoveCoupon: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(top = 10.dp, start = 30.dp, end = 30.dp, bottom = 10.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = headerTitle(coupons), style = MaterialTheme.typography.body1, color = Teal)
        ClickableText(
            text = AnnotatedString("Remove"),
            onClick = { onRemoveCoupon() },
            style = TextStyle(color = Red)
        )

    }
}