package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.TransparentGrey

@Composable
fun Loader(text: String) {
    Box(
        modifier = Modifier
            .background(TransparentGrey)
            .fillMaxSize()
    ) {
        Box(
            modifier = Modifier
                .background(Color.White)
                .padding(16.dp)
                .align(Alignment.Center)
                .clip(shape = RoundedCornerShape(5.dp))
        ) {
            Text(text = text)
        }
    }
}