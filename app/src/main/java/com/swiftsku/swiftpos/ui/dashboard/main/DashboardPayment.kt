package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.swiftsku.swiftpos.data.type.TransactionAction
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.components.DetailButtonStyle
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableCard
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableEBT
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.utils.EventUtils
import kotlin.math.ceil

@Composable
fun DashboardPayment(
    modifier: Modifier, onCashAmountClick: (amount: Float) -> Unit,
    onCardClick: () -> Unit,
    onEBTClick: () -> Unit,
    onNoSaleClick: () -> Unit,
    onRedeemClick: () -> Unit,
    onSaveClick: () -> Unit,
    onVoidClick: () -> Unit,
    onRefundClick: () -> Unit,
    onCashClick: () -> Unit,
    onLoyaltyClick: () -> Unit,
    transactionSummary: TransactionSummary,
    onPayoutClick: () -> Unit,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel,
) {
    Column(modifier = modifier) {
        PaymentDynamicAmountSection(
            onCardClick = onCardClick,
            onEBTClick = onEBTClick,
            onCashClick = onCashClick,
            onCashAmountClick = onCashAmountClick,
            transactionSummary = transactionSummary,
            dashboardVM = dashboardVM,
            fdcVM = fdcVM
        )
        PaymentFixedAmountSection(
            onCashAmountClick = onCashAmountClick,
            onCreditClick = dashboardVM::onCreditClick
        )
        TransactionActionSection(
            onRedeemClick = onRedeemClick,
            onLoyaltyClick = onLoyaltyClick,
            onPayoutClick = onPayoutClick,
            modifier = Modifier.gesturesDisabled(
                transactionSummary.totalAmountCollected() != 0f
            ),
            dashboardVM = dashboardVM
        )
        VoidActionSection(
            onVoidClick = onVoidClick,
            onSaveClick = onSaveClick,
            onNoSaleClick = onNoSaleClick,
            onRefundClick = onRefundClick,
            transactionSummary = transactionSummary
        )
    }
}

@Composable
fun PaymentFixedAmountSection(
    onCashAmountClick: (amount: Float) -> Unit,
    cashList: List<Int> = listOf(5, 10, 20, 50, 100),
    onCreditClick: () -> Unit
) {
    Row(horizontalArrangement = Arrangement.SpaceBetween) {
        cashList.map {
            CardItem(
                modifier = Modifier.weight(1f),
                title = "$$it",
                onClick = {
                    EventUtils.logEvent(
                        EventUtils.Events.FIXED_AMOUNT_CLICK,
                        mapOf(EventUtils.EventProp.AMOUNT to it)
                    )
                    onCashAmountClick(it.toFloat())
                }
            )
        }
        CardItem(
            modifier = Modifier.weight(1f),
            title = "Credit",
            onClick = { onCreditClick() }
        )
    }
}

@Composable
fun PaymentDynamicAmountSection(
    onCardClick: () -> Unit,
    onEBTClick: () -> Unit,
    onCashClick: () -> Unit,
    onCashAmountClick: (amount: Float) -> Unit,
    transactionSummary: TransactionSummary,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
) {
    val total = transactionSummary.pendingAmount()
    val storeConfig by dashboardVM.storeLevelConfig.collectAsState()
    val fdcState by fdcVM.fdcState.collectAsState()
    val list =
        listOf(
            total.toDollars(),
            ceil(total).toDollars(),
        ) + (storeConfig?.mop ?: listOf("Cash", "Card", "EBT"))



    Row(horizontalArrangement = Arrangement.SpaceBetween) {
        list.map {
            CardItem(
                modifier = Modifier
                    .weight(1f)
                    .gesturesDisabled(
                        when (it) {
                            "EBT" -> transactionSummary.disableEBT()
                            "Card" -> transactionSummary.disableCard() || fdcState.disableCard()
                            else -> false
                        }
                    ),
                title = it, onClick = {
                    when (it) {
                        "Cash" -> onCashClick()
                        "Check" -> dashboardVM.onCheckClick()
                        "Card" -> onCardClick()
                        "EBT" -> onEBTClick()
                        else -> {
                            val amount = it.replace("$", "").toFloat()
                            EventUtils.logEvent(
                                EventUtils.Events.DYNAMIC_AMOUNT_CLICK,
                                mapOf(EventUtils.EventProp.AMOUNT to amount)
                            )
                            onCashAmountClick(amount)
                        }
                    }
                },
                onDetailClick = when (it) {
                    "Card" -> dashboardVM::showCardPaymentDialog
                    "EBT" -> dashboardVM::showEbtPaymentDialog
                    else -> null
                },
                detailButtonStyle = DetailButtonStyle.Right33p
            )
        }
    }
}


@Composable
fun TransactionActionSection(
    onRedeemClick: () -> Unit,
    onLoyaltyClick: () -> Unit,
    onPayoutClick: () -> Unit,
    modifier: Modifier = Modifier,
    dashboardVM: DashboardViewModel
) {
    val storeConfig by dashboardVM.storeConfig.collectAsState()
    val actions = listOf(
        TransactionAction.Loyalty,
        TransactionAction.Redeem,
        TransactionAction.Payout
    ).filterAndMoveHiddenToEnd(storeConfig?.hideButtons)
    Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = modifier) {
        actions.map {
            it?.let {
                CardItem(
                    modifier = Modifier.weight(1f),
                    backgroundColor = Blue,
                    title = it.value,
                    onClick = when (it) {
                        TransactionAction.Redeem -> onRedeemClick
                        TransactionAction.Loyalty -> onLoyaltyClick
                        TransactionAction.Payout -> onPayoutClick
                        else -> fun() {}
                    }
                )
            } ?: run {
                Box(modifier = Modifier.weight(1f))
            }
        }
    }
}

val list = listOf(
    TransactionAction.Refund,
    TransactionAction.Save,
    TransactionAction.NoSale,
    TransactionAction.Void
)

@Composable
fun VoidActionSection(
    onVoidClick: () -> Unit,
    onSaveClick: () -> Unit,
    onNoSaleClick: () -> Unit,
    onRefundClick: () -> Unit,
    transactionSummary: TransactionSummary
) {
    Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxWidth()) {
        list.map {
            CardItem(
                backgroundColor = if (it == TransactionAction.Void || it == TransactionAction.Refund) Red else Blue,
                onClick = when (it) {
                    TransactionAction.Void -> onVoidClick
                    TransactionAction.Save -> onSaveClick
                    TransactionAction.NoSale -> onNoSaleClick
                    TransactionAction.Refund -> onRefundClick
                    else -> fun() {}
                },
                title = it.value,
                modifier = Modifier
                    .weight(1f)
                    .gesturesDisabled(
                        if (it == TransactionAction.NoSale) {
                            transactionSummary.totalAmountCollected() != 0f
                        } else {
                            false
                        }
                    )
            )
        }
    }
}


fun List<TransactionAction>.filterAndMoveHiddenToEnd(hideList: List<String>?): List<TransactionAction?> {
    val visible = filterNot { hideList?.contains(it.value).isTrue() }
    val hiddenCount = size - visible.size
    return visible + List(hiddenCount) { null }
}