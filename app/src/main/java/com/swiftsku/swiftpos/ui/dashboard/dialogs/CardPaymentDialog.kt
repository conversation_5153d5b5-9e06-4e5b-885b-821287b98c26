package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Backspace
import androidx.compose.material.icons.outlined.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.isNumeric
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.ui.theme.blackAndWhiteReverse
import com.swiftsku.swiftpos.ui.visualizers.DollarVisualTransformation
import kotlin.math.roundToInt


@OptIn(ExperimentalMaterialApi::class)
@Composable
fun CardPaymentDialog(
    header: String = "Card Payment",
    amountDue: Float,
    isForEbt: Boolean = false,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
) {
    var value by remember { mutableStateOf("") }
    var offsetX by remember { mutableFloatStateOf(1920 / 3f) }
    var offsetY by remember { mutableFloatStateOf(1080 / 10f) }

    val enteredAmount = (value.toFloatOrNull() ?: 0f) / 100f

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xA0B4B4B4))
    ) {
        Surface(
            modifier = Modifier
                .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                .width(350.dp)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        change.consume()
                        offsetX += dragAmount.x
                        offsetY += dragAmount.y
                    }
                }
        ) {
            Column(
                modifier = Modifier.padding(8.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = header,
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.h6,
                        color = MaterialTheme.colors.blackAndWhite
                    )
                    IconButton(onClick = { dashboardVM.hideCardPaymentDialog() }) {
                        Icon(
                            Icons.Outlined.Clear,
                            contentDescription = null,
                            tint = MaterialTheme.colors.blackAndWhite
                        )
                    }
                }
                Spacer(modifier = Modifier.height(10.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(start = 4.dp),
                ) {
                    Text(
                        text = "Amount Due",
                        color = MaterialTheme.colors.blackAndWhite,
                        style = MaterialTheme.typography.subtitle1,
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = amountDue.toDollars(),
                        color = Red,
                        style = MaterialTheme.typography.subtitle1,
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = {
                            value = amountDue.toDouble().convertDollarToCentPrecisely().toString()
                        },
                        contentPadding = PaddingValues(0.dp),
                        modifier = Modifier.height(24.dp),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Blue)
                    ) {
                        Text(
                            text = "Select",
                            color = Color.White,
                            style = MaterialTheme.typography.subtitle1
                        )
                    }
                }
                Spacer(modifier = Modifier.height(4.dp))
                TextField(
                    value = value,
                    enabled = false,
                    onValueChange = { value = it },
                    visualTransformation = DollarVisualTransformation,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 4.dp, end = 4.dp),
                    trailingIcon = {
                        IconButton(onClick = {
                            if (value.isNotEmpty()) {
                                value = value.removeRange(value.length - 1, value.length)
                            }
                        }) {
                            Icon(
                                Icons.Outlined.Backspace,
                                contentDescription = null,
                                tint = Color.DarkGray
                            )
                        }
                    },
                    textStyle = MaterialTheme.typography.h6,
                    colors = TextFieldDefaults.textFieldColors(
                        textColor = MaterialTheme.colors.blackAndWhite,
                        disabledTextColor = MaterialTheme.colors.blackAndWhite,
                        backgroundColor = GrayBackground,
                        disabledIndicatorColor = MaterialTheme.colors.blackAndWhiteReverse

                    )
                )
                Spacer(modifier = Modifier.height(10.dp))

                LazyVerticalGrid(columns = GridCells.Fixed(3), content = {
                    items(numList.size) { index ->
                        Card(
                            onClick = {
                                val result = value + numList[index]
                                if (result.isNumeric() && result.length < 15) {
                                    value = result
                                }
                            },
                            backgroundColor = Color.Black,
                            modifier = Modifier
                                .padding(4.dp)
                                .fillMaxWidth()
                                .testTag("numeric_pad_${numList[index]}"),
                            elevation = 8.dp,
                        ) {
                            Text(
                                text = numList[index],
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(16.dp), color = Color.White,
                                style = MaterialTheme.typography.h6
                            )
                        }
                    }
                })
                Spacer(modifier = Modifier.height(10.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Button(
                        onClick = { dashboardVM.hideCardPaymentDialog() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(3f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(
                            text = "Cancel",
                            color = Color.White,
                            style = MaterialTheme.typography.h6
                        )
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            if (isForEbt) {
                                dashboardVM.onEBTClick(enteredAmount)
                            } else {
                                dashboardVM.onCardClick(enteredAmount, fdcVM.fdcState.value)
                            }
                        },
                        enabled = enteredAmount > 0 && enteredAmount <= amountDue,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(2f)
                            .testTag("numeric_pad_ok")
                    ) {
                        Text(text = "Pay", style = MaterialTheme.typography.h6)
                    }
                }
            }
        }
    }
}
