package com.swiftsku.swiftpos.ui.dispenser.transactions

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.fuelAmount
import com.swiftsku.swiftpos.data.model.fuelVolume
import com.swiftsku.swiftpos.data.model.hasCardPayment
import com.swiftsku.swiftpos.data.model.isEpxCaptured
import com.swiftsku.swiftpos.data.model.paymentMode
import com.swiftsku.swiftpos.data.model.saleOrigin
import com.swiftsku.swiftpos.data.model.showRecall
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.to3DecimalString
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.blackAndWhite


@Composable
fun PendingFuelTxnItem(
    fuelTransaction: SaleTransaction, dashboardVM: DashboardViewModel
) {
    val cardPayment = (fuelTransaction.txnPayment[TxnPaymentType.Card] as? CardPayment)
    val epxCaptured = cardPayment.isEpxCaptured()
    val recallButtonLabel =
        if (fuelTransaction.hasCardPayment() && !epxCaptured) "Retry Payment" else "Recall"

    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(top = 8.dp, bottom = 8.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(
            text = fuelTransaction.txnId,
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = fuelTransaction.txnStartTime.toDateTime(),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = fuelTransaction.saleOrigin(),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = fuelTransaction.paymentMode(),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = fuelTransaction.fuelVolume.to3DecimalString(),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = fuelTransaction.fuelAmount().toDollars(),
            modifier = Modifier
                .weight(1f)
                .padding(end = 16.dp),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.End
        )

        Box(
            modifier = Modifier
                .weight(1.2f)
                .padding(end = 4.dp)
        ) {
            if (fuelTransaction.showRecall) {
                Button(
                    onClick = {
                        if (fuelTransaction.hasCardPayment() && !epxCaptured) {
                            dashboardVM.retryCardPayment(fuelTransaction)
                        } else {
                            dashboardVM.clearPendingFuelTxnFromUi()
                            dashboardVM.recallTransaction(fuelTransaction)
                        }
                    }, modifier = Modifier
                        .fillMaxWidth()
                        .padding(2.dp)
                ) {
                    Text(text = recallButtonLabel, maxLines = 1, overflow = TextOverflow.Ellipsis)
                }
            }
        }
        Box(
            modifier = Modifier
                .weight(1.2f)
                .padding(start = 4.dp)
        ) {
            Button(
                onClick = {
                    dashboardVM.closePendingTransactions(listOf(fuelTransaction))
                }, colors = ButtonDefaults.buttonColors(backgroundColor = Blue)
            ) {
                Text(text = "Auto Complete", color = Color.White)
            }
        }
    }
}