package com.swiftsku.swiftpos.ui.dashboard.txnhistory

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.LocalTime
import javax.inject.Inject


@HiltViewModel
class TxnHistoryViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository
) : ViewModel() {

    private val _newUiState = MutableStateFlow(TransactionHistoryUiState())
    val newUiState: StateFlow<TransactionHistoryUiState> = _newUiState

    private fun loadTransactions() {
        _newUiState.update { it.copy(isLoading = true) }
        viewModelScope.launch {
            val state = _newUiState.value

            val result = withContext(Dispatchers.IO) {
                transactionRepository.getTransactions(
                    searchQuery = state.query,
                    startDate = state.selectedStartDate,
                    startTime = state.selectedStartTime,
                    endDate = state.selectedEndDate,
                    endTime = state.selectedEndTime,
                    limit = state.limit,
                    offset = state.page * state.limit,
                    transactionStatus = state.txnStatus
                )
            }

            _newUiState.update {
                it.copy(
                    transactions = result.distinctBy { it.txnId },
                    isLoading = false,
                    page = 1,
                    endReached = result.isEmpty()
                )
            }
        }
    }

    fun loadNextPage() {
        if (_newUiState.value.endReached || _newUiState.value.isPaginating) return

        _newUiState.update { it.copy(isPaginating = true) }
        viewModelScope.launch {
            val state = _newUiState.value
            val result = withContext(Dispatchers.IO) {
                transactionRepository.getTransactions(
                    searchQuery = state.query,
                    startDate = state.selectedStartDate,
                    startTime = state.selectedStartTime,
                    endDate = state.selectedEndDate,
                    endTime = state.selectedEndTime,
                    limit = state.limit,
                    offset = state.page * state.limit,
                    transactionStatus = state.txnStatus
                )
            }
            _newUiState.update {
                it.copy(
                    transactions = (it.transactions + result).distinctBy { it.txnId },
                    isPaginating = false,
                    page = it.page + 1,
                    endReached = result.isEmpty()
                )
            }
        }
    }

    fun onStartDateAvailable(startDate: LocalDate) {
        _newUiState.update {
            it.copy(
                selectedStartDate = startDate,
                page = 0,
                transactions = emptyList(),
                endReached = false
            )
        }
        loadTransactions()
    }

    fun onEndDateAvailable(endDate: LocalDate) {
        _newUiState.update {
            it.copy(
                selectedEndDate = endDate,
                page = 0,
                transactions = emptyList(),
                endReached = false
            )
        }
        loadTransactions()
    }

    fun onStartTimeAvailable(startTime: LocalTime) {
        _newUiState.update {
            it.copy(
                selectedStartTime = startTime,
                page = 0,
                transactions = emptyList(),
                endReached = false
            )
        }
        loadTransactions()
    }

    fun onEndTimeAvailable(endTime: LocalTime) {
        _newUiState.update {
            it.copy(
                selectedEndTime = endTime,
                page = 0,
                transactions = emptyList(),
                endReached = false
            )
        }
        loadTransactions()
    }

    fun showDatePicker(type: DatePickerType) {
        _newUiState.update { it.copy(showDatePicker = type) }
    }

    fun showTimePicker(type: TimePickerType) {
        _newUiState.update { it.copy(showTimePicker = type) }
    }

    fun hideDatePicker() {
        _newUiState.update { it.copy(showDatePicker = null) }
    }

    fun hideTimePicker() {
        _newUiState.update { it.copy(showTimePicker = null) }
    }

    fun onSearchQueryChange(query: String) {
        _newUiState.update { it.copy(query = query) }
    }

    fun onDebounceSearch(query: String) {
        _newUiState.update {
            it.copy(query = query, page = 0, transactions = emptyList(), endReached = false)
        }
        loadTransactions()
    }

    fun clearFilter() {
        _newUiState.update { TransactionHistoryUiState() }
        loadTransactions()
    }
}