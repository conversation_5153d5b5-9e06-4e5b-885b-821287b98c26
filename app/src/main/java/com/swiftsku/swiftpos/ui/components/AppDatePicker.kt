package com.swiftsku.swiftpos.ui.components

import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.type.ReportDateResult
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDatePicker(
    requiredDateResult: ReportDateResult,
    datePickerState: DatePickerState,
    onDateChange: (selectedDate: LocalDate, ReportDateResult) -> Unit,
    onDismissDialog: () -> Unit,
) {
    DatePickerDialog(
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        ),
        onDismissRequest = onDismissDialog,
        confirmButton = {
            TextButton(onClick = {
                datePickerState.selectedDateMillis?.let {
                    onDateChange(
                        Instant
                            .ofEpochMilli(it)
                            .atOffset(ZoneOffset.UTC)
                            .toLocalDate(),
                        requiredDateResult
                    )
                    onDismissDialog()
                }
            }) {
                Text(text = "Confirm")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismissDialog
            ) {
                Text(text = "Cancel")
            }
        }
    ) {
        DatePicker(state = datePickerState)
    }

}
