package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.LedgerDirection
import com.swiftsku.swiftpos.data.model.LedgerEntry
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.centsToDollarsString
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.utils.Result
import com.swiftsku.swiftpos.utils.Result.Error
import com.swiftsku.swiftpos.utils.Result.Loading
import com.swiftsku.swiftpos.utils.Result.Success
import com.swiftsku.swiftpos.utils.formatAsUSPhoneNumber
import kotlinx.coroutines.launch
import java.util.Date


@Composable
fun AccountDetail(
    account: CreditAccount,
    onAddFundsClick: () -> Unit,
    onEditClick: () -> Unit,
    openTxnDetail: (txnId: String) -> Unit,
    ledgerEntries: Result<List<LedgerEntry>>?
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    var previousListSize by remember { mutableIntStateOf(0) }

    // Scroll to top when new entries are added
    LaunchedEffect(ledgerEntries) {
        if (ledgerEntries is Success) {
            val currentSize = ledgerEntries.data.size
            if (previousListSize in 1 until currentSize) {
                // New entries were added, scroll to top
                coroutineScope.launch {
                    listState.animateScrollToItem(0)
                }
            }
            previousListSize = currentSize
        }
    }

    Row(
        Modifier
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(8.dp)
    ) {
        Column(Modifier.weight(1f)) {
            Text("Outstanding", fontSize = 12.sp)
            Text(
                account.currentOutstandingCents.centsToDollarsString(),
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        }
        Spacer(modifier = Modifier.width(8.dp))
        Button(
            onClick = { onAddFundsClick() },
            colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
        ) {
            Text("Add Funds")
        }
    }
    Spacer(modifier = Modifier.height(8.dp))
    Row(
        Modifier
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(8.dp)
    ) {
        Column(Modifier.weight(1f)) {
            Row {
                Column(Modifier.weight(1f)) {
                    Text("Name", fontSize = 12.sp)
                    Text(
                        account.name, fontWeight = FontWeight.Bold, color = Color.Black
                    )
                }
                Column(Modifier.weight(1f)) {
                    Text("Phone", fontSize = 12.sp)
                    Text(
                        formatAsUSPhoneNumber(account.phone),
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                }
                Column(Modifier.weight(1f)) {
                    Text("Credit Limit", fontSize = 12.sp)
                    Text(
                        account.creditLimitCents.centsToDollarsString(),
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                }
                Column(Modifier.weight(1f)) {
                    Text("Created At", fontSize = 12.sp)
                    Text(
                        Date(account.createdAt).toDateTime(),
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                }
            }
            Spacer(modifier = Modifier.width(4.dp))
            Column {
                Text("Address", fontSize = 12.sp)
                Text(
                    account.address.orEmpty(), fontWeight = FontWeight.Bold, color = Color.Black
                )
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
        Button(
            onClick = { onEditClick() },
            colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
        ) {
            Text("Edit")
        }
    }
    Spacer(modifier = Modifier.height(8.dp))
    Column(
        Modifier
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(8.dp)
    ) {
        LedgerHeader()
        Divider()
        when (ledgerEntries) {
            null -> {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No entries",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            is Loading -> {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            is Error -> {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = ledgerEntries.errorMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            is Success -> {
                val results = ledgerEntries.data
                results.let {
                    if (it.isEmpty()) {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "No entries",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    } else {
                        LazyColumn(state = listState) {
                            itemsIndexed(
                                items = it,
                                key = { _, item -> item.txnId }) { index, entry ->
                                LedgerRow(
                                    ledgerEntry = entry,
                                    background = if (index % 2 == 0) GrayBackground else Color.Transparent,
                                    openTxnDetail = openTxnDetail
                                )
                            }
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
fun LedgerHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 8.dp)
    ) {
        Text(
            text = "Transaction ID",
            modifier = Modifier.weight(0.2f),
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Date", modifier = Modifier.weight(0.2f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Type", modifier = Modifier.weight(0.2f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "MoP", modifier = Modifier.weight(0.1f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Note", modifier = Modifier.weight(0.2f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Amount",
            modifier = Modifier.weight(0.1f),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End
        )
    }
}

@Composable
fun LedgerRow(
    ledgerEntry: LedgerEntry, background: Color, openTxnDetail: (txnId: String) -> Unit
) {
    val amountColor = if (ledgerEntry.ledgerDirection == LedgerDirection.CREDIT) Teal else LocalContentColor.current
    val sign = if (ledgerEntry.ledgerDirection == LedgerDirection.CREDIT) "-" else "+"
    Row(
        Modifier
            .fillMaxWidth()
            .background(background)
            .padding(horizontal = 8.dp, vertical = 8.dp)
            .clickable { openTxnDetail(ledgerEntry.txnId) },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = ledgerEntry.txnId, modifier = Modifier.weight(0.2f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(text = Date(ledgerEntry.txnTime).toDateTime(), modifier = Modifier.weight(0.2f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(text = ledgerEntry.ledgerReason.value, modifier = Modifier.weight(0.2f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = ledgerEntry.fundMop?.toString()?.replaceFirstChar(Char::titlecase) ?: "",
            modifier = Modifier.weight(0.1f),
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = if (ledgerEntry.fundMop == TxnPaymentType.Cheque) {
                "Cheque No: ${ledgerEntry.notes}"
            } else {
                ledgerEntry.notes.toString()
            },
            modifier = Modifier.weight(0.2f),
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "$sign${ledgerEntry.amountCents.centsToDollarsString()}",
            modifier = Modifier.weight(0.1f),
            textAlign = TextAlign.End,
            color = amountColor
        )
    }
}