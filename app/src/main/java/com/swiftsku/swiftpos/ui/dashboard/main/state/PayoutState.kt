package com.swiftsku.swiftpos.ui.dashboard.main.state

import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.Payout

data class PayoutState(
    val showPayoutDialog: Boolean = false,
    val enableVendor: Boolean = false,
    val enableLottery: Boolean = false,
    val payout: Payout? = null,
    val lotteryNames: List<String> = emptyList(),
    val vendorNames: List<String> = emptyList(),
    val lotteryPayouts: List<LotteryPayout> = emptyList()
)
