package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.TxnDiscount
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.theme.blackAndWhite


@Composable
fun TransactionSummaryDetails(
    subTotal: Float = 0f,
    tax: Float = 0f,
    total: Float = 0f,
    promotion: Float = 0f,
    lotteryPayout: LotteryPayout? = null,
    lotteryPayouts: List<LotteryPayout>?= null,
    txnDiscount: TxnDiscount? = null
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.fillMaxWidth()
    ) {
        Box(modifier = Modifier.weight(3f))

        Column(
            horizontalAlignment = Alignment.End,
            modifier = Modifier.weight(1f),
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
            ) {
                Text(
                    text = "Sub Total ",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Text(
                    text = subTotal.toDollars(), style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)

            ) {
                Text(
                    text = "Tax ", style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Text(
                    text = tax.toDollars(), style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                Text(
                    text = "PROMOTION ", style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Text(
                    text = promotion.toDollars(), style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                Text(
                    text = "TOTAL ", style = MaterialTheme.typography.body1,
                    color = MaterialTheme.colors.blackAndWhite
                )
                Text(
                    text = total.toDollars(), style = MaterialTheme.typography.body1,
                    color = MaterialTheme.colors.blackAndWhite
                )
            }
            lotteryPayout?.let {
                if (it.amount > 0) {
                    LotteryPayoutInfo(it)
                }
            }
            // Show lottery payout if paid through lottery
            lotteryPayouts?.let {
                LotteryPayoutInfo(it)
            }
            // Show applied coupon info
            txnDiscount?.let {
                DiscountInfo(it)
            }
        }
    }
}

@Composable
fun LotteryPayoutInfo(lotteryPayout: LotteryPayout) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 8.dp)
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "Lottery winning:\n${lotteryPayout.info}",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.blackAndWhite,
                textAlign = TextAlign.Start
            )
        }
        Text(
            text = "-${lotteryPayout.amount.toDollars()}",
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.End
        )
    }
}

@Composable
fun LotteryPayoutInfo(lotteryPayouts: List<LotteryPayout>) {
    lotteryPayouts.forEach { item ->
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "Lottery winning:\n${item.info}",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite,
                    textAlign = TextAlign.Start
                )
            }
            Text(
                text = "-${item.amount.toDollars()}",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.blackAndWhite,
                textAlign = TextAlign.End
            )
        }
    }
}

@Composable
fun DiscountInfo(txnDiscount: TxnDiscount) {
    txnDiscount.coupon.forEach { item ->
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "Coupon:\n${item.name}",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.blackAndWhite,
                    textAlign = TextAlign.Start
                )
            }
            Text(
                text = "-${item.amount.toDollars()}", style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.blackAndWhite,
                textAlign = TextAlign.End
            )
        }
    }
}