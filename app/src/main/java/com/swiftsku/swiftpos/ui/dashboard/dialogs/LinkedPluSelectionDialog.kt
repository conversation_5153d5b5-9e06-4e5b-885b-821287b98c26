package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Checkbox
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite


@Composable
fun LinkedPluSelectionDialog(dashboardVM: DashboardViewModel) {
    val uiState by dashboardVM.uiState.collectAsState()
    val requiredPluIds = uiState.linkedPluData
        .filter { it.required }
        .map { it.pluId to it.pluModifier }
    val requiredPlus = uiState.linkedPluItems
        .filter { item -> requiredPluIds.contains(item.pluId to item.pluModifier) }
        .toSet()
    val selectedItems = remember { mutableStateOf(requiredPlus) }

    fun onItemChecked(checked: Boolean, item: PluItem) {
        if (requiredPluIds.contains(item.pluId to item.pluModifier)) {
            return
        }
        selectedItems.value = if (checked) {
            selectedItems.value + item
        } else {
            selectedItems.value - item
        }
    }

    Dialog(
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        ),
        onDismissRequest = { dashboardVM.clearLinkedPlus() },
    ) {
        Surface(
            modifier = Modifier.clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Box(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = "Add items to cart",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.h6,
                        color = MaterialTheme.colors.blackAndWhite,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                Divider(color = Color.DarkGray)
                Box(
                    modifier = Modifier
                        .heightIn(max = 300.dp)
                        .wrapContentHeight()
                        .fillMaxWidth()
                ) {
                    LazyColumn {
                        items(uiState.linkedPluItems) { item ->
                            PluSelectionItem(
                                item, requiredPluIds, selectedItems.value, ::onItemChecked
                            )
                        }
                    }
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Spacer(modifier = Modifier.height(30.dp))
                    Button(
                        modifier = Modifier.padding(start = 10.dp),
                        onClick = { dashboardVM.addSelectedPlus(selectedItems.value.toList()) },
                        colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
                    ) {
                        Text(
                            text = if (selectedItems.value.isEmpty()) "Skip" else "Add",
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}


@Composable
fun PluSelectionItem(
    item: PluItem,
    requiredPluIds: List<Pair<String, String>>,
    selectedItems: Set<PluItem>,
    onItemChecked: (checked: Boolean, item: PluItem) -> Unit
) {
    val optionalTag = if (requiredPluIds.contains(item.pluId to item.pluModifier)) {
        "(Required)"
    } else {
        "(Optional)"
    }
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(start = 8.dp, end = 8.dp, top = 4.dp, bottom = 4.dp)
    ) {
        Checkbox(
            checked = selectedItems.contains(item),
            enabled = !requiredPluIds.contains(item.pluId to item.pluModifier),
            onCheckedChange = { onItemChecked(it, item) }
        )
        ClickableText(
            text = AnnotatedString("${item.description} $optionalTag"),
            onClick = { onItemChecked(!selectedItems.contains(item), item) },
            modifier = Modifier
                .padding(start = 16.dp)
                .weight(1f),
            style = MaterialTheme.typography.subtitle1.copy(
                color = MaterialTheme.colors.blackAndWhite
            )
        )
    }
}