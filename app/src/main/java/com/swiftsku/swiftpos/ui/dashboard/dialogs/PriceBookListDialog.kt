package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun PriceBookListDialog(
    priceBook: List<PluItem> = emptyList(),
    onDismissRequest: () -> Unit,
    onItemClick: (data: PluItem) -> Unit
) {

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xA0B4B4B4)),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .padding(vertical = 100.dp)
                .background(Color.White)
                .width(800.dp),
        ) {
            Column {
                TopAppBar(
                    backgroundColor = GrayBackground,
                    elevation = 4.dp,
                    title = {
                        Text("PLU Items")
                    },
                    actions = {
                        IconButton(onClick = onDismissRequest) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    })

                PriceBookHeader()
                LazyColumn {
                    items(priceBook) { item ->
                        PriceBookListItem(
                            description = item.description,
                            barcode = item.pluId,
                            modifier = item.pluModifier,
//                            taxes = item.taxIds.reduce { acc, s -> acc.plus(s) },
                            price = item.price.toDollars(), onClick = {
                                onItemClick(item)
                                onDismissRequest()
                            }
                        )
                    }
                }
            }
        }
    }
}


@Composable
fun PriceBookHeader() {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(text = "Item Name", modifier = Modifier.weight(2f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(text = "UPC A/E", modifier = Modifier.weight(1f))
        Spacer(modifier = Modifier.size(10.dp))

        Text(text = "UPC Modifier", modifier = Modifier.weight(1f), textAlign = TextAlign.Center)
        Spacer(modifier = Modifier.size(10.dp))

//        Text(text = "Taxes", modifier = Modifier.weight(1f), textAlign = TextAlign.Center)
        Text(text = "Price", modifier = Modifier.weight(1f), textAlign = TextAlign.End)
        Spacer(modifier = Modifier.size(10.dp))

    }
}

@Composable
fun PriceBookListItem(
    description: String,
    barcode: String,
    modifier: String,
//    taxes: String,
    price: String,
    onClick: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = description,
            modifier = Modifier.weight(2f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = barcode, modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Spacer(modifier = Modifier.size(10.dp))

        Text(
            text = modifier,
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.size(10.dp))

//        Text(
//            text = taxes,
//            modifier = Modifier.weight(1f),
//            style = MaterialTheme.typography.body2,
//            color = MaterialTheme.colors.blackAndWhite,
//            textAlign = TextAlign.Center
//        )
        Text(
            text = price,
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.End
        )
        Spacer(modifier = Modifier.size(10.dp))

    }
}