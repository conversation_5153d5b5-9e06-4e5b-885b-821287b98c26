package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.DismissDirection
import androidx.compose.material3.DismissValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.SwipeToDismiss
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDismissState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.disableGesture
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.data.model.isEbtSupported
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.theme.DisabledBackground
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.GrayBackground
import com.swiftsku.swiftpos.ui.theme.Green
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.ui.theme.clickableTextColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CartItem(
    onDeleteClick: (TransactionItem) -> Unit,
    onEditClick: (TransactionItem) -> Unit,
    item: TransactionItem,
    onApplyEBTClick: (TransactionItem) -> Unit,
    dashboardVM: DashboardViewModel
) {


    val dismissState = rememberDismissState(
        confirmValueChange = {
            if (it == DismissValue.DismissedToStart) {
                onDeleteClick(item)
                true
            } else false

        },
        positionalThreshold = { 150.dp.toPx() }
    )

    SwipeToDismiss(
        state = dismissState,
        directions = setOf(DismissDirection.EndToStart),
        modifier = Modifier
            .gesturesDisabled(item.disableGesture),
        dismissContent = {
            DismissContent(
                item = item,
                onEditClick = onEditClick,
                onApplyEBTClick = onApplyEBTClick,
                dashboardVM = dashboardVM
            )
        },
        background = {
            Row(
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                IconButton(onClick = {}) {
                    Icon(Icons.Filled.Delete, null, modifier = Modifier.size(24.dp), tint = Red)
                }
            }
        }
    )
}

@Composable
private fun DismissContent(
    item: TransactionItem,
    onEditClick: (TransactionItem) -> Unit,
    onApplyEBTClick: (TransactionItem) -> Unit,
    dashboardVM: DashboardViewModel
) {


    var showApplyEBT by remember { mutableStateOf(false) }

    val department = dashboardVM.departments.find {
        (item as? TransactionItemWithPLUItem)?.pluItem?.merchandiseCode == it.departmentId
    }


    Column(
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .background(if (item.disableGesture) DisabledBackground else GrayBackground)
            .padding(horizontal = 16.dp, vertical = 8.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(0.dp, Alignment.Start),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            Column(
                modifier = Modifier.weight(1f),
            ) {
                ClickableText(
                    onClick = {
                        if (!item.isEBT() && item.isEbtSupported(department)) {
                            showApplyEBT = !showApplyEBT
                        }
                    },
                    text = AnnotatedString(item.itemName()),
                    //                        modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption.copy(
                        MaterialTheme.colors.blackAndWhite
                    ),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.testTag("cart_item_${item.itemName()}")
                )

                if (showApplyEBT) {
                    Spacer(modifier = Modifier.height(10.dp))
                    ClickableText(
                        onClick = {
                            onApplyEBTClick(item)
                            showApplyEBT = false
                        },
                        text = AnnotatedString("Apply EBT"),
                        //                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.caption.copy(
                            color = Teal
                        ),
                        modifier = Modifier.testTag("cart_item_${item.itemName()}_apply_ebt")
                    )
                }
            }


            Text(
                modifier = Modifier
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = { onEditClick(item) }),
                text = "x${item.quantity}",
                style = MaterialTheme.typography.subtitle2,
                color = MaterialTheme.colors.clickableTextColor,
                textAlign = TextAlign.Center

            )
            if (item.isEBT()) {
                Spacer(modifier = Modifier.width(10.dp))
                Text(
                    text = "EBT",
                    //                        modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption,
                    color = Teal
                )
            }
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
//                if (item.disableGesture) {
//                    Text(
//                        text = "Paid",
//                        style = MaterialTheme.typography.caption,
//                        color = Red
//                    )
//                    Spacer(modifier = Modifier.width(10.dp))
//                }
                Text(
                    text = item.totalItemPrice().toDollars(),
                    style = MaterialTheme.typography.subtitle2,
                    color = MaterialTheme.colors.blackAndWhite,
                    textAlign = TextAlign.End
                )
            }
        }
        (item.promotion ?: emptyList()).forEach { promotion ->
            Row(
                horizontalArrangement = Arrangement.spacedBy(20.dp, Alignment.Start),
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(start = 10.dp)
            ) {
                Text(
                    text = promotion.promotionReason,
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.blackAndWhite,
                    overflow = TextOverflow.Ellipsis,
                )
                Text(
                    text = "-${promotion.promotionAmount.toDollars()}",
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption,
                    color = Green,
                    textAlign = TextAlign.End
                )
            }
        }
        item.discount.loyalty.forEach { discount ->
            Row(
                horizontalArrangement = Arrangement.spacedBy(20.dp, Alignment.Start),
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(start = 10.dp)
            ) {
                Text(
                    text = discount.name,
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.blackAndWhite,
                    overflow = TextOverflow.Ellipsis,
                )
                Text(
                    text = "-${discount.amount.toDollars()}",
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.caption,
                    color = Green,
                    textAlign = TextAlign.End
                )
            }
        }
    }
}