package com.swiftsku.swiftpos.ui.dashboard.recall

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RecallViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
) : ViewModel() {

    private val _loading = MutableStateFlow(true)
    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)
    private val _refreshTrigger = MutableStateFlow(Unit)

    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _pendingTransactions = _refreshTrigger
        .flatMapLatest {
            transactionRepository.getTransactionsFlow(
                TransactionStatus.Pending,
                TransactionType.Sale
            )
        }
        .catch { EventUtils.recordException(it) }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())

    val uiState: StateFlow<RecallListState> =
        combine(
            _pendingTransactions,
            _loading,
        ) { pendingTransactions, loading ->
            RecallListState(
                loading = loading,
                pendingTransactions = pendingTransactions.distinctBy { it.txnId }
                    .sortedByDescending { it.txnStartTime }
            )
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = RecallListState()
        )

    fun onDeleteClick(transaction: Transaction) = viewModelScope.launch {
        transactionRepository.deleteTransaction(transaction)
    }

    fun refreshSavedTransactions() {
        _refreshTrigger.value = Unit
    }
}