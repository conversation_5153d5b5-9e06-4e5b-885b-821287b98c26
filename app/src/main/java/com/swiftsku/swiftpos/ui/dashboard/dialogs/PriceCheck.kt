package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Red

@Composable
fun PriceCheck(
    onKeyEvent: (event: KeyEvent) -> Unit,
    onDismissRequest: () -> Unit,
    onAddToCartClick: (PluItem) -> Unit,
    pluItem: PluItem? = null
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/AgeVerificationIcon.svg")
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )

    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }


    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        Box(
            modifier = Modifier
                .focusRequester(focusRequester)
                .focusable(true)
                .focusProperties { canFocus = true }
                .fillMaxWidth()
                .padding(80.dp)
                .onKeyEvent { event ->
                    onKeyEvent(event)
                    true
                }
        ) {
            Surface(
                modifier = Modifier
                    .padding(50.dp)
                    .clip(shape = RoundedCornerShape(8.dp))
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxSize(1f)
                ) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .size(115.dp)
                    )
                    if (pluItem == null) {
                        Text(
                            text = "Scan Item to check price",
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.h5,
                            modifier = Modifier.padding(bottom = 24.dp) // optional padding
                        )
                        Button(
                            onClick = onDismissRequest,
                            modifier = Modifier
                                .padding(horizontal = 8.dp), // replaced weight with padding
                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                        ) {
                            Text(text = "Dismiss", color = Color.White)
                        }
                    } else {
                        Text(
                            text = "Price Check",
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.h5
                        )
                        Spacer(modifier = Modifier.height(10.dp))
                        Text(
                            text = "Description : ${pluItem.description}",
                            textAlign = TextAlign.Center,
                        )
                        Spacer(modifier = Modifier.height(10.dp))
                        Text(
                            text = "Regular Price : ${pluItem.price}",
                            textAlign = TextAlign.Center,
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                        ) {
                            Button(
                                onClick = onDismissRequest,
                                modifier = Modifier
                                    .weight(0.5f)
                                    .padding(8.dp),
                                colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                            ) {
                                Text(text = "Dismiss", color = Color.White)
                            }
                            Button(
                                onClick = {
                                    onAddToCartClick(pluItem)
                                    onDismissRequest()
                                },
                                modifier = Modifier
                                    .weight(0.5f)
                                    .padding(8.dp),
                                colors = ButtonDefaults.buttonColors(backgroundColor = Blue)
                            ) {
                                Text(text = "Add to Cart", color = Color.White)
                            }
                        }
                    }
                }
            }
        }
    }
}