package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Grey_5F
import com.swiftsku.swiftpos.ui.theme.Grey_D6
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun EBTDialog(
    onDismissRequest: () -> Unit,
    onProceedClick: (Boolean) -> Unit,
    ebtAmount: Float = 0f,
    remainingAmount: Float = 0f,
    dashboardVM: DashboardViewModel
) {
    val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()

    Dialog(onDismissRequest = onDismissRequest) {
        Surface(
            modifier = Modifier
                .padding(32.dp)
                .clip(shape = RoundedCornerShape(8.dp))
                .semantics { testTagsAsResourceId = true }
        ) {
            if (storeLevelConfig?.ebtPayments == true && storeLevelConfig?.streamPayments == true) {
                EbtTypeSelection(
                    storeLevelConfig?.ebtOptions.orEmpty(),
                    ebtAmount,
                    remainingAmount,
                    dashboardVM = dashboardVM
                )
            } else {
                val message =
                    "${ebtAmount.toDollars()} can be paid through EBT, please pay remaining ${remainingAmount.toDollars()} through Cash or Card"
                EbtCollectionInfo(
                    onDismissRequest,
                    onProceedClick,
                    remainingAmount,
                    message
                )
            }
        }
    }
}

@Composable
fun EbtCollectionInfo(
    onDismissRequest: () -> Unit,
    onProceedClick: (Boolean) -> Unit,
    remainingAmount: Float = 0f,
    message: String
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/AgeVerificationIcon.svg")
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )

    Column(
        modifier = Modifier.padding(32.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .size(115.dp)
                    .align(Alignment.Center)
            )
        }

        Spacer(modifier = Modifier.height(10.dp))

        Text(
            text = message,
            textAlign = TextAlign.Justify,
        )

        Spacer(modifier = Modifier.height(30.dp))

        Row(modifier = Modifier.fillMaxWidth()) {
            Button(
                onClick = onDismissRequest,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                colors = ButtonDefaults.buttonColors(backgroundColor = Red)
            ) {
                Text(text = "Dismiss", color = Color.White)
            }
            Spacer(modifier = Modifier.width(30.dp))
            Button(
                onClick = { onProceedClick(remainingAmount == 0f) },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .testTag("ebt_proceed"),
                colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
            ) {
                Text(text = "Proceed", color = Color.White)
            }
        }
    }
}

@Composable
fun EbtTypeSelection(
    ebtTypes: List<EbtType>,
    ebtAmount: Float = 0f,
    remainingAmount: Float = 0f,
    dashboardVM: DashboardViewModel
) {
    val loadings by dashboardVM.loadings.collectAsState()
    var selectedWallet by remember { mutableStateOf(EbtType.FOOD_STAMP) }
    val uiState by dashboardVM.uiState.collectAsState()
    var payAmount = ebtAmount
    uiState.ebtBalancesCents[selectedWallet]?.let { ebtBalanceCents ->
        if (ebtBalanceCents > 0f && (ebtBalanceCents / 100) < payAmount) {
            payAmount = ebtBalanceCents / 100
        }
    }
    Column {
        Text(
            text = "Wallets",
            style = MaterialTheme.typography.h6,
            modifier = Modifier.padding(start = 12.dp, end = 12.dp, top = 8.dp, bottom = 8.dp),
            color = Color.Black
        )
        Divider(color = Color.DarkGray)
        Row {
            LazyColumn(
                modifier = Modifier
                    .weight(0.4f)
                    .background(Grey_5F)
            ) {
                ebtTypes.forEachIndexed { _, ebtType ->
                    item {
                        Box {
                            Text(
                                text = ebtType.value,
                                style = MaterialTheme.typography.h6,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable(onClick = { selectedWallet = ebtType })
                                    .background(if (ebtType == selectedWallet) Grey_D6 else Color.Transparent)
                                    .padding(start = 12.dp, end = 12.dp, top = 6.dp, bottom = 6.dp),
                                color = if (ebtType == selectedWallet) Blue else Color.DarkGray
                            )
                        }
                    }
                }
            }
            Column(
                modifier = Modifier
                    .weight(0.6f)
                    .padding(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 16.dp)
            ) {
                Button(
                    onClick = {
                        dashboardVM.selectEbtType(selectedWallet, remainingAmount == 0f, payAmount)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(backgroundColor = Teal),
                    elevation = ButtonDefaults.elevation(0.dp)
                ) {
                    Text(
                        text = "Pay ${payAmount.toDollars()}",
                        style = MaterialTheme.typography.h6,
                        color = Color.White
                    )
                }
                Row(
                    modifier = Modifier
                        .padding(10.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ClickableText(
                        onClick = { dashboardVM.getEbtBalance(selectedWallet) },
                        text = AnnotatedString("Check Balance"),
                        style = MaterialTheme.typography.subtitle1.copy(color = Blue),
                    )
                    if (loadings.contains(DashboardViewModel.Loadings.EBT_BALANCE)) {
                        CircularProgressIndicator(
                            modifier = Modifier
                                .padding(start = 8.dp)
                                .size(16.dp),
                            color = Blue,
                            strokeWidth = 2.dp
                        )
                    }
                }
            }
        }
    }
}
