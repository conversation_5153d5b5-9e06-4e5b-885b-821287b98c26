package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.ui.components.AppToast
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TxnHistoryDetailViewModel
import com.swiftsku.swiftpos.ui.theme.Teal

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun RequestCashDrawerClose(
    onDismissRequest: () -> Unit,
    message: @Composable () -> Unit,
    txnHistoryDetailVM: TxnHistoryDetailViewModel
) {

    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/AgeVerificationIcon.svg")
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )
    val toast by txnHistoryDetailVM.toastMessage.collectAsState()

    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        Surface(
            modifier = Modifier
                .padding(32.dp)
                .clip(shape = RoundedCornerShape(8.dp))
                .semantics { testTagsAsResourceId = true }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(32.dp)
            ) {
                Image(
                    painter = painter,
                    contentDescription = null,
                    modifier = Modifier
                        .size(115.dp)
                )
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text = "Close the Drawer",
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.h6
                )
                Spacer(modifier = Modifier.height(24.dp))

                message()


                Spacer(modifier = Modifier.height(24.dp))
                Button(
                    onClick = onDismissRequest,
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag("cash_drawer_done"),
                    colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
                ) {
                    Text(text = "Done", color = Color.White)
                }
            }

            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                toast?.let { AppToast(message = it) }
            }
        }
    }
}