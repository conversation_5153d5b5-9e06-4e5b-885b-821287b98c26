package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.EbtInfo
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.debounced
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Purple
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun TransactionHeader(
    name: String,
    transactionId: String,
    date: String,
    totalAmount: Float,
    txnType: String,
    onRefundClick: () -> Unit,
    onReprintClick: () -> Unit,
    showRefund: Boolean = false,
    isRefunded: Boolean = false,
    loading: Boolean = false,
    showReprint: Boolean = false,
    cashPayment: CashPayment? = null,
    cardPayment: CardPayment? = null,
    ebtPayment: EBTPayment? = null,
    chequePayment: ChequePayment? = null,
    creditPayment: CreditPayment? = null,
    ebtInfo: EbtInfo? = null,
    isIntegrated: Boolean = false,
    txn: Transaction? = null,
    appliedFees: List<AppliedFee>? = null,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Row(
            modifier = Modifier
                .weight(1f)
                .background(MaterialTheme.colors.background)
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment =
            Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "$txnType Transaction by $name",
                    color = MaterialTheme.colors.blackAndWhite
                )
                Text(text = "TI- $transactionId  |  $date")
                Spacer(modifier = Modifier.size(10.dp))
                Row {
                    if (cashPayment != null) {
                        PaidAmount("Cash", cashPayment.tender)
                        Spacer(modifier = Modifier.size(5.dp))
                        PaidAmount("Change", cashPayment.change)
                        Spacer(modifier = Modifier.size(5.dp))
                    }
                    if (cardPayment != null) {
                        PaidAmount("Card", cardPayment.amount)
                        Spacer(modifier = Modifier.size(5.dp))
                        (txn as? SaleTransaction)?.refundRes?.gen?.ok?.let { genRefunded ->
                            if (genRefunded) {
                                Text(text = "(Refunded)", fontSize = 12.sp)
                                Spacer(modifier = Modifier.size(5.dp))
                            }
                        }
                    }
                    if (ebtPayment != null) {
                        PaidAmount("EBT", ebtPayment.amount)
                        ebtInfo?.ebtType?.let {
                            Spacer(modifier = Modifier.size(5.dp))
                            Text(text = "[${it.value}]", fontSize = 12.sp)
                            Spacer(modifier = Modifier.size(5.dp))
                        }
                        (txn as? SaleTransaction)?.refundRes?.ebt?.ok?.let { ebtRefunded ->
                            if (ebtRefunded) {
                                Text(text = "(Refunded)", fontSize = 12.sp)
                                Spacer(modifier = Modifier.size(5.dp))
                            }
                        }
                    }
                    chequePayment?.let {
                        PaidAmount("Check", it.amount)
                    }
                    creditPayment?.let {
                        PaidAmount("Credit", it.amount)
                        Spacer(modifier = Modifier.size(5.dp))
                        Text(text = "(${it.accountName})", fontSize = 12.sp)
                    }
                }
                appliedFees?.firstOrNull { it.type == FeeType.CARD_PROCESSING }?.let {
                    Text(
                        text = "${it.amount.toDollars()} was charged extra as a card processing fee",
                        fontSize = 12.sp
                    )
                }
            }
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Icon(
                    ImageVector.vectorResource(id = R.drawable.ic_sync_done),
                    null,
                    tint = if (isIntegrated) Teal else Color.DarkGray,
                    modifier = Modifier.size(36.dp)
                )
                Spacer(modifier = Modifier.size(8.dp))
                Text(text = totalAmount.toDollars(), color = Blue)
            }
        }

        Spacer(modifier = Modifier.size(10.dp))
        Column(
            modifier = Modifier.width(100.dp)
        ) {

            if (showReprint) {
                Button(
                    onClick = debounced(onClick = onReprintClick),
                    colors = ButtonDefaults.buttonColors(backgroundColor = Purple),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = "Reprint", color = Color.White)
                }
            }
            if (loading) {
                CircularProgressIndicator()
            }
            if (showRefund && !loading) {
                Button(
                    onClick = debounced(onClick = onRefundClick),
                    colors = ButtonDefaults.buttonColors(backgroundColor = Blue),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = "Refund", color = Color.White)
                }
            }

            if (isRefunded) {
                OutlinedButton(
                    onClick = {},
                    colors = ButtonDefaults.buttonColors(
                        contentColor = Red,
                        backgroundColor = Color.White
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = "Refunded")
                }
            }


        }
    }
}


@Composable
fun PaidAmount(
    title: String,
    amount: Float,
) {
    Row {
        Text(text = title, fontSize = 12.sp)
        Spacer(modifier = Modifier.size(5.dp))
        Text(text = amount.toDollars(), fontSize = 12.sp, color = Blue)
    }
}