package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color

@Composable
fun <T> DropDownMenu(
    items: List<T>,
    selectedItem: T,
    onSelectedMenuChange: (T) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable (T) -> Unit,
    dropDownContent: @Composable (T) -> Unit
) {

    var expanded by remember { mutableStateOf(false) }
    Box(
        modifier = modifier
            .wrapContentSize(Alignment.TopStart)
    ) {
        Box(
            modifier = Modifier
                .clickable(onClick = { expanded = true })
        ) {
            content(selectedItem)
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier
                .background(Color.White)
        ) {
            items.forEachIndexed { index, s ->
                DropdownMenuItem(
                    text = {
                        dropDownContent(s)
                    },
                    onClick = {
                        onSelectedMenuChange(items[index])
                        expanded = false
                    }
                )
            }
        }
    }
}