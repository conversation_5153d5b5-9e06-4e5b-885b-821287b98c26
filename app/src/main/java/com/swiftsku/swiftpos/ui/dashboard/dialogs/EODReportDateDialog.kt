package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.extension.convertToDate
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.visualizers.DateVisualTransformation
import com.swiftsku.swiftpos.utils.isValidDate
import java.time.LocalDate

@Composable
fun EODReportDateDialog(
    onDismissRequest: () -> Unit,
    onSubmitRequest: (date: String) -> Unit,
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/AgeVerificationIcon.svg")
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )

    var date by remember {
        mutableStateOf("")
    }
    var isDateSet by remember {
        mutableStateOf(false)
    }
    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        Surface(
            modifier = Modifier
                .padding(32.dp)
//                    .width(500.dp)
                .clip(shape = RoundedCornerShape(8.dp))

        ) {
            Column(
//                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(32.dp)
            ) {

                Box(modifier = Modifier.fillMaxWidth()) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .size(115.dp)
                            .align(Alignment.Center)
                    )
                }


                Spacer(modifier = Modifier.height(10.dp))

                Text(
                    text = "Select date for EOD report",
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.caption
                )
                Spacer(modifier = Modifier.height(5.dp))

                TextField(
                    value = date,
                    placeholder = { Text(text = "MM/DD/YYYY") },
                    modifier = Modifier.fillMaxWidth(),
                    visualTransformation = DateVisualTransformation,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword),
                    onValueChange = { input ->
                        if (input.length <= 8) {
                            date = input
                            isDateSet = if (input.length == 8) {
                                isValidDate(input, maxDate = LocalDate.now())
                            } else {
                                false
                            }
                        }

                    }
                )

                Spacer(modifier = Modifier.height(30.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        enabled = isDateSet,
                        onClick = { onSubmitRequest(date.convertToDate()) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        Text(text = "Submit")
                    }
                }
            }
        }
    }
}

