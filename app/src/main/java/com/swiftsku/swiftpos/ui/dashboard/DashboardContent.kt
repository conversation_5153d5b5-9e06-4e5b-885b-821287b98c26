package com.swiftsku.swiftpos.ui.dashboard

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.LoyaltyState
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.ui.dashboard.cart.DashboardCart
import com.swiftsku.swiftpos.ui.dashboard.main.DashboardMain
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel

@Composable
fun DashboardContent(
    modifier: Modifier = Modifier,
    onCardClick: () -> Unit,
    onEBTClick: () -> Unit,
    onNoSaleClick: () -> Unit,
    onRedeemClick: () -> Unit,
    onSaveClick: () -> Unit,
    onVoidClick: () -> Unit,
    onCashClick: () -> Unit,
    onLoyaltyClick: () -> Unit,
    onCashAmountClick: (amount: Float) -> Unit,
    onPriceBookItemClick: (item: PluItem) -> Unit,
    totalSale: Float,
    customerCount: Int,
    onDeleteClick: (item: TransactionItem) -> Unit,
    onEditClick: (item: TransactionItem) -> Unit,
    onPriceCheckClick: (Int) -> Unit,
    onRecallClick: (Int) -> Unit,
    onTxnHistoryClick: (Int) -> Unit,
    cartItems: List<TransactionItem> = emptyList(),
    priceBookItems: List<PluItem>,
    departmentList: List<Department>,
    onDepartmentClick: (item: Department) -> Unit,
    transactionSummary: TransactionSummary,
    onMenuClick: () -> Unit,
    coupons: List<Coupon>,
    onCouponRemoveClick: () -> Unit,
    onPayoutClick: () -> Unit,
    loyaltyState: LoyaltyState,
    onReportClick: (Int) -> Unit,
    onInfoClick: () -> Unit,
    onApplyEBTClick: (TransactionItem) -> Unit,
    fuelPumps: List<FuelPumpState>,
    onPumpClick: (FuelPumpState) -> Unit,
    allowEditingCart: Boolean = true,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = modifier.fillMaxWidth()
    ) {
        DashboardCart(
            onDeleteClick = onDeleteClick,
            onEditClick = onEditClick,
            cartItems = cartItems,
            transactionSummary = transactionSummary,
            coupons = coupons,
            onCouponRemoveClick = onCouponRemoveClick,
            loyaltyState = loyaltyState,
            onApplyEBTClick = onApplyEBTClick,
            allowEditingCart = allowEditingCart,
            dashboardVM = dashboardVM
        )
        DashboardMain(
            onCardClick = onCardClick,
            onEBTClick = onEBTClick,
            onNoSaleClick = onNoSaleClick,
            onRedeemClick = onRedeemClick,
            onSaveClick = onSaveClick,
            onVoidClick = onVoidClick,
            onCashClick = onCashClick,
            onLoyaltyClick = onLoyaltyClick,
            onCashAmountClick = onCashAmountClick,
            onPriceBookItemClick = onPriceBookItemClick,
            totalSale = totalSale,
            customerCount = customerCount,
            onPriceCheckClick = onPriceCheckClick,
            onRecallClick = onRecallClick,
            onTxnHistoryClick = onTxnHistoryClick,
            priceBookItems = priceBookItems,
            departmentList = departmentList,
            onDepartmentClick = onDepartmentClick,
            transactionSummary = transactionSummary,
            onMenuClick = onMenuClick,
            onPayoutClick = onPayoutClick,
            onReportClick = onReportClick,
            onInfoClick = onInfoClick,
            fuelPumps = fuelPumps,
            onPumpClick = onPumpClick,
            dashboardVM = dashboardVM,
            fdcVM = fdcVM
        )
    }
}
