package com.swiftsku.swiftpos.ui.report

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.couchbase.user.CouchbaseUserRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.type.ReportDateResult
import com.swiftsku.swiftpos.data.type.ReportTimeResult
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.domain.config.GetPresetConfigUseCase
import com.swiftsku.swiftpos.domain.printer.PrintEODReportUseCase
import com.swiftsku.swiftpos.domain.report.GetAdHocReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEODReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEOMReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEOYReportUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalBatchReportListUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalBatchReportUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalShiftReportListUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalShiftReportUseCase
import com.swiftsku.swiftpos.domain.report.GetPresetReportUseCase
import com.swiftsku.swiftpos.domain.report.GetShiftReportUseCase
import com.swiftsku.swiftpos.extension.toEODRequestDate
import com.swiftsku.swiftpos.extension.toEOYDate
import com.swiftsku.swiftpos.extension.toEOYRequestTime
import com.swiftsku.swiftpos.modules.printer.EODReportTemplate
import com.swiftsku.swiftpos.utils.months
import com.swiftsku.swiftpos.utils.years
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import javax.inject.Inject

@HiltViewModel
class ReportViewModel @Inject constructor(
    private val printEODReportUseCase: PrintEODReportUseCase,
    private val getEOYReportUseCase: GetEOYReportUseCase,
    private val getEODReportUseCase: GetEODReportUseCase,
    private val getEOMReportUseCase: GetEOMReportUseCase,
    private val getAdHocReportUseCase: GetAdHocReportUseCase,
    private val getShiftReportUseCase: GetShiftReportUseCase,
    private val getPresetReportUseCase: GetPresetReportUseCase,
    private val getPresetConfigUseCase: GetPresetConfigUseCase,
    private val getHistoricalShiftReportUseCase: GetHistoricalShiftReportUseCase,
    private val getHistoricalBatchReportUseCase: GetHistoricalBatchReportUseCase,
    private val getHistoricalShiftReportListUseCase: GetHistoricalShiftReportListUseCase,
    private val getHistoricalBatchReportListUseCase: GetHistoricalBatchReportListUseCase,
    private val userDataSource: UserDataSource,
    private val userRepository: CouchbaseUserRepository,
) : ViewModel() {


    private val _uiState = MutableStateFlow(ReportUIState())
    private val _eodDate = MutableStateFlow(LocalDate.now())
    private val _adHocState = MutableStateFlow(AdHocState())
    private val _presetState = MutableStateFlow(PresetReportState())
    private val _historicalShiftReportState = MutableStateFlow(HistoricalReportState())
    private val _historicalBatchReportState = MutableStateFlow(HistoricalBatchState())
    private val _eomDate = MutableStateFlow(months[LocalDate.now().monthValue - 1])
    private val _eoyDate = MutableStateFlow(years[LocalDate.now().year - 2023])

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)

    val uiState: StateFlow<ReportUIState> =
        combine(
            _uiState,
            _eodDate,
            _eomDate,
            _eoyDate,
            _adHocState,
            _presetState,
            _historicalShiftReportState,
            _historicalBatchReportState
        ) { args ->

            val initialState = args[0] as ReportUIState
            val eodDate = args[1] as LocalDate
            val eomDate = args[2] as String
            val eoyDate = args[3] as String
            val adHoc = args[4] as AdHocState
//            val presetState = args[5] as PresetReportState
            val historicalReportState = args[6] as HistoricalReportState
            val historicalBatchState = args[7] as HistoricalBatchState

            initialState.copy(
                eodState = initialState.eodState.copy(selectedEODDate = eodDate),
                eomState = initialState.eomState.copy(selectedEOMDate = eomDate),
                eoyState = initialState.eoyState.copy(selectedEOYDate = eoyDate),
                adHocState = initialState.adHocState.copy(
                    selectedAdHocEndDate = adHoc.selectedAdHocEndDate,
                    selectedAdHocStartDate = adHoc.selectedAdHocStartDate,
                    selectedAdHocStartTime = adHoc.selectedAdHocStartTime,
                    selectedAdHocEndTime = adHoc.selectedAdHocEndTime,
                ),
                historicalShiftState = initialState.historicalShiftState.copy(
                    selectedShiftStartDate = historicalReportState.selectedShiftStartDate,
                    selectedShiftEndDate = historicalReportState.selectedShiftEndDate,
                    selectedTerminal = historicalReportState.selectedTerminal,
                    selectedCashier = historicalReportState.selectedCashier,
                ),
                historicalBatchState = initialState.historicalBatchState.copy(
                    selectedBatchStartDate = historicalBatchState.selectedBatchStartDate,
                    selectedBatchEndDate = historicalBatchState.selectedBatchEndDate,
                    selectedTerminal = historicalBatchState.selectedTerminal
                ),
//                presetState = presetState
            )
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = ReportUIState()
        )

    fun initReportVM() = viewModelScope.launch {
        _storeConfig.value?.let { config ->
            getCurrentUser()
            fetchStoreUsers(config)
            initializePreset(config)
            setPresetListener(config)
            fetchShiftReport(config)
            fetchEODReport(config)
            fetchEOMReport(config)
            fetchEOYReport(config)
            fetchAdHocReport(config)
            fetchHistoricalShiftReportList(config)
            fetchHistoricalBatchReportList(config)
            setSelectedHistoricalReportListener(config)
            setSelectedBatchReportListener(config)
        }
    }

    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    private fun initializePreset(config: StoreConfig) = viewModelScope.launch {
        getPresetConfigUseCase.invoke(config.storeCode).collectLatest { preset ->
            preset?.let {
                _uiState.update { state ->
                    state.copy(
                        presetState = state.presetState.copy(
                            presets = preset.presets.keys.toList()
                        )
                    )
                }
                _presetState.update {
                    it.copy(presets = preset.presets.keys.toList())
                }
            }
        }
    }

    private fun setPresetListener(config: StoreConfig) = viewModelScope.launch {
        _presetState.collectLatest { preset ->
            if (preset.selectedPreset != null) {
                _uiState.update { state ->
                    state.copy(
                        presetState = preset.copy(
                            loader = true,
                            presetReport = null,
                            currentPage = -1
                        )
                    )
                }
                val presetReport =
                    getPresetReportUseCase.get(config.storeCode, preset.selectedPreset)
                _uiState.update { state ->
                    state.copy(
                        presetState = preset.copy(
                            presetReport = presetReport,
                            loader = false
                        )
                    )
                }
            }
        }
    }

    private fun fetchHistoricalShiftReportList(config: StoreConfig) = viewModelScope.launch {
        _historicalShiftReportState.collectLatest { historicalShiftReportState ->
            val startDate = historicalShiftReportState.selectedShiftStartDate
            val endDate = historicalShiftReportState.selectedShiftEndDate
            val terminal = historicalShiftReportState.selectedTerminal
            val cashierId = historicalShiftReportState.selectedCashier

            _uiState.update {
                it.copy(
                    historicalShiftState = it.historicalShiftState.copy(
                        viewingListScreen = true,
                        shiftListLoader = true
                    )
                )
            }

            val historicalShiftReportList = getHistoricalShiftReportListUseCase.get(
                config.storeCode,
                startDate = startDate.toString(),
                endDate = endDate.toString(),
                terminal = terminal,
                cashierId = cashierId,
            )
            val terminals = historicalShiftReportList?.terminals ?: emptyList()

            _uiState.update {
                it.copy(
                    historicalShiftState = it.historicalShiftState.copy(
                        shiftListLoader = false,
                        historicalShifts = historicalShiftReportList?.data?.map { hs ->
                            HistoricalShiftReportItem(
                                shiftId = hs.shiftId ?: "",
                                terminal = hs.terminal ?: "",
                                cashierId = hs.cashierId ?: "",
                                shiftStart = hs.shiftStart ?: "",
                                shiftEnd = hs.shiftEnd ?: ""
                            )
                        } ?: emptyList(),
                        terminals = listOf("All Terminals") + terminals
                    )
                )
            }
        }
    }

    private fun fetchHistoricalBatchReportList(config: StoreConfig) = viewModelScope.launch {
        _historicalBatchReportState.collectLatest { historicalBatchReportState ->
            val startDate = historicalBatchReportState.selectedBatchStartDate
            val endDate = historicalBatchReportState.selectedBatchEndDate
            val terminal = historicalBatchReportState.selectedTerminal

            _uiState.update {
                it.copy(
                    historicalBatchState = it.historicalBatchState.copy(
                        viewingListScreen = true,
                        batchListLoader = true
                    )
                )
            }

            val historicalBatchReportList = getHistoricalBatchReportListUseCase.get(
                config.storeCode,
                startDate = startDate.toString(),
                endDate = endDate.toString(),
                terminal = terminal
            )
            val terminals = historicalBatchReportList?.terminals ?: emptyList()

            _uiState.update {
                it.copy(
                    historicalBatchState = it.historicalBatchState.copy(
                        batchListLoader = false,
                        historicalBatches = historicalBatchReportList?.data?.map { hs ->
                            HistoricalBatchReportItem(
                                batchId = hs.batchId ?: "",
                                terminal = hs.terminal ?: "",
                                batchStart = hs.batchStart ?: "",
                                batchClose = hs.batchEnd ?: "",
                                batchEndCashier = hs.batchEndCashier  ?: "",
                            )
                        } ?: emptyList(),
                        terminals = listOf("All Terminals") + terminals
                    )
                )
            }
        }
    }

    private fun setSelectedHistoricalReportListener(config: StoreConfig) = viewModelScope.launch {
        _historicalShiftReportState.collectLatest { historicalShiftReportState ->
            val shiftId = historicalShiftReportState.selectedShiftId
            if (shiftId != "") {
                fetchSelectedHistoricalShiftReport(config, shiftId)
            } else {
                _uiState.update {
                    it.copy(
                        historicalShiftState = it.historicalShiftState.copy(
                            selectedShiftLoader = false,
                            selectedHistoricalShiftReport = null,
                            currentPage = -1,
                            viewingListScreen = true
                        )
                    )
                }
            }
        }
    }

    private fun setSelectedBatchReportListener(config: StoreConfig) = viewModelScope.launch {
        _historicalBatchReportState.collectLatest { historicalBatchReportState ->
            val batchId = historicalBatchReportState.selectedBatchId
            if (batchId != "") {
                fetchSelectedHistoricalBatchReport(config, batchId)
            } else {
                _uiState.update {
                    it.copy(
                        historicalBatchState = it.historicalBatchState.copy(
                            selectedBatchLoader = false,
                            selectedHistoricalBatchReport = null,
                            currentPage = -1,
                            viewingListScreen = true
                        )
                    )
                }
            }
        }
    }

    private fun fetchSelectedHistoricalShiftReport(config: StoreConfig, shiftId: String) =
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    historicalShiftState = it.historicalShiftState.copy(
                        viewingListScreen = false,
                        selectedShiftLoader = true,
                        selectedHistoricalShiftReport = null
                    )
                )
            }

            val historicalShiftReport =
                getHistoricalShiftReportUseCase.get(config.storeCode, shiftId)

            _uiState.update {
                it.copy(
                    historicalShiftState = it.historicalShiftState.copy(
                        selectedHistoricalShiftReport = historicalShiftReport,
                        selectedShiftLoader = false
                    )
                )
            }
        }

    private fun fetchSelectedHistoricalBatchReport(config: StoreConfig, batchId: String) = viewModelScope.launch(Dispatchers.IO) {
        _uiState.update {
            it.copy(
                historicalBatchState = it.historicalBatchState.copy(
                    viewingListScreen = false,
                    selectedBatchLoader = true,
                    selectedHistoricalBatchReport = null
                )
            )
        }

        val historicalBatchReport = getHistoricalBatchReportUseCase.get(config.storeCode, batchId)

        _uiState.update {
            it.copy(
                historicalBatchState = it.historicalBatchState.copy(
                    selectedHistoricalBatchReport = historicalBatchReport,
                    selectedBatchLoader = false
                )
            )
        }
    }

    private fun fetchShiftReport(store: StoreConfig) = viewModelScope.launch {
        _uiState.update {
            it.copy(
                shiftState = it.shiftState.copy(
                    loader = true,
                    shiftReport = null
                ),
            )
        }
        val shiftReport = getShiftReportUseCase.get(store.storeCode, store.posNumber, null)
        _uiState.update {
            it.copy(
                shiftState = it.shiftState.copy(
                    shiftReport = shiftReport,
                    loader = false
                )
            )
        }
    }

    private fun fetchAdHocReport(store: StoreConfig) = viewModelScope.launch {
        _adHocState.collectLatest { adHocState ->
            val startDate = adHocState.selectedAdHocStartDate
            val endDate = adHocState.selectedAdHocEndDate
            val startTime = adHocState.selectedAdHocStartTime
            val endTime = adHocState.selectedAdHocEndTime

            val startDateTime = "${startDate.toEOYDate()}T${startTime.toEOYRequestTime()}"
            val endDateTime = "${endDate.toEOYDate()}T${endTime.toEOYRequestTime()}"
            _uiState.update {
                it.copy(
                    adHocState = it.adHocState.copy(
                        loader = true,
                        adHocReport = null
                    )
                )
            }

            val adHocReport = getAdHocReportUseCase.get(
                store.storeCode,
                startDate = startDateTime,
                endDate = endDateTime,
            )

            _uiState.update {
                it.copy(
                    adHocState = it.adHocState.copy(
                        adHocReport = adHocReport,
                        loader = false
                    )
                )
            }

        }
    }

    private fun fetchEOYReport(store: StoreConfig) = viewModelScope.launch {
        _eoyDate.collectLatest { date ->
            val year = date.toInt()

            _uiState.update {
                it.copy(
                    eoyState = it.eoyState.copy(
                        loader = true,
                        eoyReport = null

                    )
                )
            }

            val eoyReport =
                getEOYReportUseCase.get(store.storeCode, year = year)

            _uiState.update {
                it.copy(
                    eoyState = it.eoyState.copy(
                        eoyReport = eoyReport,
                        loader = false
                    )
                )
            }
        }
    }

    private fun fetchEOMReport(store: StoreConfig) = viewModelScope.launch {
        combine(_eomDate, _eoyDate) { eomDate, eoyDate ->
            Pair(eomDate, eoyDate)
        }.collectLatest { (eomDate, eoyDate) ->
            val month = months.indexOf(eomDate) + 1
            var monthOutput = "$month"
            if (month < 10) {
                monthOutput = "0$month"
            }
            val input = "$eoyDate-$monthOutput"
            val eomReport =
                getEOMReportUseCase.get(store.storeCode, date = input)

            _uiState.update { it.copy(eomState = it.eomState.copy(eomReport = eomReport)) }
        }
    }

    private fun fetchEODReport(store: StoreConfig) = viewModelScope.launch {

        _eodDate.collectLatest { date ->
            _uiState.update { it.copy(eodState = it.eodState.copy(loader = true)) }

            val eodReport =
                getEODReportUseCase.get(store.storeCode, date = date.toEODRequestDate())

            _uiState.update {
                it.copy(
                    eodState = it.eodState.copy(
                        eodReport = eodReport,
                        loader = false
                    )
                )
            }
        }
    }

    fun onReportTypeChange(type: ReportType) = viewModelScope.launch {
        val config = _storeConfig.value ?: return@launch
        _uiState.update { it.copy(selectedReportType = type) }
        if (type == ReportType.Shift) {
            fetchShiftReport(config)
        } else if (type == ReportType.HistoricalShift) {
            fetchHistoricalShiftReportList(config)
        } else if (type == ReportType.HistoricalBatch) {
            fetchHistoricalBatchReportList(config)
        }
    }

    fun onSelectedMonthChange(month: String) = viewModelScope.launch {
        _eomDate.emit(month)
    }

    fun onSelectedYearChange(year: String) = viewModelScope.launch {
        _eoyDate.emit(year)
    }

    fun onTimeSelected(time: LocalTime, reportTimeResult: ReportTimeResult) =
        viewModelScope.launch {
            when (reportTimeResult) {
                ReportTimeResult.AdHocStartTime -> {
                    _adHocState.update { it.copy(selectedAdHocStartTime = time) }
                }

                ReportTimeResult.AdHocEndTime -> {
                    _adHocState.update { it.copy(selectedAdHocEndTime = time) }
                }
            }
        }

    fun selectDate(reportDateResult: ReportDateResult) = viewModelScope.launch {
        _uiState.update { it.copy(selectDate = reportDateResult) }
    }

    fun selectTime(reportTimeResult: ReportTimeResult) = viewModelScope.launch {
        _uiState.update { it.copy(selectTime = reportTimeResult) }
    }

    fun dismissTimePicker() = viewModelScope.launch {
        _uiState.update { it.copy(selectTime = null) }
    }


    fun onDateSelected(selectedDate: LocalDate, reportDateResult: ReportDateResult) =
        viewModelScope.launch {

            when (reportDateResult) {
                ReportDateResult.EODDate -> {
                    _eodDate.emit(selectedDate)
                }

                ReportDateResult.AdHocStartDate -> {
                    _adHocState.update { it.copy(selectedAdHocStartDate = selectedDate) }
                }

                ReportDateResult.AdHocEndDate -> {
                    _adHocState.update { it.copy(selectedAdHocEndDate = selectedDate) }
                }

                ReportDateResult.HistoricalShiftStartDate -> {
                    _historicalShiftReportState.update { it.copy(selectedShiftStartDate = selectedDate) }
                }

                ReportDateResult.HistoricalShiftEndDate -> {
                    _historicalShiftReportState.update { it.copy(selectedShiftEndDate = selectedDate) }
                }

                ReportDateResult.HistoricalBatchStartDate -> {
                    _historicalBatchReportState.update { it.copy(selectedBatchStartDate = selectedDate) }
                }

                ReportDateResult.HistoricalBatchEndDate -> {
                    _historicalBatchReportState.update { it.copy(selectedBatchEndDate = selectedDate) }
                }
            }
        }

    fun onPrintClick() = viewModelScope.launch {
        val state = uiState.value
        var base64: String? = null
        when (state.selectedReportType) {
            ReportType.AdHoc -> {
                val adHocState = state.adHocState
                if (adHocState.currentPage != -1) {
                    adHocState.adHocReport?.stats?.get(adHocState.currentPage)?.terminalData?.let {
                        base64 = it
                    }
                }
            }

            ReportType.EOD -> {
                val eodState = state.eodState
                if (eodState.currentPage != -1) {
                    eodState.eodReport?.bmp?.values?.toList()?.get(eodState.currentPage)?.let {
                        base64 = it
                    }
                }
            }

            ReportType.EOM -> {
                val eomState = state.eomState
                if (eomState.currentPage != -1) {
                    eomState.eomReport?.bmp?.values?.toList()?.get(eomState.currentPage)?.let {
                        base64 = it
                    }
                }
            }

            ReportType.EOY -> {
                val eoyState = state.eoyState
                if (eoyState.currentPage != -1) {
                    eoyState.eoyReport?.stats?.get(eoyState.currentPage)?.terminalData?.let {
                        base64 = it
                    }
                }
            }

            ReportType.Shift -> {
                val shiftState = state.shiftState
                if (shiftState.currentPage != -1) {
                    shiftState.shiftReport?.stats?.get(shiftState.currentPage)?.terminalData?.let {
                        base64 = it
                    }
                }
            }

            ReportType.Preset -> {
                val presetState = state.presetState
                if (presetState.currentPage != -1) {
                    presetState.presetReport?.stats?.get(presetState.currentPage)?.terminalData
                        ?.let {
                            base64 = it
                        }
                }
            }

            ReportType.HistoricalShift -> {
                val historicalShiftState = state.historicalShiftState
                if (historicalShiftState.currentPage != -1) {
                    historicalShiftState.selectedHistoricalShiftReport?.stats
                        ?.get(historicalShiftState.currentPage)?.terminalData
                        ?.let {
                            base64 = it
                        }
                }
            }

            ReportType.HistoricalBatch -> {
                val historicalBatchState = state.historicalBatchState
                if (historicalBatchState.currentPage != -1) {
                    historicalBatchState.selectedHistoricalBatchReport?.stats
                        ?.get(historicalBatchState.currentPage)?.terminalData
                        ?.let {
                            base64 = it
                        }
                }
            }
        }
        base64?.let {
            printEODReportUseCase(EODReportTemplate(it))
        }
    }

    fun dismissDatePicker() = viewModelScope.launch {
        _uiState.update { it.copy(selectDate = null) }
    }

    fun onEODPageChange(pageNumber: Int) = viewModelScope.launch {
        _uiState.update { it.copy(eodState = it.eodState.copy(currentPage = pageNumber)) }
    }

    fun onEOMPageChange(pageNumber: Int) = viewModelScope.launch {
        _uiState.update { it.copy(eomState = it.eomState.copy(currentPage = pageNumber)) }
    }

    fun onEOYPageChange(pageNumber: Int) = viewModelScope.launch {
        _uiState.update { it.copy(eoyState = it.eoyState.copy(currentPage = pageNumber)) }
    }

    fun onAdHocPageChange(pageNumber: Int) = viewModelScope.launch {
        _uiState.update { it.copy(adHocState = it.adHocState.copy(currentPage = pageNumber)) }
    }

    fun onShiftPageChange(pageNumber: Int) {
        _uiState.update { it.copy(shiftState = it.shiftState.copy(currentPage = pageNumber)) }
    }

    fun reset() = viewModelScope.launch {
        _uiState.emit(ReportUIState())
        _eodDate.emit(LocalDate.now())
        _adHocState.emit(AdHocState())
        _presetState.emit(PresetReportState())
        _historicalShiftReportState.emit(HistoricalReportState())
        _historicalBatchReportState.emit(HistoricalBatchState())
        _eomDate.emit(months[LocalDate.now().monthValue - 1])
        _eoyDate.emit(years[LocalDate.now().year - 2023])
    }

    fun selectPreset(preset: String) = viewModelScope.launch {
        _presetState.update { it.copy(selectedPreset = preset) }
        _uiState.update { state ->
            state.copy(presetState = state.presetState.copy(selectedPreset = preset))
        }
    }

    fun selectHistoricalShift(shiftDetail: ReportListDetail) = viewModelScope.launch {
        val shiftId = shiftDetail.key
        _historicalShiftReportState.update {
            it.copy(
                selectedShiftId = shiftId,
                viewingListScreen = false
            )
        }
        _uiState.update { state ->
            state.copy(
                historicalShiftState = state.historicalShiftState.copy(
                    selectedShiftId = shiftId,
                    viewingListScreen = false,
                    selectedShiftLoader = true
                )
            )
        }
    }

    fun selectHistoricalBatch(batchDetail: ReportListDetail) = viewModelScope.launch {
        val batchId = batchDetail.key
        _historicalBatchReportState.update { it.copy(selectedBatchId = batchId, viewingListScreen = false) }
        _uiState.update { state ->
            state.copy(historicalBatchState = state.historicalBatchState.copy(selectedBatchId = batchId, viewingListScreen = false, selectedBatchLoader = true))
        }
    }

    fun returnToHistoricalShiftList() = viewModelScope.launch {
        _historicalShiftReportState.update {
            it.copy(
                viewingListScreen = true,
                selectedShiftId = ""
            )
        }
        _uiState.update { state ->
            state.copy(
                historicalShiftState = state.historicalShiftState.copy(
                    viewingListScreen = true,
                    selectedShiftId = ""
                )
            )
        }
    }

    fun returnToHistoricalBatchList() = viewModelScope.launch {
        _historicalBatchReportState.update { it.copy(viewingListScreen = true, selectedBatchId = "") }
        _uiState.update { state ->
            state.copy(historicalBatchState = state.historicalBatchState.copy(viewingListScreen = true, selectedBatchId = ""))
        }
    }

    fun selectHistoricalShiftCashier(cashier: String) = viewModelScope.launch {
        if (cashier == "All Cashiers") {
            _historicalShiftReportState.update { it.copy(selectedCashier = "") }
        } else {
            _historicalShiftReportState.update { it.copy(selectedCashier = cashier) }
        }
    }

    fun selectHistoricalShiftTerminal(terminal: String) = viewModelScope.launch {
        if (terminal == "All Terminals") {
            _historicalShiftReportState.update { it.copy(selectedTerminal = "") }
        } else {
            _historicalShiftReportState.update { it.copy(selectedTerminal = terminal) }
        }
    }

    fun selectHistoricalBatchTerminal(terminal: String) = viewModelScope.launch {
        if (terminal == "All Terminals") {
            _historicalBatchReportState.update { it.copy(selectedTerminal = "") }
        } else {
            _historicalBatchReportState.update { it.copy(selectedTerminal = terminal) }
        }
    }

    fun onPresetPageChange(pageNumber: Int) {
        _uiState.update { it.copy(presetState = it.presetState.copy(currentPage = pageNumber)) }
    }

    fun onHistoricalShiftPageChange(pageNumber: Int) {
        _uiState.update { it.copy(historicalShiftState = it.historicalShiftState.copy(currentPage = pageNumber)) }
    }

    fun onHistoricalBatchPageChange(pageNumber: Int) {
        _uiState.update { it.copy(historicalBatchState = it.historicalBatchState.copy(currentPage = pageNumber)) }
    }

    private suspend fun getCurrentUser() {
        val currentUser = userDataSource.getCurrentUser()
        _uiState.update { it.copy(currentUser = currentUser) }
    }

    private suspend fun fetchStoreUsers(config: StoreConfig) {
        val storeCode = config.storeCode
        val storeUsers = userRepository.fetchStoreUsers(storeCode)
        var cashierIds = storeUsers?.users?.keys?.toList() ?: emptyList()
        cashierIds = listOf("All Cashiers") + cashierIds
        _uiState.update { it.copy(historicalShiftState = it.historicalShiftState.copy(cashiers = cashierIds)) }
    }
}