package com.swiftsku.swiftpos.ui.dispenser

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.ProductState


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun FuelProductsDialog(
    products: List<ProductState>,
    onProductSelect: (product: ProductState) -> Unit,
    onDismissRequest: () -> Unit
) {
    Dialog(onDismissRequest = onDismissRequest) {
        Surface(modifier = Modifier
            .padding(32.dp)
            .clip(shape = RoundedCornerShape(8.dp))
            .semantics { testTagsAsResourceId = true }) {
            LazyColumn(
                modifier = Modifier
                    .padding(top = 16.dp, bottom = 16.dp)
                    .width(300.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                products.forEachIndexed { index, product ->
                    item {
                        Text(
                            text = product.productName,
                            style = MaterialTheme.typography.h6,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable(onClick = { onProductSelect(product) })
                                .padding(12.dp),
                            textAlign = TextAlign.Center
                        )
                        if (index != products.size - 1) {
                            Divider(
                                color = Color.Gray,
                                thickness = 1.dp,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        }
    }
}