package com.swiftsku.swiftpos.ui.report

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.data.type.reportDefaults
import com.swiftsku.swiftpos.ui.components.DropDownMenu

@Composable
fun ReportTypeMenu(
    reportType: ReportType,
    items: List<ReportType> = reportDefaults,
    onSelectedMenuChange: (ReportType) -> Unit,
) {
    DropDownMenu(
        items = items,
        selectedItem = reportType,
        onSelectedMenuChange = onSelectedMenuChange,
        content = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = it.type,
                    modifier = Modifier.padding(vertical = 8.dp, horizontal = 12.dp),
                    style = MaterialTheme.typography.titleSmall
                )
                Icon(Icons.Outlined.KeyboardArrowDown, contentDescription = "Select Report Type")
            }
        },
        dropDownContent = {
            Text(text = it.type)
        }
    )
}