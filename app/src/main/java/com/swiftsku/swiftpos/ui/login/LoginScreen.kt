package com.swiftsku.swiftpos.ui.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.navigation.NavController
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.type.showDollar
import com.swiftsku.swiftpos.data.type.title
import com.swiftsku.swiftpos.ui.activity.base.BaseAppState
import com.swiftsku.swiftpos.ui.activity.base.sgConnectionColor
import com.swiftsku.swiftpos.ui.components.AppToast
import com.swiftsku.swiftpos.ui.components.CenteredComposable
import com.swiftsku.swiftpos.ui.dashboard.dialogs.NumericPad
import kotlinx.coroutines.delay

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun LoginScreen(
    navController: NavController,
    loginVM: LoginViewModel,
    scaffoldState: ScaffoldState = rememberScaffoldState(),
    appState: BaseAppState
) {
    val loginUiState by loginVM.uiState.collectAsState()
    val focusManager = LocalFocusManager.current
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data("file:///android_asset/svg/LogoWhite.svg")
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )

    var userName by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    val isInputValid = userName.isNotBlank() && password.isNotBlank()
    val isLoginEnabled = isInputValid && !loginUiState.isLoading

    var showPassword by remember { mutableStateOf(value = false) }
    val toast by loginVM.toastMessage.collectAsState()

    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                loginVM.validateCurrentUser()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(loginUiState.isLoggedIn) {
        if (loginUiState.isLoggedIn) {
            loginVM.moveToDashboard(navController)
        }
    }

    LaunchedEffect(toast) {
        if (toast != null) {
            delay(2000)
            loginVM.clearToast()
        }
    }

    if (loginUiState.isSessionLoading) {
        CenteredComposable {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(text = "Checking for user session")
                Spacer(modifier = Modifier.width(10.dp))
                CircularProgressIndicator()
            }
        }
    } else {
        Scaffold(
            scaffoldState = scaffoldState,
            modifier = Modifier.statusBarsPadding(),
        ) { contentPadding ->
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(contentPadding)
                    .semantics { testTagsAsResourceId = true },
                contentAlignment = Alignment.Center
            ) {

                Card(
                    modifier = Modifier
                        .padding(10.dp)
                        .width(360.dp),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Box(
                        modifier = Modifier.padding(
                            40.dp
                        ),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {


                            Image(
                                painter = painter,
                                contentDescription = "Swiftsku-POS",
                                modifier = Modifier
                                    .width(150.dp)
                                    .height(75.dp)
                            )

                            Spacer(modifier = Modifier.height(20.dp))

                            OutlinedTextField(
                                value = userName,
                                label = { Text(text = "Username") },
                                placeholder = { Text(text = "Enter Username") },
                                onValueChange = { userName = it },
                                keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() }),
                                keyboardOptions = KeyboardOptions.Default.copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Email
                                ),
                                modifier = Modifier.testTag("username")
                            )

                            Spacer(modifier = Modifier.height(20.dp))

                            OutlinedTextField(
                                value = password,
                                label = { Text(text = "Password") },
                                placeholder = { Text(text = "Enter Password") },
                                onValueChange = { password = it },
                                visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
                                keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() }),
                                keyboardOptions = KeyboardOptions.Default.copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Password
                                ),
                                trailingIcon = {
                                    if (showPassword) {
                                        IconButton(onClick = { showPassword = false }) {
                                            Icon(
                                                imageVector = Icons.Filled.Visibility,
                                                contentDescription = "hide_password"
                                            )
                                        }
                                    } else {
                                        IconButton(
                                            onClick = { showPassword = true }) {
                                            Icon(
                                                imageVector = Icons.Filled.VisibilityOff,
                                                contentDescription = "hide_password"
                                            )
                                        }
                                    }
                                },
                                modifier = Modifier.testTag("password")
                            )


                            Spacer(modifier = Modifier.height(20.dp))

                            Button(
                                enabled = isLoginEnabled,
                                onClick = { loginVM.onLogin(userName, password) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .testTag("login_button")
                            ) {
                                if (loginUiState.isLoading) {
                                    CircularProgressIndicator(
                                        strokeWidth = 2.dp,
                                        modifier = Modifier
                                            .size(20.dp)
                                            .aspectRatio(1f)
                                    )
                                } else {
                                    Text("Login")
                                }
                            }
                        }

                    }


                }

                if (loginUiState.showKeyPad != null) {
                    NumericPad(
                        numPadResult = loginUiState.showKeyPad!!,
                        dismissible = false,
                        onCancel = { loginVM.dismissNumPadDialog() },
                        onOkClick = { loginVM.onKeypadOkPress(it, userName) },
                        header = loginUiState.showKeyPad!!.title(),
                        showDollar = loginUiState.showKeyPad!!.showDollar(),
                    )
                }

                toast?.let { AppToast(it) }

                Row(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(10.dp)
                ) {
//                    Card {
//                        Icon(
//                            painter = painterResource(id = R.drawable.vpn),
//                            contentDescription = "VPN",
//                            tint = appState.vpnConnectionColor(),
//                            modifier = Modifier
//                                .size(46.dp)
//                                .padding(12.dp)
//                        )
//                    }
//                    Spacer(modifier = Modifier.width(10.dp))
                    Card {
                        Icon(
                            painter = painterResource(id = R.drawable.sg),
                            contentDescription = "SG",
                            modifier = Modifier
                                .size(46.dp)
                                .padding(12.dp),
                            tint = appState.sgConnectionColor()
                        )
                    }

                }

            }
        }
    }
}
