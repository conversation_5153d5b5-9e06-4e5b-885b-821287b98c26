package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Button
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.swiftsku.swiftpos.ui.dashboard.main.state.PaymentState
import com.swiftsku.swiftpos.ui.theme.Red

@Composable
fun PaymentRetryDialog(
    onDismissRequest: () -> Unit,
    paymentState: PaymentState,
    onRetryClick: () -> Unit,
    svgPath: String = "file:///android_asset/svg/ErrorDialogIcon.svg"
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data(svgPath)
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Surface(
            modifier = Modifier
                .padding(32.dp)
                .width(500.dp)
                .clip(shape = RoundedCornerShape(8.dp))

        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(32.dp)
            ) {

                Box(
                    contentAlignment = Alignment.Center, modifier = Modifier
                        .size(115.dp)
                        .clip(CircleShape)
                ) {
                    Box(
                        contentAlignment = Alignment.Center, modifier = Modifier
                            .size(85.dp)
                            .clip(CircleShape)
                            .background(Red)
                    ) {
                        if (paymentState.retryCount > 0) {
                            Text(
                                text = "${paymentState.retryCount}",
                                fontSize = 36.sp,
                                color = Color.White
                            )
                        } else {
                            Icon(
                                Icons.Rounded.Close,
                                contentDescription = null,
                                modifier = Modifier.size(50.dp),
                                tint = Color.White
                            )
                        }
                    }

                    if (paymentState.retrying) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(115.dp),
                            strokeWidth = 8.dp,
//                            color = Color.Yellow
                        )
                    }

                }
                Spacer(modifier = Modifier.height(30.dp))

                Text(

                    text = "TxnId: ${paymentState.transactionId}",
                    textAlign = TextAlign.Center,
                    fontSize = 28.sp
                )
                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = paymentState.message?.message ?: "Something went wrong",
                    textAlign = TextAlign.Center,
                    fontSize = 20.sp,
                    modifier = Modifier.padding(bottom = 10.dp)
                )

                if (paymentState.transactionId != null) {
                    Text(
                        text = "Card Attempts : ${paymentState.attempts}",
                        textAlign = TextAlign.Center,
                        fontSize = 18.sp
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                if (paymentState.showRetry) {
                    ClickableText(
                        text = AnnotatedString("Retry"),
                        style = TextStyle(
                            textAlign = TextAlign.Center,
                            fontSize = 24.sp,
                            color = Red
                        ),
                        onClick = {
                            onDismissRequest()
                            onRetryClick()
                        }
                    )
                }
                Spacer(modifier = Modifier.height(30.dp))

                if (!paymentState.retrying) {
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(text = "Ok", fontSize = 18.sp)
                    }
                }
            }
        }
    }
}