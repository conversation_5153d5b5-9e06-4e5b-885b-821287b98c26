package com.swiftsku.swiftpos.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.swiftsku.swiftpos.ui.activity.base.BaseAppState
import com.swiftsku.swiftpos.ui.dashboard.DashboardScreen
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.dashboard.recall.Recall
import com.swiftsku.swiftpos.ui.dashboard.recall.RecallViewModel
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TransactionHistoryDetail
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TxnHistoryDetailViewModel
import com.swiftsku.swiftpos.ui.dashboard.txnhistory.TxnHistory
import com.swiftsku.swiftpos.ui.dashboard.txnhistory.TxnHistoryViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.login.LoginScreen
import com.swiftsku.swiftpos.ui.login.LoginViewModel
import com.swiftsku.swiftpos.ui.presentation.CustomerPresentation
import com.swiftsku.swiftpos.ui.report.ReportViewModel

@Composable
fun SwiftPosNavGraph(
    navController: NavHostController = rememberNavController(),
    startDestination: String = Routes.Login.route,
    secondaryDisplay: CustomerPresentation?,
    appState: BaseAppState,
    dashboardVM: DashboardViewModel,
    loginVM: LoginViewModel,
    recallVM: RecallViewModel,
    transactionHistoryVM: TxnHistoryViewModel,
    transactionHistoryDetailVM: TxnHistoryDetailViewModel,
    creditsVM: CreditsViewModel,
    reportVM: ReportViewModel,
    fdcVM: FDCViewModel,
) {

    NavHost(navController = navController, startDestination = startDestination) {
        composable(Routes.Login.route) {
            LoginScreen(navController, appState = appState, loginVM = loginVM)
        }
        composable(Routes.Dashboard.route) {
            DashboardScreen(
                navController = navController,
                secondaryDisplay = secondaryDisplay,
                fdcVM = fdcVM,
                dashboardVM = dashboardVM,
                reportVM = reportVM,
                txnHistoryDetailVM = transactionHistoryDetailVM,
                creditsVM = creditsVM
            )
        }
        txnHistory(
            navController,
            transactionHistoryVM,
            dashboardVM,
            transactionHistoryDetailVM
        )
        dialog(
            Routes.Recall.route, dialogProperties = DialogProperties(
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Recall(navController = navController, recallVM = recallVM, dashboardVM = dashboardVM)
        }
    }
}

fun NavGraphBuilder.txnHistory(
    navController: NavHostController,
    transactionHistoryVM: TxnHistoryViewModel,
    dashboardVM: DashboardViewModel,
    transactionHistoryDetailVM: TxnHistoryDetailViewModel,
) = navigation(
    startDestination = Routes.TxnHistoryHome.route,
    route = Routes.TxnHistory.route
) {
    dialog(
        Routes.TxnHistoryHome.route, dialogProperties = DialogProperties(
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        TxnHistory(navController, transactionHistoryVM)
    }
    dialog(
        "${Routes.TxnHistoryDetail.route}/{transactionId}",
        dialogProperties = DialogProperties(
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        ),
        arguments = listOf(navArgument("transactionId") {
            type = NavType.StringType
        })
    ) { backStackEntry ->
        backStackEntry.arguments?.getString("transactionId")?.let {
            TransactionHistoryDetail(
                navController = navController,
                transactionId = it,
                transactionHistoryDetailVM = transactionHistoryDetailVM,
                dashboardVM = dashboardVM
            )
        }
    }
}