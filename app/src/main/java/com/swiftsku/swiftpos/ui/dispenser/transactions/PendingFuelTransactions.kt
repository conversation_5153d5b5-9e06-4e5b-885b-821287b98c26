package com.swiftsku.swiftpos.ui.dispenser.transactions

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Blue


@Composable
fun PendingFuelTransactions(
    transactions: List<SaleTransaction> = emptyList(),
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel,
) {
    val loadings by dashboardVM.loadings.collectAsState()

    if (transactions.isEmpty()) {
        dashboardVM.clearPendingFuelTxnFromUi()
    }

    Dialog(
        properties = DialogProperties(usePlatformDefaultWidth = false),
        onDismissRequest = { dashboardVM.clearPendingFuelTxnFromUi() },
    ) {
        Surface(
            modifier = Modifier
                .width(1000.dp)
                .heightIn(max = 400.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Column {
                    TopAppBar(
                        elevation = 4.dp,
                        title = { Text("Pending Fuel Transactions") }
                    )
                    FuelTransactionsHeader()
                    LazyColumn {
                        items(items = transactions) { fuelTransaction ->
                            PendingFuelTxnItem(
                                fuelTransaction = fuelTransaction,
                                dashboardVM = dashboardVM
                            )
                        }
                    }
                }
                if (loadings.contains(DashboardViewModel.Loadings.CLOSING_TXNS)) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .size(32.dp),
                        color = Blue,
                        strokeWidth = 4.dp
                    )
                }
            }
        }
    }
}
