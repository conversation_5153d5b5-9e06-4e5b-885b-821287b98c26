package com.swiftsku.swiftpos.ui.dashboard.recall

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.totalAmount
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel

@Composable
fun Recall(
    navController: NavController,
    recallVM: RecallViewModel,
    dashboardVM: DashboardViewModel
) {
    LaunchedEffect(Unit) {
        recallVM.refreshSavedTransactions()
    }

    Surface(
        modifier = Modifier
            .padding(top = 100.dp, bottom = 100.dp)
            .background(Color.White)
            .width(850.dp)

    ) {
        Column {
            TopAppBar(
                elevation = 4.dp,
                title = {
                    Text("Recall")
                },
                actions = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Filled.Clear, null)
                    }
                })
            RecallHeader()

            val uiState by recallVM.uiState.collectAsState()

            LazyColumn {
                items(uiState.pendingTransactions) { transaction ->
                    RecallListItem(
                        label = (transaction as? SaleTransaction)?.txnLabel ?: "",
                        transactionId = transaction.txnId,
                        time = transaction.txnStartTime.toDateTime(),
                        itemsCount = "${transaction.txnItems.filter { it.status == TransactionItemStatus.Normal }.size}",
                        totalAmount = transaction.totalAmount().toDollars(),
                        onDeleteClick = { recallVM.onDeleteClick(transaction) },
                        onRecallClick = {
                            dashboardVM.recallTransaction(transaction)
                            navController.popBackStack()
                        }
                    )
                }
            }
        }
    }
}
