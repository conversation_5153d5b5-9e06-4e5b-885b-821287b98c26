package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.core.text.isDigitsOnly
import com.swiftsku.swiftpos.ui.components.AppTextField
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.visualizers.DollarVisualTransformation


@Composable
fun FundCashDialog(
    onDismissRequest: () -> Unit, onSubmitRequest: (amount: Int, notes: String) -> Unit
) {

    var adjustmentAmount by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismissRequest) {
        Surface(
            modifier = Modifier
                .padding(20.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column(modifier = Modifier.padding(32.dp)) {
                AppTextField(
                    header = "Cash Amount",
                    value = adjustmentAmount,
                    onValueChange = { newValue ->
                        if (newValue.isDigitsOnly()) {
                            adjustmentAmount = newValue
                        }
                    },
                    label = "",
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    visualTransformation = DollarVisualTransformation
                )
                Spacer(modifier = Modifier.height(20.dp))
                AppTextField(
                    header = "Notes (Optional)",
                    value = notes,
                    onValueChange = { newValue -> notes = newValue },
                    label = "",
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                )
                Spacer(modifier = Modifier.height(20.dp))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1f))
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f),
                        colors = ButtonDefaults.buttonColors(backgroundColor = Red)
                    ) {
                        Text(text = "Cancel", color = Color.White)
                    }
                    Spacer(modifier = Modifier.width(10.dp))
                    Button(
                        onClick = {
                            val amount = adjustmentAmount.toIntOrNull()
                            if (amount != null && amount > 0 && amount <= Int.MAX_VALUE / 2) {
                                onSubmitRequest(amount, notes)
                                onDismissRequest()
                            }
                        },
                        enabled = (adjustmentAmount.toIntOrNull() ?: 0) > 0,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .weight(1f)
                    ) {
                        Text(text = "Add")
                    }
                }
            }
        }
    }
}

