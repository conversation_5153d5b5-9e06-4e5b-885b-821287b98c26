package com.swiftsku.swiftpos.ui.dashboard.dialogs
//
//import androidx.compose.foundation.background
//import androidx.compose.foundation.layout.Arrangement
//import androidx.compose.foundation.layout.Box
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Row
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxHeight
//import androidx.compose.foundation.layout.fillMaxSize
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.padding
//import androidx.compose.foundation.layout.width
//import androidx.compose.foundation.lazy.LazyColumn
//import androidx.compose.material.Button
//import androidx.compose.material.ButtonDefaults
//import androidx.compose.material.Divider
//import androidx.compose.material.Surface
//import androidx.compose.material.Text
//import androidx.compose.runtime.Composable
//import androidx.compose.ui.Alignment
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.unit.dp
//import com.swiftsku.swiftpos.data.model.EODReport
//import com.swiftsku.swiftpos.ui.components.EODReportContent
//import com.swiftsku.swiftpos.ui.theme.Red
//
//
//@Composable
//fun EODReportDialog(
//    onPrintClick: (EODReport) -> Unit,
//    onDeleteClick: () -> Unit,
//    modifier: Modifier = Modifier,
//    eodReport: EODReport
//) {
//    Box(
//        modifier = modifier
//            .fillMaxSize()
//            .background(Color(0xA0B4B4B4)),
//    ) {
//        Row() {
//            Box(
//                modifier = Modifier
//                    .weight(2f)
//                    .background(Color.Black)
//            )
//            Surface(
//                modifier = Modifier
//                    .weight(1f)
//                    .fillMaxHeight()
//                    .background(Color.White)
//            ) {
//                Column(
//                    verticalArrangement = Arrangement.SpaceBetween
//                ) {
//                    EODReportHeader(eodReport.bDate)
//                    LazyColumn(modifier = Modifier.weight(1f)) {
//                        eodReport.stats
//                            .filter { it.value.isNotEmpty() }
//                            .forEach { (title, info) ->
//                                item {
//                                    EODReportContent(
//                                        header = title,
//                                        details = info,
//                                    )
//                                }
//                            }
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(all = 16.dp)
//                    ) {
//                        Button(
//                            onClick = onDeleteClick,
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .weight(1f),
//                            colors = ButtonDefaults.buttonColors(backgroundColor = Red)
//                        ) {
//                            Text(text = "Cancel", color = Color.White)
//                        }
//                        Spacer(modifier = Modifier.width(10.dp))
//                        Button(
//                            onClick = { onPrintClick(eodReport) },
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .weight(1f)
//                        ) {
//                            Text(text = "Print")
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//}
//

