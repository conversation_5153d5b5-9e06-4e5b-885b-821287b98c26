package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Purple_Light
import org.burnoutcrew.reorderable.ItemPosition
import org.burnoutcrew.reorderable.ReorderableItem
import org.burnoutcrew.reorderable.detectReorderAfterLongPress
import org.burnoutcrew.reorderable.rememberReorderableLazyGridState
import org.burnoutcrew.reorderable.reorderable


@Composable
fun MenuKeysSection(
    modifier: Modifier = Modifier,
    dashboardVM: DashboardViewModel
) {

    val menuKeysList by dashboardVM.menuKeys.collectAsState()
    val trxItems by dashboardVM.txnItems.collectAsState()

    var menuKeys by remember { mutableStateOf<List<MenuKey>>(emptyList()) }

    LaunchedEffect(menuKeysList) {
        menuKeys = menuKeysList.filter { it.active }
    }

    fun isDragEnabled(draggedOver: ItemPosition, dragging: ItemPosition) = trxItems.isEmpty()

    val state = rememberReorderableLazyGridState(
        onMove = { from, to ->
            menuKeys = menuKeys.toMutableList().apply {
                val temp = this[from.index]
                this[from.index] = this[to.index]
                this[to.index] = temp
            }
        },
        onDragEnd = { _, _ ->
            /**
             * Save departments in the new order, here "departments" contains only the departments
             * that has "showInDashboard" set to true.
             */
            dashboardVM.saveReorderedMenuKeys(menuKeys)
        },
        canDragOver = ::isDragEnabled
    )

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        state = state.gridState,
        modifier = modifier.reorderable(state)
    ) {
        items(menuKeys, { it.id }) { item ->
            if (trxItems.isEmpty()) {
                ReorderableItem(state, item.id) { isDragging ->
                    val elevation = animateDpAsState(if (isDragging) 8.dp else 0.dp, label = "")
                    CardItem(
                        modifier = if (trxItems.isEmpty()) {
                            Modifier
                                .detectReorderAfterLongPress(state)
                                .shadow(elevation.value)
                        } else {
                            Modifier
                        },
                        title = item.name,
                        backgroundColor = Purple_Light,
                        onClick = { dashboardVM.onMenuKeySelected(item) },
                        isMultiline = true,
                        fontSize = 16.sp
                    )
                }
            } else {
                CardItem(
                    title = item.name,
                    backgroundColor = Purple_Light,
                    onClick = { dashboardVM.onMenuKeySelected(item) },
                    onLongClick = {
                        dashboardVM.showToast(
                            "Please clear cart to reorder", ToastMessageType.Info
                        )
                    },
                    isMultiline = true,
                    fontSize = 16.sp
                )
            }
        }
    }
}