package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.UID
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.ui.components.CardItem
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.Orange
import org.burnoutcrew.reorderable.ItemPosition
import org.burnoutcrew.reorderable.ReorderableItem
import org.burnoutcrew.reorderable.detectReorderAfterLongPress
import org.burnoutcrew.reorderable.rememberReorderableLazyGridState
import org.burnoutcrew.reorderable.reorderable

@Composable
fun DashboardPriceBook(
    modifier: Modifier,
    onPriceBookItemClick: (item: PluItem) -> Unit,
    priceBookItems: List<PluItem>,
    dashboardVM: DashboardViewModel
) {

    var bookItems by remember { mutableStateOf<List<PluItem>>(emptyList()) }

    LaunchedEffect(priceBookItems) { bookItems = priceBookItems }

    val trxItems by dashboardVM.txnItems.collectAsState()

    fun isDragEnabled(draggedOver: ItemPosition, dragging: ItemPosition) = trxItems.isEmpty()

    val state = rememberReorderableLazyGridState(
        onMove = { from, to ->
            bookItems = bookItems.toMutableList().apply {
                val temp = this[from.index]
                this[from.index] = this[to.index]
                this[to.index] = temp
            }
        },
        onDragEnd = { _, _ ->
            // Save updated order in storeConfig
            val pluOrder = bookItems.map { it.UID() }
            dashboardVM.savePluItemsOrder(pluOrder)
        },
        canDragOver = ::isDragEnabled
    )

    LazyVerticalGrid(
        columns = GridCells.Fixed(5),
        state = state.gridState,
        modifier = modifier.reorderable(state)
    ) {
        items(bookItems, { it.pluId + it.pluModifier }) { item ->
            if (trxItems.isEmpty()) {
                ReorderableItem(state, item.pluId + item.pluModifier) { isDragging ->
                    val elevation = animateDpAsState(if (isDragging) 8.dp else 0.dp, label = "")
                    CardItem(
                        modifier = if (trxItems.isEmpty()) {
                            Modifier
                                .detectReorderAfterLongPress(state)
                                .shadow(elevation.value)
                        } else {
                            Modifier
                        },
                        backgroundColor = Orange,
                        onClick = {
                            onPriceBookItemClick(item)
                        },
                        title = item.description.uppercase().take(33),
                        isMultiline = item.description.uppercase().take(33).length > 10,
                        fontSize = 14.sp
                    )
                }
            } else {
                CardItem(
                    backgroundColor = Orange,
                    onClick = {
                        onPriceBookItemClick(item)
                    },
                    onLongClick = {
                        dashboardVM.showToast(
                            "Please clear cart to reorder", ToastMessageType.Info
                        )
                    },
                    title = item.description.uppercase().take(33),
                    isMultiline = item.description.uppercase().take(33).length > 10,
                    fontSize = 14.sp
                )
            }
        }
    }
}