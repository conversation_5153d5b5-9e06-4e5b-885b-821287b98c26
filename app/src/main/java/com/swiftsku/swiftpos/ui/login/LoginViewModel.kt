package com.swiftsku.swiftpos.ui.login

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.data.couchbase.event.EventRepository
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import com.swiftsku.swiftpos.data.model.Event
import com.swiftsku.swiftpos.data.model.LoginEventData
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.User
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.type.EventType
import com.swiftsku.swiftpos.data.type.NumPadResult
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.di.qualifiers.UserRepositoryCache
import com.swiftsku.swiftpos.extension.openAsync
import com.swiftsku.swiftpos.extension.sCryptHash
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.ui.navigation.Routes
import com.swiftsku.swiftpos.utils.ENABLE_EMULATOR
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject


data class LoginUiState(
    val isLoading: Boolean = false,
    val isError: Boolean = false,
    val errorMessage: String = "",
    val showKeyPad: NumPadResult? = null,
    val isSessionLoading: Boolean = true,
    val isLoggedIn: Boolean = false
)


@HiltViewModel
class LoginViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @PrinterX private val printerXManager: ISunMiPrinter,
    private val eventRepository: EventRepository,
    @UserRepositoryCache private val userRepository: UserRepository,
    private val userDataSource: UserDataSource
) : ViewModel() {
    // Login UI State
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    private val _cashDrawerOpenTime = MutableStateFlow<Date?>(null)


    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    val toastMessage = _toastMessage

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)

    fun validateCurrentUser() = viewModelScope.launch {
        try {
            val isLoggedIn = userDataSource.isUserLoggedIn()
            _uiState.update {
                it.copy(
                    isSessionLoading = true,
                    isLoggedIn = isLoggedIn
                )
            }
            if (isLoggedIn) {
                delay(2000)
            }
        } catch (ex: Exception) {
            EventUtils.recordException(ex)
        } finally {
            _uiState.update { it.copy(isSessionLoading = false) }
        }
    }


    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    private fun clearState() = viewModelScope.launch {
        _uiState.emit(LoginUiState())
        _cashDrawerOpenTime.emit(null)
    }

    fun moveToDashboard(navController: NavController) = viewModelScope.launch {
        navController.navigate(Routes.Dashboard.route) {
            popUpTo(Routes.Login.route) {
                inclusive = true
            }
        }
        clearState()
    }

    fun onLogin(userName: String, password: String) = viewModelScope.launch {
        val storeConfig = _storeConfig.value


        if (userName.isEmpty() || storeConfig == null) {
            return@launch
        }
        val user =
            userRepository.fetchStoreUsers(storeConfig.storeCode)?.users?.get(userName)

        if (user == null) {
            _toastMessage.emit(
                ToastMessage(
                    message = "User does not exists",
                    type = ToastMessageType.Error
                )
            )
            return@launch
        }


        if (password.sCryptHash() != user.password) {
            _toastMessage.emit(
                ToastMessage(
                    message = "Invalid password",
                    type = ToastMessageType.Error
                )
            )
            return@launch
        }


        if (ENABLE_EMULATOR) {
            _uiState.update {
                it.copy(
                    isLoading = false,
                    showKeyPad = NumPadResult.CashCountInDrawer
                )
            }
            return@launch
        }

        printerXManager.validateCashDrawer(
            onCashDrawerNotAvailable = {
                _toastMessage.emit(ToastMessage(message = "Cash drawer is not available, Please connect to login"))
            },
            onCashDrawerAvailable = {
                _uiState.update { state -> state.copy(isLoading = true) }
                if (!it.isOpen) {
                    it.openAsync()
                }
                _cashDrawerOpenTime.emit(Date())
                _uiState.update { state ->
                    state.copy(
                        isLoading = false,
                        showKeyPad = NumPadResult.CashCountInDrawer
                    )
                }
            }
        )


    }

    fun dismissNumPadDialog() {

    }

    private suspend fun saveCurrentUser(user: User): Boolean {
        return userDataSource.saveUser(
            UserDTO(
                loggedIn = true,
                userType = user.userType,
                firstName = user.firstName,
                lastName = user.lastName,
                username = user.username,
                email = user.email
            )
        )
    }

    fun onKeypadOkPress(value: Float, userName: String) = viewModelScope.launch {
        // update cash ledger and take user to dashboard

        val amount = value / 100f

        val storeConfig = _storeConfig.value
        val storeCode = storeConfig?.storeCode ?: ""
        val user =
            userRepository.fetchStoreUsers(storeCode)?.users?.get(userName)
                ?: return@launch

        if (ENABLE_EMULATOR) {
            val success = eventRepository.logIn(
                Event(
                    eventId = generateTransactionId(
                        storeCode,
                        posNumber = storeConfig?.posNumber ?: "1"
                    ),
                    event = EventType.Login,
                    eventStartTime = _cashDrawerOpenTime.value,
                    eventEndTime = Date(),
                    storeCode = storeCode,
                    cashierId = userName,
                    eventData = LoginEventData(cashBalance = amount)
                )
            )

            val saveUser = saveCurrentUser(user)
            if (success && saveUser) {
                _uiState.update { it.copy(isLoggedIn = true) }
            }
            return@launch
        }

        printerXManager.validateCashDrawer(
            onCashDrawerAvailable = {
                if (it.isOpen) {
                    // please close the drawer message
                    _toastMessage.emit(ToastMessage(message = "Please close the cash drawer to continue with login"))

                } else {

                    val success = eventRepository.logIn(
                        Event(
                            eventId = generateTransactionId(
                                storeCode,
                                posNumber = storeConfig?.posNumber ?: "1"
                            ),
                            event = EventType.Login,
                            eventStartTime = _cashDrawerOpenTime.value,
                            eventEndTime = Date(),
                            storeCode = storeCode,
                            cashierId = userName,
                            eventData = LoginEventData(cashBalance = amount)
                        )
                    )
                    val saveUser = saveCurrentUser(user)

                    Logger.d("saveUser: $saveUser")
                    if (success && saveUser) {
                        _uiState.update { state -> state.copy(isLoggedIn = true) }
                    }
                }
            },
            onCashDrawerNotAvailable = {
                _toastMessage.emit(ToastMessage(message = "Cash drawer is not available, Please connect to login"))
            }
        )


    }

    fun clearToast() = viewModelScope.launch {
        _toastMessage.emit(null)
    }

    fun showToast(message: ToastMessage) = viewModelScope.launch {
        _toastMessage.emit(message)
    }


}
