package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.SearchBar
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.pricebook.PricebookViewModel
import com.swiftsku.swiftpos.utils.Result.Error
import com.swiftsku.swiftpos.utils.Result.Loading
import com.swiftsku.swiftpos.utils.Result.Success


@Composable
fun PbSearchDialog(
    dashboardVM: DashboardViewModel,
    priceBookVM: PricebookViewModel = hiltViewModel()
) {
    val pbSearchResults = priceBookVM.searchResults.collectAsState().value
    var searchQuery by remember { mutableStateOf("") }

    Dialog(
        onDismissRequest = {
            dashboardVM.hidePbSearchDialog()
            priceBookVM.clearSearchResults()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .padding(16.dp)
                .width(800.dp)
                .height(450.dp)
                .clip(shape = RoundedCornerShape(8.dp))
        ) {
            Column {
                SearchBar(
                    query = searchQuery,
                    onQueryChange = { searchQuery = it },
                    modifier = Modifier.fillMaxWidth(),
                    searchHint = "Search Pricebook item by name",
                    onDebounceQueryChange = {
                        dashboardVM.storeConfig.value?.storeCode?.let {
                            priceBookVM.searchPricebook(it, searchQuery)
                        }
                    },
                )
                when (pbSearchResults) {
                    null -> {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Search by entering pricebook name above",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }

                    is Loading -> {
                        Spacer(modifier = Modifier.height(16.dp))
                        CircularProgressIndicator(
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }

                    is Error -> {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = pbSearchResults.errorMessage,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error,
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }

                    is Success -> {
                        val results = pbSearchResults.data.pluItems
                        results?.let {
                            if (it.isEmpty()) {
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "No results",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.align(Alignment.CenterHorizontally)
                                )
                            } else {
                                PriceBookHeader()
                                LazyColumn {
                                    items(it) { item ->
                                        PriceBookListItem(
                                            description = item.description.toString(),
                                            barcode = item.pluId,
                                            modifier = item.pluModifier,
                                            price = item.price.toDollars(),
                                            onClick = {
                                                dashboardVM.hidePbSearchDialog()
                                                priceBookVM.clearSearchResults()
                                                dashboardVM.storeConfig.value?.storeCode?.let {
                                                    dashboardVM.onPbSearchItemClick(it, item)
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                        } ?: run {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "No results",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.align(Alignment.CenterHorizontally)
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}