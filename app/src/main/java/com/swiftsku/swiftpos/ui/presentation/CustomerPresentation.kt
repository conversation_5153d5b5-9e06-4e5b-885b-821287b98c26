package com.swiftsku.swiftpos.ui.presentation

import android.annotation.SuppressLint
import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import android.view.View
import android.widget.ImageView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import coil.load
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.AvailableOffer
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.databinding.SecondaryDisplayBinding
import com.swiftsku.swiftpos.domain.promotion.getPromoTextAlt
import com.swiftsku.swiftpos.domain.promotion.getSavingAmount
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.grandTotal
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

data class LoyaltyInput(
    val number: String,
    val inputTs: Long,
)

class CustomerPresentation(
    outerContext: Context?,
    display: Display?
) : Presentation(outerContext, display), View.OnClickListener {

    private lateinit var binding: SecondaryDisplayBinding
    private val listAdapter = CartListAdapter(mutableListOf())
    private val offersAdapter = OffersAdapter(mutableListOf(), false)

    private val _phoneNumber = MutableStateFlow<LoyaltyInput?>(null)
    val phoneNumber = _phoneNumber

    private val scope = CoroutineScope(Dispatchers.Main)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondaryDisplayBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews()
    }

    private fun initViews() {
        binding.secondHalf.phonePad.apply {
            btBack.setOnClickListener(this@CustomerPresentation)
            btClear.setOnClickListener(this@CustomerPresentation)
            bt0.setOnClickListener(this@CustomerPresentation)
            bt1.setOnClickListener(this@CustomerPresentation)
            bt2.setOnClickListener(this@CustomerPresentation)
            bt3.setOnClickListener(this@CustomerPresentation)
            bt4.setOnClickListener(this@CustomerPresentation)
            bt5.setOnClickListener(this@CustomerPresentation)
            bt6.setOnClickListener(this@CustomerPresentation)
            bt7.setOnClickListener(this@CustomerPresentation)
            bt8.setOnClickListener(this@CustomerPresentation)
            bt9.setOnClickListener(this@CustomerPresentation)
            btOk.setOnClickListener(this@CustomerPresentation)
        }

        // cart
        binding.cartLayout.rvCart.apply {
            layoutManager = LinearLayoutManager(this.context)
            adapter = listAdapter
        }
        binding.secondHalf.rvOffers.apply {
            layoutManager = LinearLayoutManager(this.context)
            adapter = offersAdapter
        }

        updateCartSummary(transactionSummary = TransactionSummary())
    }

    @SuppressLint("SetTextI18n")
    fun updateCartSummary(transactionSummary: TransactionSummary) {
        binding.cartLayout.tvSubTotalValue.text =
            transactionSummary.transactionTotalNetAmount.toDollars()
        binding.cartLayout.tvTaxValue.text =
            transactionSummary.transactionTotalTaxNetAmount.toDollars()
        binding.cartLayout.tvTotalValue.text = transactionSummary.grandTotal().toDollars()
        updatePromotion(transactionSummary.promotionApplied)
        updateAmountCollected(transactionSummary.totalAmountCollected())
        updateCoupon(transactionSummary.couponAmount)
        updateLottery(transactionSummary.lotteryAmount)
        val cardFee = transactionSummary.calculatedFees.find { it.type == FeeType.CARD_PROCESSING }
        updateCardFee(transactionSummary.pendingAmount(), cardFee)
    }

    fun showDisplay(show: Boolean) {
        binding.linearLayout.visibility = if (show) View.VISIBLE else View.GONE
    }

    @SuppressLint("SetTextI18n")
    private fun updatePromotion(amount: Float) {
        binding.cartLayout.apply {
            tvPromotionValue.text = (amount * -1).toDollars()
            if (amount > 0) {
                llPromotion.visibility = View.VISIBLE
            } else {
                llPromotion.visibility = View.GONE
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateAmountCollected(amount: Float) {
        binding.cartLayout.apply {
            tvAmountCollectedValue.text = amount.toDollars()
            if (amount > 0) {
                llAmountCollected.visibility = View.VISIBLE
            } else {
                llAmountCollected.visibility = View.GONE
            }
        }
    }

    @SuppressLint("SetTextI18n")
    fun updateCartItems(items: List<TransactionItem>) {
        listAdapter.refresh(items)
        binding.cartLayout.header.tvQty.text = "QTY (${items.sumOf { it.quantity }})"
        if (items.isEmpty()) {
            resetTransaction()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateCoupon(coupon: Float) {
        binding.cartLayout.apply {
            if (coupon > 0) {
                tvCouponValue.text = (coupon * -1).toDollars()
                llCoupon.visibility = View.VISIBLE
            } else {
                llCoupon.visibility = View.GONE
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateLottery(lottery: Float) {
        binding.cartLayout.apply {
            if (lottery > 0) {
                tvLotteryValue.text = (lottery * -1).toDollars()
                llLottery.visibility = View.VISIBLE
            } else {
                llLottery.visibility = View.GONE
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateCardFee(pendingAmount: Float, cardFee: AppliedFee?) {
        binding.cartLayout.apply {
            cardFee?.let {
                llCardFee.visibility = View.VISIBLE
                llCardTotal.visibility = View.VISIBLE
                llCash.visibility = View.VISIBLE
                llCard.visibility = View.VISIBLE
                tvCardFeeTitle.text = "Card Fee(${it.info})"
                tvCardFeeValue.text = it.amount.toDollars()
                tvCardTotalValue.text = pendingAmount.plus(it.amount).toDollars()
                tvCashTotal.text = "Cash: ${pendingAmount.toDollars()}"
                tvCardTotal.text = "Card: ${pendingAmount.plus(it.amount).toDollars()}"
            } ?: run {
                llCardFee.visibility = View.GONE
                llCardTotal.visibility = View.GONE
                llCash.visibility = View.GONE
                llCard.visibility = View.GONE
            }
        }
    }

    fun getUserPhoneNumber() {
        // show phone number input
        binding.secondHalf.phonePad.root.visibility = View.VISIBLE
    }

    private fun resetTransaction() {
        // reset transaction
        binding.secondHalf.phonePad.etPhone.setText("")
        binding.secondHalf.phonePad.root.visibility = View.GONE
    }

    fun dismissUserInput() {
        resetTransaction()
    }

    override fun onClick(p0: View?) {
        when (p0?.id) {
            R.id.bt0 -> {
                binding.secondHalf.phonePad.etPhone.append("0")
            }

            R.id.bt1 -> {
                binding.secondHalf.phonePad.etPhone.append("1")
            }

            R.id.bt2 -> {
                binding.secondHalf.phonePad.etPhone.append("2")
            }

            R.id.bt3 -> {
                binding.secondHalf.phonePad.etPhone.append("3")
            }

            R.id.bt4 -> {
                binding.secondHalf.phonePad.etPhone.append("4")
            }

            R.id.bt5 -> {
                binding.secondHalf.phonePad.etPhone.append("5")
            }

            R.id.bt6 -> {
                binding.secondHalf.phonePad.etPhone.append("6")
            }

            R.id.bt7 -> {
                binding.secondHalf.phonePad.etPhone.append("7")
            }

            R.id.bt8 -> {
                binding.secondHalf.phonePad.etPhone.append("8")
            }

            R.id.bt9 -> {
                binding.secondHalf.phonePad.etPhone.append("9")
            }

            R.id.btBack -> {
                val text = binding.secondHalf.phonePad.etPhone.text
                if (text.isNotEmpty()) {
                    val length = text.length
                    binding.secondHalf.phonePad.etPhone.setText(text.subSequence(0, length - 1))
                }

            }

            R.id.btClear -> {
                binding.secondHalf.phonePad.etPhone.setText("")
            }

            R.id.btOk -> {
                val text = binding.secondHalf.phonePad.etPhone.text
                if (text.isNotEmpty() && text.length >= 10 && text.length <= 11) {
                    scope.launch {
                        _phoneNumber.emit(LoyaltyInput(text.toString(), System.currentTimeMillis()))
                    }
                } else {
                    Toast.makeText(this.context, "Invalid Phone Number", Toast.LENGTH_SHORT).show()
                }
            }
        }

    }

    override fun onDetachedFromWindow() {
        scope.cancel()
        super.onDetachedFromWindow()
    }

    @SuppressLint("SetTextI18n")
    fun updateOffers(
        availableOffers: List<AvailableOffer>,
        loyaltyId: String?,
        newOffersUi: Boolean = false
    ) {
        offersAdapter.updateOffers(availableOffers, newOffersUi)
        if (newOffersUi) {
            binding.secondHalf.apply {
                when {
                    availableOffers.isEmpty() -> {
                        rvOffers.visibility = View.GONE
                        clSingleOfferView.visibility = View.GONE
                        if (loyaltyId == null) {
                            clLoyaltyCard.root.visibility = View.VISIBLE
                        } else {
                            clLoyaltyCard.root.visibility = View.GONE
                        }
                    }

                    availableOffers.size == 1 -> {
                        val offer = availableOffers.first()
                        val savingAmount = getSavingAmount(offer)
                        val promoDescription = getPromoTextAlt(offer, true)
                        clSingleOfferView.visibility = View.VISIBLE
                        clLoyaltyCard.root.visibility = View.GONE

                        loadProductImage(
                            ivProduct,
                            offer,
                            {
                                tvSavingAmount.text = "You Save ${savingAmount.toDollars()}"
                                tvOfferDescription.text = promoDescription
                                clSingleOfferView.visibility = View.VISIBLE
                                rvOffers.visibility = View.GONE
                            },
                            {
                                clSingleOfferView.visibility = View.GONE
                                rvOffers.visibility = View.VISIBLE
                            }
                        )
                    }

                    else -> {
                        rvOffers.visibility = View.VISIBLE
                        clSingleOfferView.visibility = View.GONE
                        clLoyaltyCard.root.visibility = View.GONE
                    }
                }
            }
        } else {
            if (availableOffers.isNotEmpty()) {
                binding.secondHalf.tvNoOffers.visibility = View.GONE
            } else {
                binding.secondHalf.tvNoOffers.visibility = View.VISIBLE
            }
        }
    }

    private fun loadProductImage(
        imageView: ImageView,
        availableOffer: AvailableOffer,
        successCallback: () -> Unit,
        failureCallback: () -> Unit
    ) {
        val pluId = availableOffer.pluItem.pluId
        val imageUrl = "https://d3vimmhiuf8a0v.cloudfront.net/$pluId.png"

        imageView.load(imageUrl) {
            placeholder(R.drawable.ic_item_placeholder)
            error(R.drawable.ic_item_placeholder)
            listener(
                onError = { _, _ ->
                    failureCallback.invoke()
                },
                onSuccess = { _, _ ->
                    successCallback.invoke()
                }
            )
        }
    }
}