package com.swiftsku.swiftpos.ui.presentation

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.AvailableOffer
import com.swiftsku.swiftpos.databinding.OfferListItemBinding
import com.swiftsku.swiftpos.domain.promotion.getPromoTextAlt
import com.swiftsku.swiftpos.domain.promotion.getSavingAmount
import com.swiftsku.swiftpos.extension.toDollars


class OffersAdapter(
    val items: MutableList<AvailableOffer>,
    private var newOffersUi: Boolean
) : RecyclerView.Adapter<OffersAdapter.OfferViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OfferViewHolder {
        return OfferViewHolder(
            OfferListItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateOffers(offers: List<AvailableOffer>, newOffersUi: Boolean) {
        this.items.clear()
        this.items.addAll(offers)
        this.newOffersUi = newOffersUi
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return items.size
    }

    private fun getItem(position: Int): AvailableOffer = items[position]

    override fun onBindViewHolder(holder: OfferViewHolder, position: Int) {
        holder.setData(getItem(position))
    }

    inner class OfferViewHolder(
        private val binding: OfferListItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun setData(availableOffer: AvailableOffer) {
            if (newOffersUi) {
                val savingAmount = getSavingAmount(availableOffer)
                val promoDescription = getPromoTextAlt(availableOffer)

                loadProductImage(binding.ivProduct, availableOffer)

                binding.tvOfferText.text = promoDescription
                binding.tvProductDescription.text = availableOffer.pluItem.description
                binding.tvSavingAmount.text = "Save ${savingAmount.toDollars()}"
            } else {
                binding.tvOfferText.text = availableOffer.promoDescription
                binding.grpNewUi.visibility = View.GONE
            }
        }

        private fun loadProductImage(imageView: ImageView, availableOffer: AvailableOffer) {
            val pluId = availableOffer.pluItem.pluId
            val imageUrl = "https://d3vimmhiuf8a0v.cloudfront.net/$pluId.png"

            imageView.load(imageUrl) {
                placeholder(R.drawable.ic_item_placeholder)
                error(R.drawable.ic_item_placeholder)
            }
        }
    }
}