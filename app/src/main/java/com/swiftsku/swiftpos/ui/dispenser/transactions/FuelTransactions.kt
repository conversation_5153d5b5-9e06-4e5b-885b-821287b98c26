package com.swiftsku.swiftpos.ui.dispenser.transactions

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel

@Composable
fun FuelTransactions(
    fuelPump: FuelPumpState,
    onDismissRequest: () -> Unit,
    onItemClick: (SaleTransaction) -> Unit,
    transactions: List<SaleTransaction> = emptyList(),
    onRecall: (SaleTransaction) -> Unit,
    fdcVM: FDCViewModel
) {
    Dialog(
        properties = DialogProperties(usePlatformDefaultWidth = false),
        onDismissRequest = onDismissRequest
    ) {
        Surface(
            modifier = Modifier
                .width(1000.dp)
                .padding(top = 100.dp, bottom = 100.dp)

        ) {
            Column {
                TopAppBar(
                    elevation = 4.dp,
                    title = { Text("Pump ${fuelPump.pumpNo} - Fuel Transaction History") },
                    actions = {
                        IconButton(onClick = onDismissRequest) {
                            Icon(Icons.Filled.Clear, null)
                        }
                    }
                )
                FuelTransactionsHeader()
                LazyColumn {
                    items(items = transactions) { fuelTransaction ->
                        FuelTransactionListItem(
                            onItemClick = onItemClick,
                            fuelTransaction = fuelTransaction,
                            onRecall = onRecall,
                            onReprint = { fdcVM.reprintSaleInvoice(fuelTransaction.txnId) }
                        )
                    }
                }
            }
        }
    }
}