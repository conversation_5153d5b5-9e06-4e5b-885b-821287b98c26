package com.swiftsku.swiftpos.di.qualifiers

import javax.inject.Qualifier

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class IODispatcher

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class MainDispatcher

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class DefaultDispatcher

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class UnconfinedDispatcher


@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class IOCoroutineScope

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class MainCoroutineScope
