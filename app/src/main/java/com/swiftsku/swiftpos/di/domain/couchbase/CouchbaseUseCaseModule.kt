package com.swiftsku.swiftpos.di.domain.couchbase

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.config.GetTerminalConfigUseCase
import com.swiftsku.swiftpos.domain.couchbase.GetSGCredentialUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object CouchbaseUseCaseModule {
    @Provides
    fun provideGetSGCredentialUseCase(
        getTerminalConfigUseCase: GetTerminalConfigUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetSGCredentialUseCase = GetSGCredentialUseCase(getTerminalConfigUseCase, dispatcher)
}