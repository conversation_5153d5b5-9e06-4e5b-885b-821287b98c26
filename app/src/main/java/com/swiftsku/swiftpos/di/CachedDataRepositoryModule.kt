package com.swiftsku.swiftpos.di

import com.swiftsku.swiftpos.data.cached.user.CachedUserRepository
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.di.qualifiers.RemoteUserRepository
import com.swiftsku.swiftpos.di.qualifiers.UserRepositoryCache
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CachedDataRepositoryModule {

    @UserRepositoryCache
    @Provides
    @Singleton
    fun provideUserRepository(
        @RemoteUserRepository userRepository: UserRepository
    ): UserRepository = CachedUserRepository(userRepository)


}