package com.swiftsku.swiftpos.di

import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.modules.websocket.WebSocketOkHttp
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentProcessor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object WebSocketModule {
    @Provides
    @Singleton
    fun provideWebSocketRequest(): Request = Request.Builder()
        .url(BuildConfig.PAYMENT_WEBSOCKET_URL)
        .build()

    @Singleton
    @Provides
    fun provideOkHttpClient(): OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(5, TimeUnit.SECONDS)
        .readTimeout(15, TimeUnit.SECONDS)
        .writeTimeout(5, TimeUnit.SECONDS)
        .build()

    @Singleton
    @Provides
    fun provideWebSocketClient(
        okHttpClient: OkHttpClient,
        @IOCoroutineScope scope: CoroutineScope,
        webSocketRequest: Request,
        json: Json
    ): WebSocketOkHttp =
        WebSocketOkHttp(
            okHttpClient = okHttpClient,
            scope = scope,
            webSocketRequest = webSocketRequest,
            json = json
        )

    @Provides
    @Singleton
    fun providePaymentProcessor(
        @IODispatcher dispatcher: CoroutineDispatcher,
        webSocketOkHttp: WebSocketOkHttp,
        json: Json
    ) = PaymentProcessor(dispatcher, webSocketOkHttp, json)
}