package com.swiftsku.swiftpos.di.domain.printer

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LegacyPrinter
import com.swiftsku.swiftpos.domain.printer.PrintEODReportUseCase
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@InstallIn(SingletonComponent::class)
@Module
object PrinterUseCaseModule {

    @Provides
    fun providePrintTransactionUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        @LegacyPrinter legacyPrinterManager: ISunMiPrinter,
    ): PrintTransactionUseCase = PrintTransactionUseCase(legacyPrinterManager, dispatcher)

    @Provides
    fun providePrintEODReportUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        @LegacyPrinter legacyPrinterManager: ISunMiPrinter,
    ): PrintEODReportUseCase = PrintEODReportUseCase(legacyPrinterManager, dispatcher)
}