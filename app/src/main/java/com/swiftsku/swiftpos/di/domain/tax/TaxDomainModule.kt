package com.swiftsku.swiftpos.di.domain.tax

import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.pricebook.EBTCalculationsUseCase
import com.swiftsku.swiftpos.domain.tax.GetCachedTaxUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxForAmountMapUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher


@Module
@InstallIn(SingletonComponent::class)
object TaxDomainModule {

    @Provides
    fun providesGetCachedTaxUseCase(
        refDataRepository: RefDataRepository,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetCachedTaxUseCase = GetCachedTaxUseCase(refDataRepository, dispatcher)

    @Provides
    fun providesGetTaxForAmountMapUseCase(
        getTaxCachedUseCase: GetCachedTaxUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetTaxForAmountMapUseCase = GetTaxForAmountMapUseCase(getTaxCachedUseCase, dispatcher)

    @Provides
    fun providesEBTCalculationsUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher
    ) = EBTCalculationsUseCase(dispatcher)

}