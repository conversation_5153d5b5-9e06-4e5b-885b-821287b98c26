package com.swiftsku.swiftpos.di.domain.config

import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import com.swiftsku.swiftpos.di.qualifiers.RemoteConfigRepository
import com.swiftsku.swiftpos.domain.config.GetPresetConfigUseCase
import com.swiftsku.swiftpos.domain.config.GetStoreConfigUseCase
import com.swiftsku.swiftpos.domain.config.GetTerminalConfigUseCase
import com.swiftsku.swiftpos.domain.config.ListenForStoreConfigUseCase
import com.swiftsku.swiftpos.domain.config.SavePLUItemsOrderUseCase
import com.swiftsku.swiftpos.domain.config.UpdateEPXTerminalIPUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ConfigUseCaseModule {
    @Provides
    @Singleton
    fun provideGetStoreConfigUseCase(
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetStoreConfigUseCase = GetStoreConfigUseCase(storeRepository, dispatcher)


    @Provides
    @Singleton
    fun provideGetPresetConfigUseCase(
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository,
    ): GetPresetConfigUseCase = GetPresetConfigUseCase(storeRepository::presetConfigFlow)

    @Provides
    @Singleton
    fun provideUpdateEPXTerminalIPUseCase(
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository,
    ): UpdateEPXTerminalIPUseCase = UpdateEPXTerminalIPUseCase(storeRepository::updateEpxTerminalIp)

    @Provides
    @Singleton
    fun provideSavePLUItemsOrderUseCase(
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository,
    ): SavePLUItemsOrderUseCase = SavePLUItemsOrderUseCase(storeRepository::savePluItemsOrder)

    @Provides
    @Singleton
    fun provideListenForStoreConfigUseCase(
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository,
    ): ListenForStoreConfigUseCase = ListenForStoreConfigUseCase(storeRepository::storeConfigFlow)


    @Provides
    @Singleton
    fun provideTerminalConfigUseCase(
        @RemoteConfigRepository reportRepository: StoreRepository
    ): GetTerminalConfigUseCase {
        return GetTerminalConfigUseCase(reportRepository::fetchTerminalConfig)
    }

}