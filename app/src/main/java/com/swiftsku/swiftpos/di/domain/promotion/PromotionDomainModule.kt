package com.swiftsku.swiftpos.di.domain.promotion

import com.swiftsku.swiftpos.data.couchbase.promotion.PromotionsRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.promotion.GetILTDetailsUseCase
import com.swiftsku.swiftpos.domain.promotion.GetMMTDetailsUseCase
import com.swiftsku.swiftpos.domain.promotion.GetPromotionUserCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object PromotionDomainModule {

    @Provides
    fun providesGetILTDetailUseCase(
        promotionsRepository: PromotionsRepository,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetILTDetailsUseCase = GetILTDetailsUseCase(promotionsRepository, dispatcher)

    @Provides
    fun providesGetMMTDetailUseCase(
        promotionsRepository: PromotionsRepository,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetMMTDetailsUseCase = GetMMTDetailsUseCase(promotionsRepository, dispatcher)

    @Provides
    fun providesGetPromotionUseCase(
        iltDetailUseCase: GetILTDetailsUseCase,
        mmtDetailUseCase: GetMMTDetailsUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetPromotionUserCase = GetPromotionUserCase(mmtDetailUseCase, iltDetailUseCase, dispatcher)


}