package com.swiftsku.swiftpos.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.ExperimentalMultiProcessDataStore
import androidx.datastore.core.MultiProcessDataStoreFactory
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.data.local.datastore.notification.NotificationsDataSource
import com.swiftsku.swiftpos.data.local.datastore.notification.NotificationsProto
import com.swiftsku.swiftpos.data.local.datastore.user.User.UserProto
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.UserProtoSerializer
import com.swiftsku.swiftpos.data.local.datastore.notification.NotificationsProtoDataSource
import com.swiftsku.swiftpos.data.local.datastore.notification.NotificationsProtoSerializer
import com.swiftsku.swiftpos.data.local.datastore.user.proto.UserProtoDataSource
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.utils.NOTIFICATION_DATA_STORE_FILE_NAME
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import java.io.File
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object AppDataStoreModule {

    @OptIn(ExperimentalMultiProcessDataStore::class)
    @Singleton
    @Provides
    fun provideUserDataStoreProto(@ApplicationContext context: Context): DataStore<UserProto> =
        MultiProcessDataStoreFactory.create(
            serializer = UserProtoSerializer(),
            produceFile = {
                File("${context.cacheDir.path}/${BuildConfig.USER_DATA_STORE_FILE_NAME}")
            }
        )

    @Singleton
    @Provides
    fun provideUserDataStore(
        @IODispatcher dispatcher: CoroutineDispatcher,
        @IOCoroutineScope scope: CoroutineScope,
        userProtoDataStore: DataStore<UserProto>
    ): UserDataSource =
        UserProtoDataSource(
            dispatcher = dispatcher,
            appScope = scope,
            dataStore = userProtoDataStore
        )

    @OptIn(ExperimentalMultiProcessDataStore::class)
    @Singleton
    @Provides
    fun provideNotificationsDataStore(
        @ApplicationContext context: Context
    ): DataStore<NotificationsProto> = MultiProcessDataStoreFactory.create(
        serializer = NotificationsProtoSerializer,
        produceFile = {
            File("${context.cacheDir.path}/$NOTIFICATION_DATA_STORE_FILE_NAME")
        }
    )

    @Singleton
    @Provides
    fun provideNotificationsDataSource(
        @IODispatcher dispatcher: CoroutineDispatcher,
        dataStore: DataStore<NotificationsProto>
    ): NotificationsDataSource = NotificationsProtoDataSource(
        dispatcher = dispatcher,
        dataStore = dataStore
    )
}
