package com.swiftsku.swiftpos.di

import com.swiftsku.swiftpos.di.qualifiers.DefaultDispatcher
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.MainCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.MainDispatcher
import com.swiftsku.swiftpos.di.qualifiers.UnconfinedDispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CoroutineModule {

    @Provides
    @IODispatcher
    fun providesDispatcherIO(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    @MainDispatcher
    fun providesDispatcherMain(): CoroutineDispatcher = Dispatchers.Main


    @Provides
    @DefaultDispatcher
    fun providesDispatcherDefault(): CoroutineDispatcher = Dispatchers.Default


    @Provides
    @UnconfinedDispatcher
    fun providesDispatcherIOUndefined(): CoroutineDispatcher = Dispatchers.Unconfined


    @Singleton
    @MainCoroutineScope
    @Provides
    fun providesMainCoroutineScope(
        @MainDispatcher defaultDispatcher: CoroutineDispatcher
    ): CoroutineScope = CoroutineScope(SupervisorJob() + defaultDispatcher)

    @Singleton
    @IOCoroutineScope
    @Provides
    fun providesIOCoroutineScope(
        @IODispatcher defaultDispatcher: CoroutineDispatcher
    ): CoroutineScope = CoroutineScope(SupervisorJob() + defaultDispatcher)

}