package com.swiftsku.swiftpos.di

import android.graphics.Bitmap
import com.swiftsku.swiftpos.di.qualifiers.BitmapRegistry
import com.swiftsku.swiftpos.modules.registry.Registry
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object AppRegistry {
    @Provides
    @Singleton
    @BitmapRegistry
    fun provideBitmapRegistry(): Registry<String, Bitmap> = Registry()
}