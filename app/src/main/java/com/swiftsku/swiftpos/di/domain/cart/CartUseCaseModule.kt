package com.swiftsku.swiftpos.di.domain.cart

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.cart.DeleteCartItemUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object CartUseCaseModule {
    @Provides
    fun provideDeleteCartItemUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        transactionRepository: TransactionRepository
    ): DeleteCartItemUseCase = DeleteCartItemUseCase(
        dispatcher,
        transactionRepository
    )
}