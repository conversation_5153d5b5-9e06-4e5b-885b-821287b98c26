package com.swiftsku.swiftpos.di

import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.swiftsku.swiftpos.data.couchbase.analytics.EventLogger
import com.swiftsku.swiftpos.data.couchbase.analytics.FirebaseEventLogger
import com.swiftsku.swiftpos.di.qualifiers.FirebaseEventLoggerQualifier
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AnalyticsModule {
    @Provides
    @Singleton
    fun provideFirebaseAnalytics(): FirebaseAnalytics = Firebase.analytics

    @Provides
    @Singleton
    @FirebaseEventLoggerQualifier
    fun provideFirebaseEventLogger(
        analytics: FirebaseAnalytics
    ): EventLogger = FirebaseEventLogger(analytics)
}