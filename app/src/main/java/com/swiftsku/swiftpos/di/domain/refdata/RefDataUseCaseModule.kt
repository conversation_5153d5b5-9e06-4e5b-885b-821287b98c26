package com.swiftsku.swiftpos.di.domain.refdata

import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.domain.refdata.RefDataUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent


@Module
@InstallIn(SingletonComponent::class)
object RefDataUseCaseModule {

    @Provides
    fun provideRefDataUseCase(refDataRepository: RefDataRepository) =
        RefDataUseCase(refDataRepository)
}