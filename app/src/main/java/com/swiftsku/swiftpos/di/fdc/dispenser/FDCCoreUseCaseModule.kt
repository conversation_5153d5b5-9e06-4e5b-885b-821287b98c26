package com.swiftsku.swiftpos.di.fdc.dispenser

import com.swiftsku.fdc.core.di.services.FDCCoreService
import com.swiftsku.fdc.core.di.usecases.core.FQMMessageListenerUseCase
import com.swiftsku.fdc.core.di.usecases.core.GetVersionInfoUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCExceptionUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.LogInUseCase
import com.swiftsku.fdc.core.di.usecases.core.LogOffUseCase
import com.swiftsku.fdc.core.di.usecases.core.SendDeviceAlarmUseCase
import com.swiftsku.fdc.core.di.usecases.core.SendPOSReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.StartFDCUseCase
import com.swiftsku.fdc.core.di.usecases.core.StopFDCUseCase
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FDCCoreUseCaseModule {

    @Provides
    @Singleton
    fun providesFDCExceptionListener(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService
    ): ListenForFDCExceptionUseCase =
        ListenForFDCExceptionUseCase(fdcCoreService, scope)

    @Provides
    @Singleton
    fun providesSendPOSReadyUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        fdcCoreService: FDCCoreService
    ): SendPOSReadyUseCase =
        SendPOSReadyUseCase(fdcCoreService, dispatcher)

    @Provides
    @Singleton
    fun providesVersionInfo(
        fdcDispenserService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetVersionInfoUseCase = GetVersionInfoUseCase(fdcDispenserService, dispatcher)


    @Provides
    @Singleton
    fun providesFDCReadyListener(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService
    ): ListenForFDCReadyUseCase =
        ListenForFDCReadyUseCase(fdcCoreService, scope)

    @Provides
    @Singleton
    fun providesLogin(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): LogInUseCase =
        LogInUseCase(fdcCoreService, dispatcher, scope)

    @Provides
    @Singleton
    fun providesLogOff(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): LogOffUseCase =
        LogOffUseCase(fdcCoreService, dispatcher, scope)

    @Provides
    @Singleton
    fun providesSendDeviceAlarm(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): SendDeviceAlarmUseCase =
        SendDeviceAlarmUseCase(fdcCoreService, dispatcher, scope)

    @Provides
    @Singleton
    fun providesStartFDC(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): StartFDCUseCase = StartFDCUseCase(fdcCoreService, dispatcher, scope)

    @Provides
    @Singleton
    fun providesStopFDC(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): StopFDCUseCase = StopFDCUseCase(fdcCoreService, dispatcher, scope)

    @Provides
    @Singleton
    fun providesFQMMessageListenerUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcCoreService: FDCCoreService,
    ): FQMMessageListenerUseCase = FQMMessageListenerUseCase(fdcCoreService, scope)

}