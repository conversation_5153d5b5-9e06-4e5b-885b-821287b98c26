package com.swiftsku.swiftpos.di.fdc.dispenser

import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.fdc.core.di.services.FDCCoreService
import com.swiftsku.fdc.core.di.services.FDCDispenserService
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FDCServiceModule {

    @Provides
    @Singleton
    fun providesCoreService(
        webSocketManager: WebSocketManager
    ): FDCCoreService = webSocketManager.create(FDCCoreService::class.java)

    @Provides
    @Singleton
    fun providesDispenserService(
        webSocketManager: WebSocketManager
    ): FDCDispenserService = webSocketManager.create(FDCDispenserService::class.java)
}