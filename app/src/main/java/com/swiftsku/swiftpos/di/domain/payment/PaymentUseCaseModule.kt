package com.swiftsku.swiftpos.di.domain.payment

import com.swiftsku.swiftpos.data.couchbase.event.EventRepository
import com.swiftsku.swiftpos.data.couchbase.payment.CbPayRecordsRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object PaymentUseCaseModule {

    @Provides
    fun providePaymentUseCase(
        transactionRepository: TransactionRepository,
        paymentRepository: PaymentRepository,
        eventRepository: EventRepository,
        cbPayRecordsRepository: CbPayRecordsRepository,
        paxPaymentService: PaxPaymentService
    ): PaymentUseCase =
        PaymentUseCase(transactionRepository, paymentRepository, eventRepository, cbPayRecordsRepository, paxPaymentService)
}