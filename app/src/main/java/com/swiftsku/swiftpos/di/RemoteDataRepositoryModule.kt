package com.swiftsku.swiftpos.di

import com.apollographql.apollo3.ApolloClient
import com.couchbase.lite.Collection
import com.swiftsku.swiftpos.data.couchbase.Couchbase
import com.swiftsku.swiftpos.data.couchbase.config.CouchbaseStoreRepository
import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.data.couchbase.credit.CreditRepository
import com.swiftsku.swiftpos.data.couchbase.event.CouchbaseEventRepository
import com.swiftsku.swiftpos.data.couchbase.event.EventRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.CouchbaseGUPCRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.GUPCRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.correction.CouchbaseGUPCCorrectionRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.correction.GUPCCorrectionRepository
import com.swiftsku.swiftpos.data.couchbase.payment.CbPayRecordsRepository
import com.swiftsku.swiftpos.data.couchbase.pricebook.CouchbasePriceBookRepository
import com.swiftsku.swiftpos.data.couchbase.pricebook.PriceBookRepository
import com.swiftsku.swiftpos.data.couchbase.promotion.CouchbasePromotionsRepository
import com.swiftsku.swiftpos.data.couchbase.promotion.PromotionsRepository
import com.swiftsku.swiftpos.data.couchbase.refdata.CouchbaseRefDataRepository
import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.data.couchbase.report.CouchbaseReportRepository
import com.swiftsku.swiftpos.data.couchbase.report.ReportRepository
import com.swiftsku.swiftpos.data.couchbase.support.SupportRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.CouchbaseTransactionRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.couchbase.user.CouchbaseUserRepository
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.data.remote.repositories.app.AppRepository
import com.swiftsku.swiftpos.data.remote.repositories.config.ApolloStoreConfigRepository
import com.swiftsku.swiftpos.data.remote.repositories.loyalty.ApolloLoyaltyRepository
import com.swiftsku.swiftpos.data.remote.repositories.loyalty.LoyaltyRepository
import com.swiftsku.swiftpos.data.remote.repositories.payment.ApolloPaymentRepository
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.data.remote.repositories.report.GraphQLReportRepository
import com.swiftsku.swiftpos.di.qualifiers.AccountLedgersCollection
import com.swiftsku.swiftpos.di.qualifiers.CreditAccountsCollection
import com.swiftsku.swiftpos.di.qualifiers.EventCollection
import com.swiftsku.swiftpos.di.qualifiers.GUPCCollection
import com.swiftsku.swiftpos.di.qualifiers.GUPCReportCollection
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LoyaltyApolloClient
import com.swiftsku.swiftpos.di.qualifiers.PaymentApolloClient
import com.swiftsku.swiftpos.di.qualifiers.PaymentRecordsCollection
import com.swiftsku.swiftpos.di.qualifiers.PriceBookCollection
import com.swiftsku.swiftpos.di.qualifiers.PromotionsCollection
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseReportRepository
import com.swiftsku.swiftpos.di.qualifiers.RefDataCollection
import com.swiftsku.swiftpos.di.qualifiers.RemoteConfigRepository
import com.swiftsku.swiftpos.di.qualifiers.RemoteReportRepository
import com.swiftsku.swiftpos.di.qualifiers.RemoteUserRepository
import com.swiftsku.swiftpos.di.qualifiers.ReportApolloClient
import com.swiftsku.swiftpos.di.qualifiers.ReportCollection
import com.swiftsku.swiftpos.di.qualifiers.StoreCollection
import com.swiftsku.swiftpos.di.qualifiers.SupportDocsCollection
import com.swiftsku.swiftpos.di.qualifiers.TransactionCollection
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.serialization.json.Json
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RemoteDataRepositoryModule {
    @Singleton
    @Provides
    fun providePriceBookRepository(
        @PriceBookCollection collection: Collection,
        @IODispatcher coroutineDispatcher: CoroutineDispatcher,
        json: Json
    ): PriceBookRepository = CouchbasePriceBookRepository(collection, coroutineDispatcher, json)

    @Singleton
    @Provides
    fun provideTransactionRepository(
        @TransactionCollection collection: Collection,
        @IODispatcher coroutineDispatcher: CoroutineDispatcher,
        json: Json,
        creditRepository: CreditRepository
    ): TransactionRepository = CouchbaseTransactionRepository(collection, coroutineDispatcher, json, creditRepository)

    @Singleton
    @Provides
    fun provideGUPCRepository(
        @GUPCCollection collection: Collection,
    ): GUPCRepository = CouchbaseGUPCRepository(collection)

    @Singleton
    @Provides
    fun provideRefDataRepository(
        @RefDataCollection collection: Collection,
        @IODispatcher coroutineDispatcher: CoroutineDispatcher,
        json: Json
    ): RefDataRepository = CouchbaseRefDataRepository(collection, coroutineDispatcher, json)


    @Singleton
    @Provides
    fun providePromotionRepository(
        @PromotionsCollection collection: Collection,
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): PromotionsRepository = CouchbasePromotionsRepository(collection, dispatcher, json)

    @Singleton
    @Provides
    fun providePaymentRepository(
        @PaymentApolloClient apolloClient: ApolloClient,
        @IODispatcher dispatcher: CoroutineDispatcher,
        paxPaymentService: PaxPaymentService
    ): PaymentRepository = ApolloPaymentRepository(apolloClient, dispatcher, paxPaymentService)

    @Singleton
    @Provides
    fun provideLoyaltyRepository(
        @LoyaltyApolloClient apolloClient: ApolloClient,
        @IODispatcher dispatcher: CoroutineDispatcher,
        json: Json
    ): LoyaltyRepository = ApolloLoyaltyRepository(apolloClient, dispatcher, json)

    @Singleton
    @Provides
    @QualifierCouchbaseConfigRepository
    fun provideStoreRepository(
        @StoreCollection collection: Collection,
        @IODispatcher dispatcher: CoroutineDispatcher,
        json: Json
    ): StoreRepository =
        CouchbaseStoreRepository(collection, coroutineDispatcher = dispatcher, json = json)

    @Singleton
    @Provides
    @RemoteConfigRepository
    fun provideApolloStoreRepository(
        @PaymentApolloClient apolloClient: ApolloClient,
        @IODispatcher dispatcher: CoroutineDispatcher,
    ): StoreRepository =
        ApolloStoreConfigRepository(
            coroutineDispatcher = dispatcher,
            apolloClient = apolloClient
        )

    @Singleton
    @Provides
    @RemoteUserRepository
    fun provideUserRepository(
        @StoreCollection collection: Collection,
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): UserRepository = CouchbaseUserRepository(
        collection = collection,
        jsonEncoder = json,
        dispatcher = dispatcher
    )

    @Singleton
    @Provides
    fun provideEventRepository(
        @EventCollection collection: Collection,
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): EventRepository = CouchbaseEventRepository(
        collection = collection,
        jsonEncoder = json,
        dispatcher = dispatcher
    )

    @Singleton
    @Provides
    @QualifierCouchbaseReportRepository
    fun provideReportRepository(
        @ReportCollection collection: Collection,
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ReportRepository = CouchbaseReportRepository(
        collection = collection,
        coroutineDispatcher = dispatcher,
        json = json,
    )

    @Singleton
    @Provides
    @RemoteReportRepository
    fun provideGraphQLReportRepository(
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher,
        @ReportApolloClient apolloClient: ApolloClient
    ): ReportRepository = GraphQLReportRepository(
        dispatcher = dispatcher,
        json = json,
        apolloClient = apolloClient
    )

    @Singleton
    @Provides
    fun provideAppV3Repository(
        @IODispatcher dispatcher: CoroutineDispatcher,
        @ReportApolloClient apolloClient: ApolloClient
    ): AppRepository = AppRepository(
        dispatcher = dispatcher,
        apolloClient = apolloClient
    )

    @Singleton
    @Provides
    fun provideGUPCReportRepository(
        @GUPCReportCollection collection: Collection,
        json: Json,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GUPCCorrectionRepository = CouchbaseGUPCCorrectionRepository(
        collection = collection,
        dispatcher = dispatcher,
        json = json,
    )

    @Singleton
    @Provides
    fun provideCbPayRecordsRepository(
        @PaymentRecordsCollection collection: Collection,
        @IODispatcher dispatcher: CoroutineDispatcher,
        json: Json
    ) = CbPayRecordsRepository(collection, dispatcher, json)

    @Singleton
    @Provides
    fun provideCreditRepository(
        couchbase: Couchbase,
        @CreditAccountsCollection accountCollection: Collection,
        @AccountLedgersCollection ledgerCollection: Collection,
        @TransactionCollection txnCollection: Collection,
        @IODispatcher dispatcher: CoroutineDispatcher,
        json: Json
    ) = CreditRepository(
        couchbase,
        accountCollection,
        ledgerCollection,
        txnCollection,
        dispatcher,
        json
    )

    @Singleton
    @Provides
    fun provideSupportRepository(
        @SupportDocsCollection collection: Collection,
        @IODispatcher dispatcher: CoroutineDispatcher,
        json: Json
    ): SupportRepository = SupportRepository(collection, dispatcher, json)
}

