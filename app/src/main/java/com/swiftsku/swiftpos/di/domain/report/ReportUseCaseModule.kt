package com.swiftsku.swiftpos.di.domain.report

import com.swiftsku.swiftpos.data.couchbase.report.ReportRepository
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseReportRepository
import com.swiftsku.swiftpos.di.qualifiers.RemoteReportRepository
import com.swiftsku.swiftpos.domain.report.GetAdHocReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEODReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEOMReportUseCase
import com.swiftsku.swiftpos.domain.report.GetEOYReportUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalBatchReportListUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalBatchReportUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalShiftReportListUseCase
import com.swiftsku.swiftpos.domain.report.GetHistoricalShiftReportUseCase
import com.swiftsku.swiftpos.domain.report.GetPresetReportUseCase
import com.swiftsku.swiftpos.domain.report.GetShiftReportUseCase
import com.swiftsku.swiftpos.domain.report.InitCbSyncUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object ReportUseCaseModule {
    @Provides
    @Singleton
    fun provideGetEODReportUseCase(
        @QualifierCouchbaseReportRepository reportRepository: ReportRepository
    ): GetEODReportUseCase {
        return GetEODReportUseCase(reportRepository::getEODReport)
    }

    @Provides
    @Singleton
    fun provideGetEOMReportUseCase(
        @QualifierCouchbaseReportRepository reportRepository: ReportRepository
    ): GetEOMReportUseCase {
        return GetEOMReportUseCase(reportRepository::getEOMReport)
    }

    @Provides
    @Singleton
    fun provideGetEOYReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetEOYReportUseCase {
        return GetEOYReportUseCase(reportRepository::getEOYReport)
    }

    @Provides
    @Singleton
    fun provideGetAdHocReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetAdHocReportUseCase {
        return GetAdHocReportUseCase(reportRepository::getAdHocReport)
    }

    @Provides
    @Singleton
    fun provideGetShiftReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetShiftReportUseCase {
        return GetShiftReportUseCase(reportRepository::getShiftReport)
    }

    @Provides
    @Singleton
    fun provideInitCbSyncUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): InitCbSyncUseCase {
        return InitCbSyncUseCase(reportRepository::initCbSync)
    }

    @Provides
    @Singleton
    fun providePresetReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetPresetReportUseCase {
        return GetPresetReportUseCase(reportRepository::getPresetReport)
    }

    @Provides
    @Singleton
    fun provideGetHistoricalShiftReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetHistoricalShiftReportUseCase {
        return GetHistoricalShiftReportUseCase(reportRepository::getHistoricalShiftReport)
    }

    @Provides
    @Singleton
    fun provideGetHistoricalShiftReportListUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetHistoricalShiftReportListUseCase {
        return GetHistoricalShiftReportListUseCase(reportRepository::getHistoricalShiftReportList)
    }

    @Provides
    @Singleton
    fun provideGetHistoricalBatchReportUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetHistoricalBatchReportUseCase {
        return GetHistoricalBatchReportUseCase(reportRepository::getHistoricalBatchReport)
    }

    @Provides
    @Singleton
    fun provideGetHistoricalBatchReportListUseCase(
        @RemoteReportRepository reportRepository: ReportRepository
    ): GetHistoricalBatchReportListUseCase {
        return GetHistoricalBatchReportListUseCase(reportRepository::getHistoricalBatchReportList)
    }
}