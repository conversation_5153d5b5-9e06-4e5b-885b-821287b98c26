package com.swiftsku.swiftpos.di

import com.swiftsku.swiftpos.data.provider.DashboardStateProvider
import com.swiftsku.swiftpos.domain.departments.UpdateDepartmentTxnItemsUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object StateModule {
    @Singleton
    @Provides
    fun provideDashboardStateProvider(
        updateDepartmentTxnItemsUseCase: UpdateDepartmentTxnItemsUseCase
    ): DashboardStateProvider = DashboardStateProvider(updateDepartmentTxnItemsUseCase)
}