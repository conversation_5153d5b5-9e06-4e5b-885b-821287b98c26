package com.swiftsku.swiftpos.di.fdc.dispenser

import android.content.Context
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.swiftsku.fdc.core.di.client.DefaultWebSocketFlowListener
import com.swiftsku.fdc.core.di.client.WebSocketFlowListener
import com.swiftsku.fdc.core.di.client.WebSocketOkHttp
import com.swiftsku.fdc.core.di.client.WebSocketOkHttp.Builder
import com.swiftsku.fdc.core.di.client.event.DefaultWebSocketEventLoop
import com.swiftsku.fdc.core.di.client.event.WebSocketEventLoop
import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.swiftpos.di.fdc.dispenser.qualifier.SocketOkHttpQualifier
import com.swiftsku.swiftpos.di.fdc.dispenser.qualifier.SocketRequestQualifier
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.fdc.dispenser.converter.JacksonConverterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import okhttp3.OkHttpClient
import okhttp3.Request
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object SocketModule {
    @Provides
    @Singleton
    @SocketRequestQualifier
    fun provideWebSocketRequest(): Request = Request.Builder()
        .url("ws://app-mirror:36101/fqm/fdc")
        .build()

    @Provides
    @Singleton
    fun provideObjectMapper(): ObjectMapper =
        ObjectMapper()
            .configure(SerializationFeature.INDENT_OUTPUT, true)


    @Singleton
    @Provides
    @SocketOkHttpQualifier
    fun provideOkHttpClient(): OkHttpClient = OkHttpClient.Builder().build()

    @Singleton
    @Provides
    fun provideWebSocketClient(
        @SocketOkHttpQualifier okHttpClient: OkHttpClient,
        @SocketRequestQualifier request: Request,
    ): WebSocketOkHttp =
        Builder(
            webSocketRequest = request,
            okHttpClient = okHttpClient,
        )
            .build()

    @Provides
    @Singleton
    fun provideWebsocketEventLoop(
    ): WebSocketEventLoop =
        DefaultWebSocketEventLoop(eventScope = CoroutineScope(SupervisorJob() + Dispatchers.Default))

    @Provides
    @Singleton
    fun provideWebSocketFlowListener(): WebSocketFlowListener =
        DefaultWebSocketFlowListener(CoroutineScope(SupervisorJob() + Dispatchers.Default))

    @Provides
    @Singleton
    fun provideWebSocketManager(
        @IOCoroutineScope scope: CoroutineScope,
        @ApplicationContext context: Context,
        eventLoop: WebSocketEventLoop,
        client: WebSocketOkHttp,
        listener: WebSocketFlowListener,
        objectMapper: ObjectMapper
    ): WebSocketManager = WebSocketManager(
        ioScope = scope,
        webSocketEventLoop = eventLoop,
        webSocketClient = client,
        webSocketLister = listener,
        converterFactory = JacksonConverterFactory.create(objectMapper),
        context = context
    )


}