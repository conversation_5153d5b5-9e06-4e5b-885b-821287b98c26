package com.swiftsku.swiftpos.di

import com.swiftsku.swiftpos.modules.hotp.HOTPGenerator
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.crypto.Mac
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object HOtpModule {
    @Provides
    @Singleton
    fun provideHTOPGenerator(): HOTPGenerator {
        val algorithm = "HmacSHA1"
        return HOTPGenerator(
            algorithm = algorithm,
            mac = Mac.getInstance(algorithm)
        )
    }


}