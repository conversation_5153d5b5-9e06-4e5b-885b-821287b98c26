package com.swiftsku.swiftpos.di.qualifiers

import javax.inject.Qualifier

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PriceBookCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GUPCCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RefDataCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class TransactionCollection


@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PromotionsCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class StoreCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class EventCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ReportCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GUPCReportCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PaymentRecordsCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CreditAccountsCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AccountLedgersCollection

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SupportDocsCollection
