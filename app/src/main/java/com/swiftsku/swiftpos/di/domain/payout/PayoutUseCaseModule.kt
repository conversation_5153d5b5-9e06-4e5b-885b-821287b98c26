package com.swiftsku.swiftpos.di.domain.payout

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payout.CreateLotteryPayoutUseCase
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object PayoutUseCaseModule {

    @Provides
    fun provideCreateLotteryPayoutUseCase(
        transactionRepository: TransactionRepository,
        printTransactionUseCase: PrintTransactionUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): CreateLotteryPayoutUseCase =
        CreateLotteryPayoutUseCase(dispatcher, printTransactionUseCase, transactionRepository)
}