package com.swiftsku.swiftpos.di.domain.pricebook

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.pricebook.EBTCalculationsUseCase
import com.swiftsku.swiftpos.domain.pricebook.UpdatePriceBookTxnItemsUseCase
import com.swiftsku.swiftpos.domain.promotion.GetPromotionUserCase
import com.swiftsku.swiftpos.domain.tax.GetTaxForAmountMapUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object PriceBookModule {

    @Provides
    fun provideUpdatePriceBookTxnItemsUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        getPromotionUserCase: GetPromotionUserCase,
        getTaxForAmountMapUseCase: GetTaxForAmountMapUseCase,
        ebtCalculationsUseCase: EBTCalculationsUseCase
    ) = UpdatePriceBookTxnItemsUseCase(dispatcher, getTaxForAmountMapUseCase, getPromotionUserCase, ebtCalculationsUseCase)
}