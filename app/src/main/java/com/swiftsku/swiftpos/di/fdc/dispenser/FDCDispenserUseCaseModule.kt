package com.swiftsku.swiftpos.di.fdc.dispenser

import com.swiftsku.fdc.core.di.services.FDCDispenserService
import com.swiftsku.fdc.core.di.usecases.dispenser.AuthorizeFuelPointRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ChangeDSPLimitRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ChangeFPFuelModeRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ChangeFuelPriceRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ClearFuelSaleRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.CloseFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ConsentAuthorizeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetAvailableFuelSaleTransactionsUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetCurrentFuelingStatusUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetDSPConfigurationUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetFPStateRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetFuelTransactionDetailsRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetModeTableUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetProductTableUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetTotalsRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFPCurrentFuelingStatusMessageUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFPStateChangeMessageUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFuelSaleTransactionsMessageUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.LockFuelSaleRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.OpenFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ReserveFuelPointRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ResumeFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.SuspendFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.TerminateFPRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.UnclearFuelSaleRequestUseCase
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FDCDispenserUseCaseModule {

    @Provides
    @Singleton
    fun providesAuthorizeFP(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): AuthorizeFuelPointRequestUseCase =
        AuthorizeFuelPointRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesChangeDSPLimit(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ChangeDSPLimitRequestUseCase =
        ChangeDSPLimitRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesChangeFPFuelMode(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ChangeFPFuelModeRequestUseCase =
        ChangeFPFuelModeRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesChangeFuelPrice(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ChangeFuelPriceRequestUseCase =
        ChangeFuelPriceRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesClearFuelSale(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ClearFuelSaleRequestUseCase =
        ClearFuelSaleRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesConsentAuthorizeFP(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ConsentAuthorizeFuelPointUseCase =
        ConsentAuthorizeFuelPointUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesFreeFP(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): FreeFuelPointUseCase =
        FreeFuelPointUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesGetAvailableFuelSaleTransactions(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetAvailableFuelSaleTransactionsUseCase =
        GetAvailableFuelSaleTransactionsUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesGetCurrentFuelingStatus(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetCurrentFuelingStatusUseCase =
        GetCurrentFuelingStatusUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetDSPConfiguration(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetDSPConfigurationUseCase =
        GetDSPConfigurationUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetFPState(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetFPStateRequestUseCase =
        GetFPStateRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetFuelTransactionDetails(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetFuelTransactionDetailsRequestUseCase =
        GetFuelTransactionDetailsRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetModeTableUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetModeTableUseCase =
        GetModeTableUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetProductTableUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetProductTableUseCase =
        GetProductTableUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesGetTotalsRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): GetTotalsRequestUseCase =
        GetTotalsRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesListenForFPCurrentFuelingStatusMessageUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
    ): ListenForFPCurrentFuelingStatusMessageUseCase =
        ListenForFPCurrentFuelingStatusMessageUseCase(fdcDispenserService, scope)

    @Provides
    @Singleton
    fun providesListenForFPStateChangeMessageUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
    ): ListenForFPStateChangeMessageUseCase =
        ListenForFPStateChangeMessageUseCase(fdcDispenserService, scope)

    @Provides
    @Singleton
    fun providesListenForFuelSaleTransactionsMessageUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
    ): ListenForFuelSaleTransactionsMessageUseCase =
        ListenForFuelSaleTransactionsMessageUseCase(fdcDispenserService, scope)


    @Provides
    @Singleton
    fun providesLockFuelSaleRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): LockFuelSaleRequestUseCase =
        LockFuelSaleRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesReserveFuelPointRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ReserveFuelPointRequestUseCase =
        ReserveFuelPointRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesResumeFPRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): ResumeFPRequestUseCase =
        ResumeFPRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesSuspendFPRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): SuspendFPRequestUseCase =
        SuspendFPRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesTerminateFPRequestUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): TerminateFPRequestUseCase =
        TerminateFPRequestUseCase(fdcDispenserService, scope, dispatcher)


    @Provides
    @Singleton
    fun providesUnclearFSUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): UnclearFuelSaleRequestUseCase =
        UnclearFuelSaleRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesCloseFSUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): CloseFPRequestUseCase =
        CloseFPRequestUseCase(fdcDispenserService, scope, dispatcher)

    @Provides
    @Singleton
    fun providesOpenFSUseCase(
        @IOCoroutineScope scope: CoroutineScope,
        fdcDispenserService: FDCDispenserService,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): OpenFPRequestUseCase =
        OpenFPRequestUseCase(fdcDispenserService, scope, dispatcher)
}