package com.swiftsku.swiftpos.di.domain.support

import com.swiftsku.swiftpos.data.couchbase.support.SupportRepository
import com.swiftsku.swiftpos.domain.support.SupportUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SupportUseCaseModule {

    @Provides
    @Singleton
    fun provideSupportUseCase(
        supportRepository: SupportRepository
    ): SupportUseCase = SupportUseCase(supportRepository)
}