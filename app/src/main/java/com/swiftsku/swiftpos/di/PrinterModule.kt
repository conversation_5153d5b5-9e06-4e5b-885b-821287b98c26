package com.swiftsku.swiftpos.di

import android.content.Context
import com.sunmi.peripheral.printer.InnerPrinterManager
import com.sunmi.printerx.PrinterSdk
import com.swiftsku.swiftpos.di.qualifiers.LegacyPrinter
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.modules.printer.LegacyPrinterManager
import com.swiftsku.swiftpos.modules.printer.PrinterXManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object PrinterModule {

    @Singleton
    @Provides
    @LegacyPrinter
    fun provideLegacyPrinterManager(
        @ApplicationContext context: Context
    ): ISunMiPrinter = LegacyPrinterManager(context, manager = InnerPrinterManager.getInstance())

    @Singleton
    @Provides
    @PrinterX
    fun providePrinterXManager(
        @ApplicationContext context: Context
    ): ISunMiPrinter = PrinterXManager(context, manager = PrinterSdk.getInstance())
}