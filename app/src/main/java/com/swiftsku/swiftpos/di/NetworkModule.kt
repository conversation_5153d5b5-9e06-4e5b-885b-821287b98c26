package com.swiftsku.swiftpos.di

import android.content.Context
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.interceptor.ApolloInterceptor
import com.apollographql.apollo3.network.http.DefaultHttpEngine
import com.apollographql.apollo3.network.http.HttpInterceptor
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LoyaltyApolloClient
import com.swiftsku.swiftpos.di.qualifiers.PaymentApolloClient
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import com.swiftsku.swiftpos.di.qualifiers.QualifierLoggingApolloInterceptor
import com.swiftsku.swiftpos.di.qualifiers.ReportApolloClient
import com.swiftsku.swiftpos.domain.config.GetStoreConfigUseCase
import com.swiftsku.swiftpos.services.network.AndroidNetworkConnectivityService
import com.swiftsku.swiftpos.services.network.NetworkConnectivityService
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.services.vpn.SwiftScaleVPNService
import com.swiftsku.swiftpos.services.vpn.VPNService
import com.swiftsku.swiftpos.utils.LoggingApolloInterceptor
import com.swiftsku.swiftpos.utils.PaymentAuthInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Singleton
    @Provides
    fun provideAuthInterceptor(getStoreConfigUseCase: GetStoreConfigUseCase): HttpInterceptor =
        PaymentAuthInterceptor(getStoreConfigUseCase)

    @Singleton
    @Provides
    fun provideNetworkConnectivityService(
        @ApplicationContext context: Context,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): NetworkConnectivityService =
        AndroidNetworkConnectivityService(context, dispatcher)

    @Singleton
    @Provides
    fun provideVPNService(
        @ApplicationContext context: Context,
    ): VPNService =
        SwiftScaleVPNService(context)


    @Singleton
    @PaymentApolloClient
    @Provides
    fun providePaymentApolloClient(
        authInterceptor: HttpInterceptor,
        @QualifierLoggingApolloInterceptor loggingApolloInterceptor: ApolloInterceptor

    ): ApolloClient {
        val builder = ApolloClient
            .Builder()
            .httpEngine(DefaultHttpEngine(110_000, readTimeout = 110_000))
            .serverUrl(BuildConfig.PAYMENT_URL)
            .addHttpInterceptor(authInterceptor)
        if (BuildConfig.DEBUG) {
            builder.addInterceptor(loggingApolloInterceptor)
        }
        return builder.build()
    }

    @Provides
    @Singleton
    @QualifierLoggingApolloInterceptor
    fun provideLoggingApolloInterceptor(): ApolloInterceptor =
        LoggingApolloInterceptor()


    @Singleton
    @LoyaltyApolloClient
    @Provides
    fun provideLoyaltyApolloClient(
        @QualifierLoggingApolloInterceptor loggingApolloInterceptor: ApolloInterceptor
    ): ApolloClient {
        val builder = ApolloClient.Builder()
            .serverUrl(BuildConfig.LOYALTY_URL)
        if (BuildConfig.DEBUG) {
            builder.addInterceptor(loggingApolloInterceptor)
        }
        return builder.build()
    }

    @Singleton
    @ReportApolloClient
    @Provides
    fun provideReportApolloClient(
        @QualifierLoggingApolloInterceptor loggingApolloInterceptor: ApolloInterceptor
    ): ApolloClient {
        val builder = ApolloClient.Builder()
            .serverUrl(BuildConfig.REPORT_URL)
        if (BuildConfig.DEBUG) {
            builder.addInterceptor(loggingApolloInterceptor)
        }
        return builder.build()
    }

    @Singleton
    @Provides
    fun providePaxPaymentService(
        @ApplicationContext context: Context,
        @IODispatcher dispatcher: CoroutineDispatcher,
        @QualifierCouchbaseConfigRepository storeRepository: StoreRepository
    ) = PaxPaymentService(context, dispatcher, storeRepository)
}