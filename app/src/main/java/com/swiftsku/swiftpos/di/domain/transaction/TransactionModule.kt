package com.swiftsku.swiftpos.di.domain.transaction

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.dispenser.PendingFuelTxnUseCase
import com.swiftsku.swiftpos.domain.dispenser.UpdateFuelSaleTransactionStatusUseCase
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payout.CreateLotteryPayoutUseCase
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.domain.transaction.CompleteTransactionUseCase
import com.swiftsku.swiftpos.domain.transaction.GetTransactionSummaryUseCase
import com.swiftsku.swiftpos.domain.transaction.UpdateTransactionCollectedAmountUseCase
import com.swiftsku.swiftpos.domain.transaction.PaymentRefundUseCase
import com.swiftsku.swiftpos.modules.hotp.HOTPGenerator
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
object TransactionModule {

    @Provides
    fun provideTransactionSummaryUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher
    ) = GetTransactionSummaryUseCase(dispatcher)

    @Provides
    fun providePaymentRefundUseCase(
        paymentRepository: PaymentRepository,
        hOtpGenerator: HOTPGenerator
    ) = PaymentRefundUseCase(paymentRepository, hOtpGenerator)

    @Provides
    fun provideCompleteTransactionUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        transactionRepository: TransactionRepository,
        createLotteryPayoutUseCase: CreateLotteryPayoutUseCase,
        printTransactionUseCase: PrintTransactionUseCase,
    ) = CompleteTransactionUseCase(
        dispatcher = dispatcher,
        transactionRepository = transactionRepository,
        createLotteryPayout = createLotteryPayoutUseCase,
        printTransaction = printTransactionUseCase,
    )

    @Provides
    fun provideUpdateTransactionCollectedAmountUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
    ): UpdateTransactionCollectedAmountUseCase = UpdateTransactionCollectedAmountUseCase(dispatcher)

    @Provides
    fun providesPendingFuelTxnUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        transactionRepository: TransactionRepository,
        paxPaymentService: PaxPaymentService,
        paymentUseCase: PaymentUseCase
    ): PendingFuelTxnUseCase =
        PendingFuelTxnUseCase(dispatcher, transactionRepository, paxPaymentService, paymentUseCase)
}