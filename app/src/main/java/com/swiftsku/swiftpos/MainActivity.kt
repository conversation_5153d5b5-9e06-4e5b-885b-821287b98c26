package com.swiftsku.swiftpos

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.swiftpos.lifecycle.observer.printer.PrinterObserver
import com.swiftsku.swiftpos.ui.activity.base.ActivityWithSecondaryDisplay
import com.swiftsku.swiftpos.ui.activity.base.AppRoot
import com.swiftsku.swiftpos.ui.activity.base.secondaryDisplay
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.copyAssetToExternalFile
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject


@AndroidEntryPoint
class MainActivity : ActivityWithSecondaryDisplay() {

    @Inject
    lateinit var printerObserver: PrinterObserver

    @Inject
    lateinit var socketManager: WebSocketManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        printerObserver.registerLifecycle(lifecycle = lifecycle)
        socketManager.registerLifeCycle(lifecycle = lifecycle)

        setContent {
            val context = LocalContext.current

            val idleImageResources by produceState<File?>(initialValue = null) {
                withContext(Dispatchers.IO) {
                    value = runCatching {
                        copyAssetToExternalFile(context, "mt30_ad1.zip")
                    }.onFailure { EventUtils.recordException(it) }.getOrNull()
                }
            }

            AppRoot(
                configVM = hiltViewModel(),
                dashboardVM = hiltViewModel(),
                loginVM = hiltViewModel(),
                fdcVM = hiltViewModel(),
                secondaryDisplay = secondaryDisplay,
                fdcSocketManager = socketManager,
                recallVM = hiltViewModel(),
                transactionHistoryVM = hiltViewModel(),
                transactionHistoryDetailVM = hiltViewModel(),
                reportVM = hiltViewModel(),
                creditsVM = hiltViewModel(),
                idleImageResources = idleImageResources
            )
        }
    }
}



