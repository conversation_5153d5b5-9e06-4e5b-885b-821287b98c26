package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.extension.formattedAmount

val txnSummarySvgTemplateString =
    """<svg width="576" height="{{height}}" viewBox="0 0 576 {{height}}" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="576" height="100%" fill="white"/>
            <rect x="40%" y="3" width="55%" height="1" fill="black"/>
            {{#each items}}
            <g transform="translate(0 10)">
                <text fill="black" font-family="sans-serif" font-size="22" font-weight="{{fontWeight}}" letter-spacing="0em" text-anchor="end">
                    <tspan x="60%" y="{{y}}">{{key}}</tspan>
                </text>
                <text fill="black" font-family="sans-serif" font-size="22" font-weight="{{fontWeight}}" letter-spacing="0em">
                    <tspan x="69%" y="{{y}}" text-anchor="end">{{quantity}}</tspan>
                </text>
                <text fill="black" font-family="sans-serif" font-size="22" font-weight="{{fontWeight}}" letter-spacing="0em" text-anchor="end">
                    <tspan x="95%" y="{{y}}">{{value}}</tspan>
                </text>
            </g>
            {{/each}}
            <rect width="516" height="1" transform="translate(30 {{endLineY}})" fill="black"/>
        </svg>""".trimIndent()

data class TransactionSummaryTemplate(
    val subTotal: Float,
    val tax: Float,
    val coupon: Float,
    val promotions: Float,
    val total: Float,
    val ebt: Float,
    val cash: Float,
    val card: Float,
    val changeGiven: Float,
    val check: Float,
    val credit: Float,
    val totalQuantity: Int,
    val appliedFees: List<AppliedFee>? = null,
    val lotteryPayout: LotteryPayout?,
    val lotteryPayouts: List<LotteryPayout>?
)

data class TransactionSummaryItemTemplate(
    val key: String,
    val value: String,
    val quantity: String = "",
    val fontWeight: String = "500",
)

fun TransactionSummaryTemplate.list(): List<TransactionSummaryItemTemplate> {
    val list = mutableListOf<TransactionSummaryItemTemplate>()

    list.add(
        TransactionSummaryItemTemplate(
            key = "Subtotal",
            value = subTotal.formattedAmount(),
            quantity = totalQuantity.toString()
        )
    )
    list.add(TransactionSummaryItemTemplate("Tax", tax.formattedAmount()))
    if (coupon > 0) {
        list.add(TransactionSummaryItemTemplate("Coupon", "-${coupon.formattedAmount()}"))
    }

    if (promotions > 0) {
        list.add(
            TransactionSummaryItemTemplate(
                "Promotions",
                "-${promotions.formattedAmount()}"
            )
        )
    }
    list.add(
        TransactionSummaryItemTemplate(
            "Total",
            total.formattedAmount(),
            fontWeight = "700"
        )
    )
    lotteryPayout?.let {
        if (it.amount > 0) {
            list.add(
                TransactionSummaryItemTemplate(
                    "Lottery winning: ${it.info}",
                    "-${it.amount.formattedAmount()}"
                )
            )
        }
    }
    lotteryPayouts?.forEach {
        list.add(
            TransactionSummaryItemTemplate(
                "Lottery winning: ${it.info}",
                "-${it.amount.formattedAmount()}"
            )
        )
    }
    if (ebt > 0) {
        list.add(TransactionSummaryItemTemplate("EBT", ebt.formattedAmount()))
    }
    if (cash > 0) {
        list.add(TransactionSummaryItemTemplate("Cash", cash.formattedAmount()))
    }
    if (check > 0) {
        list.add(TransactionSummaryItemTemplate("Check", check.formattedAmount()))
    }
    if (credit > 0) {
        list.add(TransactionSummaryItemTemplate("Credit", credit.formattedAmount()))
    }
    if (card > 0) {
        var cardSum = card
        val cardFee = appliedFees?.firstOrNull { it.type == FeeType.CARD_PROCESSING }
        cardFee?.let {
            cardSum += it.amount
            list.add(TransactionSummaryItemTemplate("Card Fee(${it.info})", it.amount.formattedAmount()))
        }
        list.add(TransactionSummaryItemTemplate("Card", cardSum.formattedAmount()))
    }
    if (changeGiven > 0) {
        list.add(TransactionSummaryItemTemplate("Change", changeGiven.formattedAmount()))
    }

    return list
}

fun TransactionSummaryTemplate.map(): Map<String, Any> {

    var y = 25 // init y position
    var translateYOffset = 10 // init translate y offset
    val offset = 30 // offset between each item
    return mapOf(

        "items" to list().map { item ->
            val mapOf = mapOf(
                "key" to item.key,
                "quantity" to item.quantity,
                "value" to item.value,
                "fontWeight" to item.fontWeight,
                "translateYOffset" to translateYOffset,
                "y" to y,
            )
            y += offset
            translateYOffset += offset
            mapOf
        },
        "height" to (y),
        "endLineY" to (y - 5),
    )
}


fun TransactionSummaryTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val txnSummaryData = map()

    val txnSummaryTemplate = handlebars.compileInline(txnSummarySvgTemplateString)
    val txnSummarSvgWithFilledData = txnSummaryTemplate.apply(txnSummaryData)


    val txnSummarySvg = SVG.getFromString(txnSummarSvgWithFilledData)
    txnSummarySvg.renderDPI = 300f
    val txnSummaryPicture = txnSummarySvg.renderToPicture()
    val txnSummaryBitmap = Bitmap.createBitmap(
        txnSummaryPicture.width,
        txnSummaryPicture.height,
        Bitmap.Config.RGB_565
    )
    val txnSummaryCanvas = Canvas(txnSummaryBitmap)
    txnSummaryCanvas.drawPicture(txnSummaryPicture)

    return txnSummaryBitmap
}