package com.swiftsku.swiftpos.modules.websocket.payment

import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.modules.websocket.WebSocketOkHttp
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class PaymentProcessor @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val webSocketOkHttp: WebSocketOkHttp,
    private val json: Json
) : PaymentService {

    var socketState = webSocketOkHttp.socketState

    object Headers {
        const val TERMINAL_ID = "terminal_id"
        const val UID = "uid"
    }

    override suspend fun processPayment(
        input: PaymentInputDTO, terminalId: String, userName: String
    ): Flow<PaymentResponseWS> =
        callbackFlow {

            webSocketOkHttp.connect(object : WebSocketOkHttp.Callback {
                override fun readyToSend() {
                    webSocketOkHttp.send(json.encodeToString(input))
                }
            }, mapOf(Headers.TERMINAL_ID to terminalId, Headers.UID to userName))

            webSocketOkHttp.message.collectLatest {
                if (!isActive) return@collectLatest

                val response = json.decodeFromString<PaymentResponseWS>(it)
                trySend(response)
                if (response.completed == true) {
                    webSocketOkHttp.disconnect()
                    close()
                }
            }

            awaitClose {
                webSocketOkHttp.disconnect()
            }
        }

    override suspend fun cancelPayment() {
        withContext(dispatcher) {
            webSocketOkHttp.cancel()
        }
    }
}