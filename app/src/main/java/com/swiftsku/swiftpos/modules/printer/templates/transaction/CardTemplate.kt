package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars

val cardSvgTemplate = """
<svg width="576" height="300" viewBox="0 0 576 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="white"/>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em"><tspan x="35%" y="13%">{{label}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="30%">Merchant ID</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="40%">{{merchantId}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="30%">Approval Code</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="40%">{{approvalCode}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="55%">Logo</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="65%">{{logo}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="55%">Entry</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="65%">{{entry}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="80%">Account #</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="90%">{{account}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="80%">Transaction ID</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="90%">{{transactionId}}</tspan></text>
  <rect width="516" height="1" transform="translate(30 295)" fill="black"/>
</svg>
""".trimIndent();


data class CardTemplate(
    val label: String,
    val merchantId: String,
    val approvalCode: String,
    val terminalId: String,
    val logo: String,
    val transactionId: String,
    val account: String,
    val entry: String,
    val amount: Float,
)

fun CardTemplate.map() = mapOf(
    "label" to label,
    "merchantId" to merchantId,
    "approvalCode" to approvalCode,
    "terminalId" to terminalId,
    "logo" to logo,
    "transactionId" to transactionId,
    "entry" to entry,
    "account" to account,
    "amount" to amount,
)


fun CardTemplate.drawBitmap(handlebars: Handlebars): Bitmap {

    val data = map()

    val cardDetailsTemplate = handlebars.compileInline(cardSvgTemplate)
    val cardDetailsTemplateFilledData = cardDetailsTemplate.apply(data)
    val cardDetailsSvg = SVG.getFromString(cardDetailsTemplateFilledData)
    cardDetailsSvg.renderDPI = 300f
    val cardDetailsPicture = cardDetailsSvg.renderToPicture()
    val cardDetailsBitmap = Bitmap.createBitmap(
        cardDetailsPicture.width,
        cardDetailsPicture.height,
        Bitmap.Config.RGB_565
    )
    val cardDetailsCanvas = Canvas(cardDetailsBitmap)
    cardDetailsCanvas.drawPicture(cardDetailsPicture)

    return cardDetailsBitmap
}