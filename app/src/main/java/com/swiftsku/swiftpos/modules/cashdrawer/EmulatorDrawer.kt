package com.swiftsku.swiftpos.modules.cashdrawer

import com.sunmi.printerx.api.PrintResult;
import com.sunmi.printerx.api.CashDrawerApi
import com.sunmi.printerx.SdkException;

class EmulatorDrawer: CashDrawerApi {
    override fun open(callback: PrintResult) {
        callback.onResult(0, "Success")
    }

    @Throws(SdkException::class)
    override fun isOpen(): Boolean {
        return false
    }
}