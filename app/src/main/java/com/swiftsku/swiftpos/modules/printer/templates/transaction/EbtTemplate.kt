package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.extension.formattedAmount

val ebtSvgTemplate = """
<svg width="576" height="300" viewBox="0 0 576 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="white"/>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em"><tspan x="35%" y="13%">EBT Details</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="30%">{{ebtType}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="40%">{{ebtPaidAmount}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="30%">Ref</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="40%">{{refId}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="55%">Auth</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="65%">{{authCode}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="55%">Entry</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="65%">{{cardEntryType}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="80%">Card No</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="90%">{{cardLast4}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="80%">Balance</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="90%">{{ebtBalance}}</tspan></text>
  <rect width="516" height="1" transform="translate(30 295)" fill="black"/>
</svg>
""".trimIndent()

data class EbtTemplate(
    val ebtType: String,
    val ebtPaidAmount: Float,
    val ebtBalance: Float,
    val cardEntryType: String,
    val refId: String,
    val authCode: String,
    val cardLast4: String
)

fun EbtTemplate.map() = mapOf(
    "ebtType" to ebtType,
    "ebtPaidAmount" to ebtPaidAmount.formattedAmount(),
    "ebtBalance" to ebtBalance.formattedAmount(),
    "cardEntryType" to cardEntryType,
    "refId" to refId,
    "authCode" to authCode,
    "cardLast4" to cardLast4
)

fun EbtTemplate.drawBitmap(handlebars: Handlebars): Bitmap {

    val data = map()

    val cardDetailsTemplate = handlebars.compileInline(ebtSvgTemplate)
    val cardDetailsTemplateFilledData = cardDetailsTemplate.apply(data)
    val cardDetailsSvg = SVG.getFromString(cardDetailsTemplateFilledData)
    cardDetailsSvg.renderDPI = 300f
    val cardDetailsPicture = cardDetailsSvg.renderToPicture()
    val cardDetailsBitmap = Bitmap.createBitmap(
        cardDetailsPicture.width,
        cardDetailsPicture.height,
        Bitmap.Config.RGB_565
    )
    val cardDetailsCanvas = Canvas(cardDetailsBitmap)
    cardDetailsCanvas.drawPicture(cardDetailsPicture)

    return cardDetailsBitmap
}