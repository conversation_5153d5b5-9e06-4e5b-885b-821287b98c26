package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars

val loyaltyTemplateSVG = """
    <svg width="576" height="150" viewBox="0 0 576 150" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="white"/>
    <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em"><tspan x="35%" y="30%">Loyalty Program</tspan></text>
    <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="218" y="60%">Account ID</tspan></text>
    <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="218.446" y="80%">{{accountId}}</tspan></text>
    <rect width="516" height="1" transform="translate(30 145)" fill="black"/>
</svg>

""".trimIndent();


data class LoyaltyTemplate(
    val accountId : String,
)

fun LoyaltyTemplate.map() = mapOf(
    "accountId" to accountId,
)

fun LoyaltyTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val storeData = map()
    val template = handlebars.compileInline(loyaltyTemplateSVG)
    val receiptHeaderSvgWithFilledData = template.apply(storeData)
    val receiptHeaderSvg = SVG.getFromString(receiptHeaderSvgWithFilledData)
    receiptHeaderSvg.renderDPI = 300f
    val receiptHeaderPicture = receiptHeaderSvg.renderToPicture()
    val receiptHeaderBitmap = Bitmap.createBitmap(
        receiptHeaderPicture.width,
        receiptHeaderPicture.height,
        Bitmap.Config.RGB_565
    )
    val receiptHeaderCanvas = Canvas(receiptHeaderBitmap)
    receiptHeaderCanvas.drawPicture(receiptHeaderPicture)
    return receiptHeaderBitmap
}