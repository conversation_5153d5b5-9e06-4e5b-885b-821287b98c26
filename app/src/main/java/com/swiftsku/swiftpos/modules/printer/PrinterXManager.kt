package com.swiftsku.swiftpos.modules.printer

import android.content.Context
import com.sunmi.printerx.PrinterSdk
import com.sunmi.printerx.api.CashDrawerApi
import com.sunmi.printerx.style.BitmapStyle
import com.swiftsku.swiftpos.modules.cashdrawer.EmulatorDrawer
import com.swiftsku.swiftpos.utils.ENABLE_EMULATOR
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import io.sentry.Sentry
import javax.inject.Inject

class PrinterXManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val manager: PrinterSdk
) :
    ISunMiPrinter,
    PrinterSdk.PrinterListen {

    private var printer: PrinterSdk.Printer? = null

    override suspend fun print(
        printerTemplate: BitmapGenerator,
    ) {



        val receiptBitmap = printerTemplate.generateBitmap()
        val lineApi = printer?.lineApi()
        lineApi?.printBitmap(receiptBitmap, BitmapStyle.getStyle())
        lineApi?.autoOut()
    }

    override fun register() {
        try {
            manager.getPrinter(context, this)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    override fun unregister() {
        try {
            manager.destroy()
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    override fun onDefPrinter(p0: PrinterSdk.Printer?) {
        printer = p0
    }

    override fun onPrinters(p0: MutableList<PrinterSdk.Printer>?) {

    }

    override suspend fun validateCashDrawer(
        onCashDrawerAvailable: suspend (cashDrawer: CashDrawerApi) -> Unit,
        onCashDrawerNotAvailable: suspend () -> Unit
    ) {
        try {
            val cashDrawer = if (ENABLE_EMULATOR) {
                EmulatorDrawer()
            } else {
                printer?.cashDrawerApi()
            }
            if (cashDrawer != null) {
                onCashDrawerAvailable(cashDrawer)
            } else {
                onCashDrawerNotAvailable()
            }
        } catch (e: Exception) {
            EventUtils.recordException(e)
            onCashDrawerNotAvailable()
        }

    }

    override fun hasPrinter(): Boolean {
        return printer != null
    }
}