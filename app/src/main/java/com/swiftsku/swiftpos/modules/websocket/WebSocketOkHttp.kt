package com.swiftsku.swiftpos.modules.websocket

import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.utils.MAX_WS_CONNECTION_RETRIES
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WebSocketOkHttp @Inject constructor(
    private val webSocketRequest: Request,
    private val okHttpClient: OkHttpClient,
    @IOCoroutineScope val scope: CoroutineScope,
    val json: Json
) : WebSocketListener() {

    private var _webSocket: WebSocket? = null

    private val _message = MutableSharedFlow<String>()
    val message: SharedFlow<String> = _message

    private val _byteMessage = MutableSharedFlow<ByteString>()
    val byteMessage: SharedFlow<ByteString> = _byteMessage

    private val _socketState = MutableStateFlow(SocketState(connecting = true))
    val socketState: StateFlow<SocketState> = _socketState

    private var connectAttempts = 0
    private val maxRetries = MAX_WS_CONNECTION_RETRIES
    private val retryDelay = 1000L
    private var extraHeaders: Map<String, String>? = null

    interface Callback {
        fun readyToSend()
    }

    private var callback: Callback? = null

    fun connect(
        callback: Callback?,
        headers: Map<String, String>? = null,
        reconnecting: Boolean = false
    ) = scope.launch {
        if (reconnecting) {
            _socketState.emit(
                _socketState.value.copy(connecting = true, connectAttempts = connectAttempts)
            )
        } else {
            connectAttempts = 1
            _socketState.emit(
                SocketState(connecting = true, connectAttempts = connectAttempts)
            )
        }
        <EMAIL> = callback
        extraHeaders = headers
        val newRequest = headers?.let {
            webSocketRequest.newBuilder().apply {
                it.entries.forEach {
                    addHeader(it.key, it.value)
                }
            }.build()
        } ?: webSocketRequest
        _webSocket = okHttpClient.newWebSocket(newRequest, this@WebSocketOkHttp)
    }

    fun send(message: String) = scope.launch {
        _webSocket?.send(message)
    }

    /**
     * Immediately and violently release resources held by this web socket, discarding any enqueued
     * messages. This does nothing if the web socket has already been closed or canceled.
     */
    fun cancel() = scope.launch { _webSocket?.cancel() }

    fun disconnect(reason: String = "Client disconnected") = scope.launch {
        _webSocket?.close(1001, reason)
        _webSocket = null
    }

    private fun reconnect() = scope.launch {
        if ((connectAttempts < maxRetries) || _webSocket == null) {
            _webSocket?.close(1000, "Reconnecting")
            delay(retryDelay)
            connectAttempts++
            connect(callback, extraHeaders, true)
        } else {
            _socketState.emit(
                SocketState(
                    errorCode = 500,
                    errorMessage = "Retries exhausted",
                    connectAttempts = connectAttempts
                )
            )
        }
    }

    /**
     * Invoked when a web socket has been accepted by the remote peer and may begin transmitting messages.
     */
    override fun onOpen(webSocket: WebSocket, response: Response) {
        scope.launch {
            _socketState.emit(SocketState(isConnected = true))
            callback?.readyToSend()
        }
    }

    /**
     * Invoked when a text (type `0x1`) message has been received.
     */
    override fun onMessage(webSocket: WebSocket, text: String) {
        scope.launch {
            _message.emit(text)
        }
    }

    /**
     * Invoked when a binary (type 0x2) message has been received.
     */
    override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
        scope.launch {
            _byteMessage.emit(bytes)
        }
    }

    /**
     * Invoked when the remote peer has indicated that no more incoming messages will be transmitted.
     */
    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        // webSocket.close(code, reason)
    }

    /**
     * Invoked when both peers have indicated that no more messages will be transmitted and the
     * connection has been successfully released. No further calls to this listener will be made.
     */
    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        scope.launch {
            _socketState.emit(SocketState(closeCode = code, closeReason = reason))
        }
    }

    /**
     * Invoked when a web socket has been closed due to an error reading from or writing to the
     * network. Both outgoing and incoming messages may have been lost. No further calls to this
     * listener will be made.
     */
    override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
        scope.launch {
            if (connectAttempts <= maxRetries) {
                reconnect()
            } else {
                webSocket.cancel()
            }
            _socketState.emit(
                SocketState(errorCode = response?.code, errorMessage = t.message)
            )
        }
    }
}