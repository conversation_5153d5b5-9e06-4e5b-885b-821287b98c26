package com.swiftsku.swiftpos.modules.websocket.payment

import com.swiftsku.swiftpos.data.model.EbtType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class PaymentInputDTO(
    val epoch: Int,
    @SerialName("tdata")
    val tData: TData
)

@Serializable
data class TData(
    val token: String,
    val amount: Int,
    @SerialName("txn_id")
    val txnId: String,
    val attempt: Int,
    @SerialName("txn_type")
    val txnType: TxnType,
    @SerialName("payment_type")
    val paymentType: PaymentType,
    val fee: Int,
    @SerialName("ebt_type")
    val ebtType: EbtType? = null,
)

// NOTE: don't rename to uppercase, otherwise add KSerializer for this enum
// Because BE expects "merchandise" not "MERCHANDISE"
enum class TxnType {
    merchandise, fuel
}

// NOTE: don't rename to uppercase, otherwise add KSerializer for this enum
// Because BE expects "merchandise" not "MERCHANDISE"
enum class PaymentType {
    gen, ebt
}