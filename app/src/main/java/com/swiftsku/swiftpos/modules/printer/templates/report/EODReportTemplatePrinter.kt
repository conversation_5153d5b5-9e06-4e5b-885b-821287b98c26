package com.swiftsku.swiftpos.modules.printer

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import androidx.compose.ui.text.capitalize
import androidx.compose.ui.text.intl.Locale
import com.caverock.androidsvg.SVG
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.base64ToBitmap
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext


data class EODReportTemplate(
    val base64: String,
)

class EODReportTemplatePrinter(
    private val eodReport: EODReportTemplate,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
) : BitmapGenerator {
    override suspend fun generateBitmap(): Bitmap {
        return withContext(coroutineDispatcher) {
            base64ToBitmap(eodReport.base64)
        }
    }
}
