package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars


data class CashAdjustmentTemplate(val itemDescription: String, val value: String)

fun CashAdjustmentTemplate.map() = mapOf(
    "itemDescription" to itemDescription,
    "amount" to value
)

fun CashAdjustmentTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val data = map()
    val template = handlebars.compileInline(singleItemSvgTemplateString)
    val svgWithFilledData = template.apply(data)
    val svg = SVG.getFromString(svgWithFilledData)
    svg.renderDPI = 300f
    val picture = svg.renderToPicture()
    val bitmap = Bitmap.createBitmap(picture.width, picture.height, Bitmap.Config.RGB_565)
    val payoutCanvas = Canvas(bitmap)
    payoutCanvas.drawPicture(picture)
    return bitmap
}
