package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.utils.getFormattedDob


val txnInfoSvgTemplate = """
    {{#if hasContent}}
        <svg width="576" height="{{height}}" viewBox="0 0 576 {{height}}" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#fff"/>
          {{#if dateOfBirth}}
              <text fill="#000" xml:space="preserve" style="white-space:pre" font-family="Roboto" font-size="22" letter-spacing="0em">
                <tspan x="2%" y="20" font-weight="600">DOB: </tspan><tspan font-weight="400">{{dateOfBirth}}</tspan>
              </text>
          {{/if}}
          {{#if txnType}}
              <text fill="#000" xml:space="preserve" style="white-space:pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em" text-anchor="middle">
                <tspan x="50%" y="{{txnTypeY}}">{{txnType}}</tspan>
              </text>
          {{/if}}
        </svg>
    {{else}}
        <svg width="576" height="0" viewBox="0 0 576 0" fill="none" xmlns="http://www.w3.org/2000/svg"></svg>
    {{/if}}
""".trimIndent()


data class TxnInfoTemplate(
    val txnType: String? = null,
    val dateOfBirth: String? = null,
)

fun TxnInfoTemplate.map(): Map<String, Any?> {
    val hasContent = txnType != null || dateOfBirth != null
    val height = when {
        txnType != null && dateOfBirth != null -> 65
        txnType != null || dateOfBirth != null -> 35
        else -> 0
    }
    val txnTypeY = if (dateOfBirth != null) 50 else 20

    return mapOf(
        "txnType" to txnType,
        "dateOfBirth" to getFormattedDob(dateOfBirth),
        "hasContent" to hasContent,
        "height" to height,
        "txnTypeY" to txnTypeY
    )
}

fun TxnInfoTemplate.drawBitmap(handlebars: Handlebars): Bitmap? {

    val data = map()
    if (data["hasContent"] != true) {
        return null
    }

    val txnInfoTemplate = handlebars.compileInline(txnInfoSvgTemplate)
    val txnInfoTemplateFilledData = txnInfoTemplate.apply(data)
    val txnInfoSvg = SVG.getFromString(txnInfoTemplateFilledData)
    txnInfoSvg.renderDPI = 300f
    val txnInfoPicture = txnInfoSvg.renderToPicture()
    val txnInfoBitmap = Bitmap.createBitmap(
        txnInfoPicture.width,
        txnInfoPicture.height,
        Bitmap.Config.RGB_565
    )
    val txnInfoCanvas = Canvas(txnInfoBitmap)
    txnInfoCanvas.drawPicture(txnInfoPicture)

    return txnInfoBitmap
}