package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG

val merchantCopySVG = """
    <svg width="576" height="150" viewBox="0 0 576 150" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="576" height="100%" fill="white"/>
        
    <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em"
       text-anchor="middle"
      ><tspan x="50%" y="40">APPROVED</tspan></text>
      
        <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="600" letter-spacing="0.005em"
           text-anchor="middle"
          ><tspan x="50%" y="90">Customer Copy</tspan></text>
        <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0.005em"
           text-anchor="middle"
          ><tspan x="50%" y="120">Thank You for Shopping with us</tspan></text>
    </svg>
""".trimIndent()


data class TransactionTemplate(
    val storeTemplate: StoreTemplate,
    val txnInfoTemplate: TxnInfoTemplate? = null,
    val cartTemplate: CartTemplate? = null,
    val cardDetail: CardTemplate? = null,
    val transactionSummaryTemplate: TransactionSummaryTemplate? = null,
    val creditDetail: CreditTemplate? = null,
    val loyaltyTemplate: LoyaltyTemplate? = null,
    val ebtTemplate: EbtTemplate? = null,
    val barcodeTemplate: BarcodeTemplate? = null,
    val payoutTemplate: PayoutTemplate? = null,
    val accountEntryTemplate: AccountEntryTemplate? = null,
    val cashAdjustmentTemplate: CashAdjustmentTemplate? = null,
    val printFooter: Boolean = true
)

fun TransactionTemplate.drawMerchantCopyBitmap(): Bitmap {
    val merchantCopySVG = SVG.getFromString(merchantCopySVG)
    merchantCopySVG.renderDPI = 300f
    val merchantCopyPicture = merchantCopySVG.renderToPicture()
    val merchantCopyBitmap = Bitmap.createBitmap(
        merchantCopyPicture.width,
        merchantCopyPicture.height,
        Bitmap.Config.RGB_565
    )
    val receiptHeaderCanvas = Canvas(merchantCopyBitmap)
    receiptHeaderCanvas.drawPicture(merchantCopyPicture)
    return merchantCopyBitmap
}
