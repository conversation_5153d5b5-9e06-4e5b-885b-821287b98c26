package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.utils.generateBarcodeBitmap


data class BarcodeTemplate(
    val transactionId: String,
    val printTxnId: Boolean?
)


fun BarcodeTemplate.drawBitmap(): Bitmap? {
    if (printTxnId.isTrue()) {
        return generateBarcodeBitmap(transactionId, 576, 50)
    }
    return null
}