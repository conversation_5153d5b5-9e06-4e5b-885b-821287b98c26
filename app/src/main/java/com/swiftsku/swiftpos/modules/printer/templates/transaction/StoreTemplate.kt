package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars

val storeSvgTemplateString = """
        <svg width="576" height="250" viewBox="0 0 576 250" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="white"/>
            <text fill="black" font-family="sans-serif" font-size="42" font-weight="bold" letter-spacing="0em" text-anchor="middle" text-decoration="underline">
        <tspan x="50%" y="20%">{{storeName}}</tspan>
        </text>
            <text fill="black" font-family="sans-serif" font-size="32" font-weight="normal" letter-spacing="0em" text-anchor="middle">
        <tspan x="50%" y="40%">{{streetAddress}}</tspan>
        <tspan x="50%" y="55%">{{city}}, {{state}} {{zipCode}}</tspan>
        <tspan x="50%" y="68%" font-size="23">{{phoneNumber}}</tspan>
        </text>
            <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="11%" y="90%">{{transactionId}}</tspan></text>
            <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="600" letter-spacing="0em"><tspan x="2%" y="90%">Txn: </tspan></text>
            <text text-anchor="end" fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="98%" y="90%">{{date}}</tspan></text>
        </svg>
""".trimIndent()


data class StoreTemplate(
    val storeName: String,
    val streetAddress: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val phoneNumber: String,
    val date: String,
    val transactionId: String,
)

fun StoreTemplate.map() = mapOf(
    "storeName" to storeName,
    "streetAddress" to streetAddress,
    "city" to city,
    "state" to state,
    "zipCode" to zipCode,
    "phoneNumber" to phoneNumber,
    "date" to date,
    "transactionId" to transactionId,
)

fun StoreTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val storeData = map()
    val template = handlebars.compileInline(storeSvgTemplateString)
    val receiptHeaderSvgWithFilledData = template.apply(storeData)
    val receiptHeaderSvg = SVG.getFromString(receiptHeaderSvgWithFilledData)
    receiptHeaderSvg.renderDPI = 300f
    val receiptHeaderPicture = receiptHeaderSvg.renderToPicture()
    val receiptHeaderBitmap = Bitmap.createBitmap(
        receiptHeaderPicture.width,
        receiptHeaderPicture.height,
        Bitmap.Config.RGB_565
    )
    val receiptHeaderCanvas = Canvas(receiptHeaderBitmap)
    receiptHeaderCanvas.drawPicture(receiptHeaderPicture)
    return receiptHeaderBitmap
}