package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars

val creditSvgTemplate = """
<svg width="576" height="180" viewBox="0 0 576 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="white"/>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em"><tspan x="35%" y="18%">Credit Details</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="40%">Account Name</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="55%">{{accountName}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="40%">Account ID</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="55%">{{accountId}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="30" y="75%">Credit Limit</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="30" y="90%">{{creditLimit}}</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" letter-spacing="0em"><tspan x="288" y="75%">Outstanding Amount</tspan></text>
  <text fill="black" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="22" font-weight="500" letter-spacing="0em"><tspan x="288" y="90%">{{outstandingAmount}}</tspan></text>
  <rect width="516" height="1" transform="translate(30 175)" fill="black"/>
</svg>
""".trimIndent()


data class CreditTemplate(
    val accountName: String,
    val accountId: String,
    val creditLimit: String,
    val outstandingAmount: String
)

fun CreditTemplate.map() = mapOf(
    "accountName" to accountName,
    "accountId" to accountId,
    "creditLimit" to creditLimit,
    "outstandingAmount" to outstandingAmount
)

fun CreditTemplate.drawBitmap(handlebars: Handlebars): Bitmap {

    val data = map()

    val creditDetailsTemplate = handlebars.compileInline(creditSvgTemplate)
    val creditDetailsTemplateFilledData = creditDetailsTemplate.apply(data)
    val creditDetailsSvg = SVG.getFromString(creditDetailsTemplateFilledData)
    creditDetailsSvg.renderDPI = 300f
    val creditDetailsPicture = creditDetailsSvg.renderToPicture()
    val creditDetailsBitmap = Bitmap.createBitmap(
        creditDetailsPicture.width,
        creditDetailsPicture.height,
        Bitmap.Config.RGB_565
    )
    val creditDetailsCanvas = Canvas(creditDetailsBitmap)
    creditDetailsCanvas.drawPicture(creditDetailsPicture)

    return creditDetailsBitmap
}