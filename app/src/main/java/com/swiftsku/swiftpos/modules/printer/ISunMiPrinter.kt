package com.swiftsku.swiftpos.modules.printer

import com.sunmi.printerx.api.CashDrawerApi

interface ISunMiPrinter {
    fun register()
    fun unregister()
    suspend fun print(
        printerTemplate: BitmapGenerator,
    )

    suspend fun validateCashDrawer(
        onCashDrawerAvailable: suspend (cashDrawer: CashDrawerApi) -> Unit,
        onCashDrawerNotAvailable: suspend () -> Unit
    )

    fun hasPrinter(): <PERSON><PERSON><PERSON>

}