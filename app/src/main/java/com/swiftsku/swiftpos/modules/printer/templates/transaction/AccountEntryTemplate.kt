package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.data.model.LedgerEntryData
import com.swiftsku.swiftpos.data.model.getItemDescription
import com.swiftsku.swiftpos.extension.to2Decimal


data class AccountEntryTemplate(val ledgerEntryData: LedgerEntryData)

fun AccountEntryTemplate.map() = mapOf(
    "itemDescription" to ledgerEntryData.getItemDescription(),
    "amount" to (ledgerEntryData.amountCents / 100f).to2Decimal()
)

fun AccountEntryTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val data = map()
    val template = handlebars.compileInline(singleItemSvgTemplateString)
    val svgWithFilledData = template.apply(data)
    val svg = SVG.getFromString(svgWithFilledData)
    svg.renderDPI = 300f
    val picture = svg.renderToPicture()
    val bitmap = Bitmap.createBitmap(picture.width, picture.height, Bitmap.Config.RGB_565)
    val canvas = Canvas(bitmap)
    canvas.drawPicture(picture)
    return bitmap
}
