package com.swiftsku.swiftpos.modules.websocket.payment

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


/**
 * @param ok            true if payment was successful, false otherwise
 * @param completed     true when the payment flow is finished, false when in process
 * @param title         title for the UI
 * @param msg           message for the UI
 * @param info          info about the processed payment, e.g. card details, etc.
 */
@Serializable
data class PaymentResponseWS(
    val ok: Boolean? = false,
    val completed: Boolean? = false,
    val title: String?,
    val msg: String?,
    val info: Info? = null,
    val ecode: String? = null
) {
    fun isSuccess() = completed == true && ok == true
    fun isFailure() = completed == true && ok == false
}

@Serializable
data class Info(
    @SerialName("card_brand")
    val cardBrand: String? = "",
    @SerialName("card_number")
    val cardNumber: String? = "",
    @SerialName("payment_type")
    val paymentType: String? = "",
    @SerialName("approval_code")
    val approvalCode: String? = "",
    val mid: String? = "",
    @SerialName("processor_type")
    val processorType: String? = "",
    @SerialName("card_entry_mode")
    val cardEntryMode: String? = "",
    @SerialName("card_holder_name")
    val cardHolderName: String? = "",
    @SerialName("application_label")
    val applicationLabel: String? = "",
    @SerialName("payfac_id")
    val payfacId: String? = "",
    @SerialName("card_balance")
    val cardBalanceCents: Float? = 0f
)