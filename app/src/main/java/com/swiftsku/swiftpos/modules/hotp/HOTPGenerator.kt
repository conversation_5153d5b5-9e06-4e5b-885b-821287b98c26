package com.swiftsku.swiftpos.modules.hotp

import java.nio.ByteBuffer
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import kotlin.math.pow

class HOTPGenerator @Inject constructor(
    private val mac: Mac,
    private val digits: Int = 7,
    private val algorithm: String
) {
    fun generateHOTP(
        tid: String,
        token: String,
        counter: Long,
    ): String {
        val secret = "$token:$tid".toByteArray()
        val counterBytes = ByteBuffer.allocate(8).putLong(counter).array()
        val secretKey = SecretKeySpec(secret, algorithm)
        mac.init(secretKey)
        val hMac = mac.doFinal(counterBytes)
        val offset = (hMac.last().toInt() and 0x0f)
        val binary = ((hMac[offset].toInt() and 0x7f) shl 24
                or ((hMac[offset + 1].toInt() and 0xff) shl 16)
                or ((hMac[offset + 2].toInt() and 0xff) shl 8)
                or (hMac[offset + 3].toInt() and 0xff))
        val otp = binary % 10.0.pow(digits.toDouble())
        return otp.toInt().toString().padStart(digits, '0')
    }
}
