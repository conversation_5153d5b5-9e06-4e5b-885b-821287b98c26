package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.data.model.ReceiptItem
import com.swiftsku.swiftpos.extension.formattedAmount

val cartSvgTemplateString = """
<svg width="576" height="{{height}}" viewBox="0 0 576 {{height}}" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="576" height="100%" fill="white"/>
  <g transform="translate(0 10)">
    <text fill="black" font-family="sans-serif" font-size="25" font-weight="bold" letter-spacing="0em"><tspan x="2%" y="25">Description</tspan></text>
    <text fill="black" font-family="sans-serif" font-size="25" font-weight="bold" letter-spacing="0em"><tspan x="69%" y="25" text-anchor="end">Price*Qty</tspan></text>
    <text fill="black" font-family="sans-serif" font-size="25" font-weight="bold" letter-spacing="0em"><tspan x="98%" y="25" text-anchor="end">Amount</tspan></text>
  </g>
  <rect width="576" height="1" transform="translate(0 50)" fill="black"/>
  {{#each items}}
  <g transform="translate(0 {{transformYOffset}})">
    <text fill="black" font-family="sans-serif" font-size="22" font-weight="500" letter-spacing="0em">
                    <tspan x="2%" y="80" text-anchor="start">{{itemDescription}}</tspan>
                </text>
    <text fill="black" font-family="sans-serif" font-size="22" font-weight="500" letter-spacing="0em">
                    <tspan x="69%" y="80" text-anchor="end">{{priceXquantity}}</tspan>
                </text>
    <text fill="black" font-family="sans-serif" font-size="22" font-weight="500" letter-spacing="0em">
                    <tspan x="98%" y="80" text-anchor="end">{{amount}}</tspan>
                </text>
    {{#each promotions}}
    <g transform="translate(0 0)">
      <text fill="#272727" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="18" letter-spacing="0em"><tspan x="2%" y="{{yPosition}}">{{promotionReason}}</tspan></text>
      <text fill="#272727" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="18" letter-spacing="0em"><tspan x="98%" y="{{yPosition}}" text-anchor="end">-{{promotionAmount}}</tspan></text>
    </g>
    {{/each}}
        {{#each discounts}}
    <g transform="translate(0 0)">
      <text fill="#272727" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="18" letter-spacing="0em"><tspan x="2%" y="{{yPosition}}">{{name}}</tspan></text>
      <text fill="#272727" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="18" letter-spacing="0em"><tspan x="98%" y="{{yPosition}}" text-anchor="end">-{{amount}}</tspan></text>
    </g>
    {{/each}}
        {{#each extraInfo}}
    <g transform="translate(0 0)">
      <text fill="#272727" xml:space="preserve" style="white-space: pre" font-family="Roboto" font-size="18" letter-spacing="0em"><tspan x="2%" y="{{yPosition}}">{{extraInfoDesc}}</tspan></text>
    </g>
    {{/each}}
  </g>
  {{/each}}
</svg>
""".trimIndent()

data class CartTemplate(
    val itemDetails: List<ReceiptItem> = emptyList(),
)

fun CartTemplate.map(yPos: Int = 0): Map<String, Any> {
    var y = yPos

    return mapOf(
        "items" to itemDetails.map { item ->
            val currentY = y
            y += 50 + (item.extraInfo.size * 20) + (item.promotions.size * 20) + (item.discounts.size * 20)

            var subItemY = 105 // Starting Y position for promotions/discounts/extraInfo

            val priceXquantity = item.volume?.let {
                "${item.price}x${it}"
            } ?: "${item.price}x${item.quantity}"
            mapOf(
                "itemDescription" to item.description.take(20),
                "quantity" to item.quantity,
                "priceXquantity" to priceXquantity,
                "amount" to item.amount,
                "transformYOffset" to currentY,
                "promotions" to item.promotions.map {
                    val currentSubItemY = subItemY
                    subItemY += 20
                    mapOf(
                        "promotionAmount" to it.promotionAmount.formattedAmount(),
                        "promotionReason" to it.promotionReason,
                        "yPosition" to currentSubItemY
                    )
                },
                "discounts" to item.discounts.map {
                    val currentSubItemY = subItemY
                    subItemY += 20
                    mapOf(
                        "name" to it.name,
                        "amount" to it.amount.toFloat().formattedAmount(),
                        "yPosition" to currentSubItemY
                    )
                },
                "extraInfo" to item.extraInfo.map {
                    val currentSubItemY = subItemY
                    subItemY += 20
                    mapOf(
                        "extraInfoDesc" to it,
                        "yPosition" to currentSubItemY
                    )
                }
            )
        },
        "height" to (y + 60)
    )
}

fun CartTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val cartData = map(10)
    val cartTemplate = handlebars.compileInline(cartSvgTemplateString)
    val cartSvgWithFilledData = cartTemplate.apply(cartData)
    val cartSvg = SVG.getFromString(cartSvgWithFilledData)
    cartSvg.renderDPI = 300f
    val cartPicture = cartSvg.renderToPicture()
    val cartBitmap =
        Bitmap.createBitmap(cartPicture.width, cartPicture.height, Bitmap.Config.RGB_565)
    val cartCanvas = Canvas(cartBitmap)
    cartCanvas.drawPicture(cartPicture)
    return cartBitmap
}
