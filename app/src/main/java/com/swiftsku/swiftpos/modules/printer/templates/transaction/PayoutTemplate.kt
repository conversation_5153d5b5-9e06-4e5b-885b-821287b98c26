package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.extension.formattedAmount

val singleItemSvgTemplateString = """
<svg width="576" height="100" viewBox="0 0 576 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="576" height="100%" fill="white"/>
  <g transform="translate(0 10)">
    <text fill="black" font-family="sans-serif" font-size="25" font-weight="bold" letter-spacing="0em"><tspan x="2%" y="25">Description</tspan></text>
    <text fill="black" font-family="sans-serif" font-size="25" font-weight="bold" letter-spacing="0em"><tspan x="98%" y="25" text-anchor="end">Amount</tspan></text>
  </g>
  <rect width="576" height="1" transform="translate(0 50)" fill="black"/>
  <g transform="translate(0 0)">
    <text fill="black" font-family="sans-serif" font-size="22" font-weight="500" letter-spacing="0em">
        <tspan x="2%" y="80" text-anchor="start">{{itemDescription}}</tspan>
    </text>
    <text fill="black" font-family="sans-serif" font-size="22" font-weight="500" letter-spacing="0em">
        <tspan x="98%" y="80" text-anchor="end">{{amount}}</tspan>
    </text>
  </g>
  <rect width="576" height="1" transform="translate(0 95)" fill="black"/>
</svg>
""".trimIndent()

data class PayoutTemplate(val payout: Payout)

fun PayoutTemplate.map() = mapOf(
    "itemDescription" to payout.info,
    "amount" to payout.amount.formattedAmount()
)

fun PayoutTemplate.drawBitmap(handlebars: Handlebars): Bitmap {
    val payoutData = map()
    val payoutTemplate = handlebars.compileInline(singleItemSvgTemplateString)
    val payoutSvgWithFilledData = payoutTemplate.apply(payoutData)
    val payoutSvg = SVG.getFromString(payoutSvgWithFilledData)
    payoutSvg.renderDPI = 300f
    val payoutPicture = payoutSvg.renderToPicture()
    val payoutBitmap =
        Bitmap.createBitmap(payoutPicture.width, payoutPicture.height, Bitmap.Config.RGB_565)
    val payoutCanvas = Canvas(payoutBitmap)
    payoutCanvas.drawPicture(payoutPicture)
    return payoutBitmap
}
