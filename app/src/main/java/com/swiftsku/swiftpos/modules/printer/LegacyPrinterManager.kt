package com.swiftsku.swiftpos.modules.printer

import android.content.Context
import com.sunmi.peripheral.printer.InnerPrinterCallback
import com.sunmi.peripheral.printer.InnerPrinterManager
import com.sunmi.peripheral.printer.InnerResultCallback
import com.sunmi.peripheral.printer.SunmiPrinterService
import com.sunmi.printerx.api.CashDrawerApi
import com.swiftsku.swiftpos.utils.EventUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import io.sentry.Sentry
import javax.inject.Inject


class LegacyPrinterManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val manager: InnerPrinterManager
) :
    <PERSON>unMiPrinter,
    InnerPrinterCallback() {


    private var printer: SunmiPrinterService? = null

    override fun onConnected(service: SunmiPrinterService?) {
        printer = service
    }

    override fun onDisconnected() {
        printer = null
    }

    private val printerResultCallback = object : InnerResultCallback() {
        override fun onRunResult(isSuccess: <PERSON>olean) {

        }

        override fun onReturnString(result: String?) {

        }

        override fun onRaiseException(code: Int, msg: String?) {

        }

        override fun onPrintResult(code: Int, msg: String?) {

        }

    }

    override suspend fun print(
        printerTemplate: BitmapGenerator,
    ) {
        val receiptBitmap = printerTemplate.generateBitmap()
        printer?.printBitmap(receiptBitmap, printerResultCallback)
        printer?.lineWrap(3, printerResultCallback)
        printer?.cutPaper(printerResultCallback)
    }

    override suspend fun validateCashDrawer(
        onCashDrawerAvailable: suspend (cashDrawer: CashDrawerApi) -> Unit,
        onCashDrawerNotAvailable: suspend () -> Unit
    ) {

    }

    override fun register() {
        try {
            manager.bindService(context, this)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    override fun hasPrinter(): Boolean {
        return this.printer != null
    }

    override fun unregister() {
        try {
            manager.unBindService(context, this)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

}
