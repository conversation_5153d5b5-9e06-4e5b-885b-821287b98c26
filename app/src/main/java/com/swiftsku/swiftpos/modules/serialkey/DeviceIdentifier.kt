package com.swiftsku.swiftpos.modules.serialkey

import android.os.Build
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.utils.ENABLE_EMULATOR
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry

object DeviceIdentifier {
    /*
    READ_PRIVILEGED_PHONE_STATE is not available for Android 9
    READ_PHONE_STATE is given as a superApp permission from Sunmi MDM config
    Build.getSerial() works for Android 8 to 10. Sunmi platform is Android 9
    */
    fun getSerialNumber(): String? {
        return try {
            val serial: String = if (ENABLE_EMULATOR) BuildConfig.TEST_POS_ID else Build.getSerial()
            serial
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }
}
