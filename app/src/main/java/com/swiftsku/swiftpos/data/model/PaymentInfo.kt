package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.SecuredPaymentProcessorMutation
import com.swiftsku.swiftpos.payment.SecuredPaymentRefunderMutation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PaymentInfo(
    val applicationLabel: String? = null,
    val approvalCode: String? = null,
    val cardBrand: String? = null,
    val cardEntryMode: String? = null,
    val cardHolderName: String? = null,
    val cardNumber: String? = null,
    val errCode: String? = null,
    val errMsg: String? = null,
    val failureCode: String? = null,
    val failureMessage: String? = null,
    val ok: Boolean = false,
    val paymentType: String? = null,
    val pinVerified: String? = null,
    val mid: String? = null,
    val infoMsg: String? = null
)

@Serializable
data class TxnInsights(
    val amount: Int? = null,
    val gatewayProcessTime: Float? = null,
    val mid: String? = null,
    val txnId: String? = null,
    val txnTime: Float? = null
)

@Serializable
data class PaymentResponse(
    val data: TxnInsights? = null,
    val details: String? = null,
    @SerialName("eventid")
    val eventId: String? = null,
    val msg: String? = null,
    @SerialName("payfacInfo")
    val payFacInfo: PaymentInfo,
    val status: Boolean = false,
)

fun SecuredPaymentProcessorMutation.ResponseMessage.extractResponse(): PaymentResponse {
    return PaymentResponse(
        payFacInfo = PaymentInfo(
            cardBrand = this.payfacInfo?.cardBrand,
            cardEntryMode = this.payfacInfo?.cardEntryMode,
            cardHolderName = this.payfacInfo?.cardHolderName,
            cardNumber = this.payfacInfo?.cardNumber,
            failureCode = this.payfacInfo?.failureCode,
            failureMessage = this.payfacInfo?.failureMessage,
            paymentType = this.payfacInfo?.paymentType,
            ok = this.payfacInfo?.ok ?: false,
            errCode = this.payfacInfo?.errCode,
            errMsg = this.payfacInfo?.errMsg,
            applicationLabel = this.payfacInfo?.applicationLabel,
            approvalCode = this.payfacInfo?.approvalCode,
            pinVerified = this.payfacInfo?.pinVerified,
            mid = "",
            infoMsg = this.payfacInfo?.infoMsg
        ),
        details = this.details,
        data = TxnInsights(
            amount = this.data?.amount,
            gatewayProcessTime = this.data?.gatewayProcessTime?.toFloat(),
            txnId = this.data?.txnId,
            txnTime = this.data?.txnTime?.toFloat(),
            mid = this.data?.mid
        ),
        eventId = this.eventid,
        msg = this.msg,
        status = this.status ?: false
    )
}

fun SecuredPaymentRefunderMutation.ResponseMessage.extractResponse(): PaymentResponse {
    return PaymentResponse(
        payFacInfo = PaymentInfo(
            cardBrand = this.payfacInfo?.cardBrand,
            cardEntryMode = this.payfacInfo?.cardEntryMode,
            cardHolderName = this.payfacInfo?.cardHolderName,
            cardNumber = this.payfacInfo?.cardNumber,
            failureCode = this.payfacInfo?.failureCode,
            failureMessage = this.payfacInfo?.failureMessage,
            paymentType = this.payfacInfo?.paymentType,
            ok = this.payfacInfo?.ok ?: false,
            errCode = this.payfacInfo?.errCode,
            errMsg = this.payfacInfo?.errMsg,
            applicationLabel = this.payfacInfo?.applicationLabel,
            approvalCode = this.payfacInfo?.approvalCode,
            pinVerified = this.payfacInfo?.pinVerified,
            mid = "",
            infoMsg = this.payfacInfo?.infoMsg
        ),
        details = this.details,
        data = TxnInsights(
            amount = this.data?.amount,
            gatewayProcessTime = this.data?.gatewayProcessTime?.toFloat(),
            txnId = this.data?.txnId,
            txnTime = this.data?.txnTime?.toFloat(),
            mid = this.data?.mid
        ),
        eventId = this.eventid,
        msg = this.msg,
        status = this.status ?: false
    )
}