package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class ReleasesDoc(
    @SerialName("doc_type")
    val docType: String = "support_item",
    val releases: List<ReleaseData> = emptyList()
)

@Serializable
data class ReleaseData(
    val version: String = "",
    val date: String = "",
    @SerialName("update_type")
    val updateType: ReleaseUpdateType = ReleaseUpdateType.MINOR,
    val notes: ReleaseNotes? = null
)

@Serializable
data class ReleaseNotes(
    val features: List<ReleaseNotesContent> = emptyList(),
    val changes: List<ReleaseNotesContent> = emptyList(),
    val bugs: List<ReleaseNotesContent> = emptyList()
)

@Serializable
data class ReleaseNotesContent(
    val info: String = "",
    val contentType: ContentType? = null,
    val link: String? = null,
    val deepLink: String? = null
)

@Serializable
enum class ReleaseUpdateType {
    MAJOR, MINOR
}

@Serializable
enum class ContentType {
    TEXT, IMAGE, GIF, VIDEO, LINK
}