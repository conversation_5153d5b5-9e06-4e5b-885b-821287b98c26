package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

//"txnId": "1ff6e-1-4842d29",
//"txnType": "PAYOUT",
//"txnStatus": "COMPLETE",
//"isIntegrated": false // Should always be sent as false by pos-app
//"txnStartTime": "<epoch_ms>",
//"txnEndTime": "<epoch_ms>",
//"payoutData": {
//    "category": "VENDOR", // [VENDOR, LOTTERY]
//    "info": "Pepsi",
//    "amount": <float>,
//},
//"cashierId": "cashier"

@Serializable
data class PayoutEvent(
    override val txnId: String,
    override val txnItems: List<TransactionItem> = emptyList(),
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date,
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date = Date(),
    override val txnType: TransactionType = TransactionType.Payout,
    override val txnStatus: TransactionStatus = TransactionStatus.Complete,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    override val cashierId: String,
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    val fromSale: Boolean = false,
    val payoutData: Payout,
    override var paymentRecord: PaymentRecord? = null
) : Transaction()
