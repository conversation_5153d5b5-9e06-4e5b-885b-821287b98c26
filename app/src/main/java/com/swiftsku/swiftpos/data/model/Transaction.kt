package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.security.MessageDigest
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.TimeZone
import kotlin.math.max

@Serializable
sealed class Transaction {
    abstract val txnId: String
    abstract val txnItems: List<TransactionItem>

    @Serializable(with = DateSerializer::class)
    abstract val txnStartTime: Date

    @Serializable(with = DateSerializer::class)
    abstract val txnEndTime: Date?
    abstract val txnType: TransactionType?
    abstract val txnStatus: TransactionStatus
    abstract val statusHistory: HashMap<Int, TransactionStatus>?
    abstract val cashierId: String
    abstract val isIntegrated: Boolean
    abstract val isCashIntegrated: Boolean
    abstract var paymentRecord: PaymentRecord?
}


@Serializable
data class BaseTransaction(
    override val txnId: String,
    override val txnItems: List<TransactionItem>,
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date? = null,
    override val txnType: TransactionType,
    override val txnStatus: TransactionStatus,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    override val cashierId: String,
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date = Date(),
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    override var paymentRecord: PaymentRecord? = null
) : Transaction()

fun Transaction.totalAmount(): Float {
    return when (this) {
        is SaleTransaction -> this.txnTotalGrandAmount
        is RefundTransaction -> this.txnTotalGrandAmount
        is VoidTransaction -> this.txnTotalGrandAmount
        is PayoutEvent -> -1 * this.payoutData.amount
        is CashWithdrawalTxn -> -1 * (this.data.amount ?: 0f)
        is CashDepositTxn -> (this.data.amount ?: 0f)
        is AccountLedgerTxn -> (this.data.amountCents / 100f)
        else -> 0f
    }
}

fun Transaction.typeName(): String = when (this) {
    is SaleTransaction -> if (this.txnType == TransactionType.SaleRefund) "Sale Refund" else "Sale"
    is RefundTransaction -> "Refund"
    is VoidTransaction -> "Void"
    is NoSaleTransaction -> "No Sale"
    is PayoutEvent -> "${this.payoutData.category.type} Payout (${this.payoutData.info})"
    is CashWithdrawalTxn -> "Safe Drop"
    is CashDepositTxn -> "Safe Loan"
    is AccountLedgerTxn -> "Ledger Entry"
    else -> txnType?.tender ?: "Unknown"
}

fun Transaction.enableRefund(): Boolean {
    if (this is SaleTransaction) {
        val refundableAmounts = getRefundableAmounts(this)
        if (refundableAmounts.getTotalRefundAmount() == 0.0) return false
    }
    return this.txnType == TransactionType.Sale && txnStatus == TransactionStatus.Complete
}


fun Transaction.promotionAmount(): Float {
    return when (this) {
        is SaleTransaction -> (this.subtotalAmount() + this.totalTaxAmount() - this.totalAmount()).to2Decimal()
        is RefundTransaction -> (this.subtotalAmount() + this.totalTaxAmount() - this.totalAmount()).to2Decimal()
        else -> 0f
    }
}

fun Transaction.ebtAmount(): Float {

//    txnItems.filter { it.isEBT() }.forEach { it. }

    return when (this) {
        is SaleTransaction -> (this.subtotalAmount() + this.totalTaxAmount() - this.totalAmount()).to2Decimal()
        is RefundTransaction -> (this.subtotalAmount() + this.totalTaxAmount() - this.totalAmount()).to2Decimal()
        else -> 0f
    }
}


fun Transaction.subtotalAmount(): Float {
    return when (this) {
        is SaleTransaction -> this.txnTotalNetAmount
        is RefundTransaction -> this.txnTotalNetAmount
        else -> 0f
    }
}

fun Transaction.totalTaxAmount(): Float {
    return when (this) {
        is SaleTransaction -> this.txnTotalTaxNetAmount
        is RefundTransaction -> this.txnTotalTaxNetAmount
        else -> 0f
    }
}

fun Transaction.showReprint(): Boolean =
    txnType in listOf(
        TransactionType.Sale,
        TransactionType.SaleRefund,
        TransactionType.Refund,
        TransactionType.Payout,
        TransactionType.CashDeposit,
        TransactionType.CashWithdrawal,
        TransactionType.Void,
        TransactionType.AccountEntry
    )

fun generatePumpRequestId(pumpId: Int): String {
    val last5digits = (Calendar.getInstance().timeInMillis % 100000)
        .toString()
        .padStart(5, '0')
    return "$pumpId$last5digits"
}

fun generateTransactionId(storeCode: String, posNumber: String = "1"): String {
    val date = (Calendar.getInstance(TimeZone.getDefault()).timeInMillis / 1000).toString(16)
    return "${storeCode}-${posNumber}-${date.slice(IntRange(1, date.length - 1))}"
}

fun getDateFromFdcTimestamp(timestamp: String?): Date {
    return try {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        val localDateTime = LocalDateTime.parse(timestamp, formatter)
        Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())
    } catch (ex: Exception) {
        EventUtils.recordException(ex)
        return Date()
    }
}

fun generateTxnId(storeCode: String, timestamp: String?): String {
    val inputToHash = if (!timestamp.isNullOrBlank()) {
        timestamp
    } else {
        // Use current time in ISO_LOCAL_DATE_TIME format as fallback
        LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault())
            .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    }
    val digest = MessageDigest.getInstance("SHA-256")
    val hash = digest.digest(inputToHash.toByteArray())
        .take(4)
        .joinToString("") { "%02x".format(it) }

    // ToDo: Replace "99" with better logic of assigning to any specific terminal
    return "$storeCode-99-$hash"
}


val Transaction.hasFuel: Boolean get() = txnItems.any { it is TransactionItemWithFuel }
val Transaction.hasPostFuel: Boolean get() = txnItems.any { it is TransactionItemWithFuel && it.postFuel != null }

/**
 * Regex to check if the transaction ID is valid.
 * We have used logic to expect 1st part of the string to be the store code,
 * 2nd part can be any string and
 * the 3rd part is created using Hexadecimal base, so it should only contain 0-9 & a-f characters.
 */
fun isValidTransactionId(storeId: String, txnId: String): Boolean {
    val regex = "^$storeId-[^\\-]+-[a-f0-9]+$".toRegex()
    return regex.matches(txnId)
}

/**
 * Returns refundable amount as a pair of Gen and EBT amount in dollars
 */
fun getRefundableAmounts(transaction: SaleTransaction): RefundableAmounts {
    val cardPayment = transaction.txnPayment[TxnPaymentType.Card]
    val ebtPayment = transaction.txnPayment[TxnPaymentType.EBT]
    val cashPayment = transaction.txnPayment[TxnPaymentType.Cash]
    val chequePayment = transaction.txnPayment[TxnPaymentType.Cheque]
    val creditPayment = transaction.txnPayment[TxnPaymentType.Credit]
    var genRefundAmount = (cardPayment as? CardPayment)?.amount?.toDouble() ?: 0.0
    val ebtRefundAmount = (ebtPayment as? EBTPayment)?.amount?.toDouble() ?: 0.0
    var creditRefundAmount = (creditPayment as? CreditPayment)?.amount?.toDouble() ?: 0.0
    var cashRefundAmount = 0.0
    (cashPayment as? CashPayment)?.let {
        cashRefundAmount = (it.tender - cashPayment.change).toDouble().to2Decimal()
    }
    (chequePayment as? ChequePayment)?.let {
        cashRefundAmount = cashRefundAmount.plus(it.amount)
    }
    // If there was any lottery payout, consider it as cash paid.
    val lotteryPayout = transaction.lotteryPayouts?.sumOfFloats { it.amount } ?: 0f
    if (lotteryPayout > 0f) {
        cashRefundAmount = cashRefundAmount.plus(lotteryPayout)
    }
    if (transaction.hasPostFuel) {
        /**
         * We deduct fuel amount from the card payment first.
         * In case there is still fuel amount pending, it must have been paid through cash.
         * In case there is still fuel amount pending, it must have been paid through credit.
         * (Considering we don't support EBT on fuel yet)
         */
        var remainingFuelAmount = transaction.fuelAmount().to2Decimal()

        // Deduct from card refund first
        val cardDeduction = minOf(remainingFuelAmount, genRefundAmount)
        genRefundAmount -= cardDeduction
        remainingFuelAmount -= cardDeduction

        // Then from credit
        val creditDeduction = minOf(remainingFuelAmount, creditRefundAmount)
        creditRefundAmount -= creditDeduction
        remainingFuelAmount -= creditDeduction

        // Then from cash
        val cashDeduction = minOf(remainingFuelAmount, cashRefundAmount)
        cashRefundAmount -= cashDeduction
        remainingFuelAmount -= cashDeduction
    }
    return RefundableAmounts(genRefundAmount, ebtRefundAmount, cashRefundAmount, creditRefundAmount)
}

data class RefundableAmounts(
    val cardAmount: Double,
    val ebtAmount: Double,
    val cashAmount: Double,
    val creditAmount: Double
)

fun RefundableAmounts.getTotalRefundAmount(): Double {
    return cardAmount + ebtAmount + cashAmount + creditAmount
}