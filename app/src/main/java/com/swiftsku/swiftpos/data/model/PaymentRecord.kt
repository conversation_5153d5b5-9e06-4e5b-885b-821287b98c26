package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.domain.payment.dto.DoCreditRequestDto
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditResponseDto
import kotlinx.serialization.Serializable


@Serializable
data class PaymentRecord(
    var payment: RecordData?,
    var refund: RecordData?,
    var lastModStamp: Int,
    var auth: RecordData? = null,
    var inquiry: RecordData? = null,
    var originalReferenceNumber: String? = null
)

@Serializable
data class RecordData(
    var gen: MutableMap<String, RecordDetails>?,
    var ebt: MutableMap<String, RecordDetails>?,
)

@Serializable
data class RecordDetails(
    var payload: DoCreditRequestDto?,
    var res: DoCreditResponseDto?
)