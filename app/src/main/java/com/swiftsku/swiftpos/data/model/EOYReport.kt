package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import com.swiftsku.swiftpos.payment.type.SalesSummaryDataResponse
import kotlinx.serialization.Serializable

@Serializable
sealed class SalesSummaryData {
    abstract val storeCode: String
    abstract val stats: List<ReportDetail>
}

@Serializable
data class EOYReport(
    override val storeCode: String,
    override val stats: List<ReportDetail> = listOf()
) : SalesSummaryData()

@Serializable
data class ReportDetail(
    val terminalId: String,
    val terminalData: String
)


fun SalesSummaryReportQuery.Data.extractEOYResponse(): EOYReport? {
    val salesSummaryData = this.salesSummaryData
    if (salesSummaryData != null) {
        return EOYReport(
            stats = salesSummaryData.stats?.filterNotNull()?.map { stat ->
                ReportDetail(
                    stat.terminalId,
                    stat.terminalData
                )
            } ?: listOf(),
            storeCode = salesSummaryData.storeCode
        )
    }
    return null
}