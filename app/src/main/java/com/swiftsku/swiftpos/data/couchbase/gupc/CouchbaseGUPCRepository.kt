package com.swiftsku.swiftpos.data.couchbase.gupc

import android.util.Log
import com.couchbase.lite.Collection
import com.couchbase.lite.DataSource
import com.couchbase.lite.Expression
import com.couchbase.lite.QueryBuilder
import com.couchbase.lite.SelectResult
import com.couchbase.lite.queryChangeFlow
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.di.qualifiers.GUPCCollection
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.barcode.gs1utils.GTIN
import com.swiftsku.swiftpos.utils.upcEtoA
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import javax.inject.Inject

class CouchbaseGUPCRepository @Inject constructor(
    @GUPCCollection private val collection: Collection,
    private val coroutineDispatcher: CoroutineDispatcher = Dispatchers.IO
) : GUPCRepository {
    override fun getGUPCStream(): Flow<List<PluItem>> {
        val query = QueryBuilder
            .select(
                SelectResult.property("plumap")
            )
            .from(DataSource.collection(collection))
            .where(Expression.property("doc_type").equalTo(Expression.string("gupc_item")))


        val json_ = Json {
            ignoreUnknownKeys = true
        }
        val flow = query.queryChangeFlow()
            .map { queryChange ->
                val priceBookList = mutableListOf<PluItem>()
                queryChange.results?.let { results ->
                    results.forEach { result ->
                        val pluMap =
                            json_.decodeFromString<JsonObject>(result.toJSON())["plumap"]?.jsonObject
                        for (key in pluMap!!.keys) {
                            val pricebookItem =
                                json_.decodeFromString<PluItem>(pluMap[key]!!.toString())
                            priceBookList.add(pricebookItem)
                        }
                    }
                }
                priceBookList.toList()
            }
            .flowOn(Dispatchers.IO)
        query.execute()
//        Log.d("query gupc", "gupc done")
        return flow

    }

    override suspend fun findByBarcode(barcode: String): PluItem? {
        return withContext(coroutineDispatcher) {


            Log.d("GUPC barcode", barcode)
            val json_ = Json {
                ignoreUnknownKeys = true
            }
            return@withContext try {
                val documentResult =
                    collection.getDocument(barcode)?.toJSON() ?: return@withContext null
                val pluMap =
                    json_.decodeFromString<JsonObject>(documentResult)["plumap"]?.jsonObject
                return@withContext json_.decodeFromString<PluItem>(pluMap?.get("000")!!.toString())
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }
    }
}