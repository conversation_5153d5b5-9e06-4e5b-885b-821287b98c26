package com.swiftsku.swiftpos.data.dtos.couchbase

import com.swiftsku.swiftpos.data.model.Tax
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class StoreTaxes(
    @SerialName("store_id")
    val storeId: String,
    @SerialName("store_code")
    val storeCode: String,
    val taxes: Map<String, Tax> = emptyMap()
)

@Serializable
data class PayoutOptions(
    @SerialName("store_id")
    val storeId: String,
    @SerialName("store_code")
    val storeCode: String,
    val vendor: List<String> = emptyList(),
    val lottery: List<String> = emptyList(),
)