package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class NoSaleTransaction(
    override val txnId: String,
    override val txnItems: List<TransactionItem> = emptyList(),
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date,
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date = Date(),
    override val txnType: TransactionType? = TransactionType.NoSale,
    override val txnStatus: TransactionStatus = TransactionStatus.Complete,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    val cashBalance: Float? = null,
    override val cashierId: String,
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    override var paymentRecord: PaymentRecord? = null
) : Transaction()
