package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = PayoutTypeSerializer::class)
sealed class PayoutType(val type: String) {
    object Vendor : PayoutType("VENDOR")
    object Lottery : PayoutType("LOTTERY")
}


object PayoutTypeSerializer : KSerializer<PayoutType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("PayoutType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: PayoutType) {
        encoder.encodeString(value.type)
    }

    override fun deserialize(decoder: Decoder): PayoutType {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            PayoutType.Vendor.type -> PayoutType.Vendor
            PayoutType.Lottery.type -> PayoutType.Lottery
            else -> throw IllegalArgumentException("Unknown PayoutType: $format")
        }
    }
}