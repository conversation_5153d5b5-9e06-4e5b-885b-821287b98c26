package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class User(
    val active: Boolean = true,
    @SerialName("user_type")
    val userType: String = "owner",
    @SerialName("first_name")
    val firstName: String,
    val username: String,
    @SerialName("last_name")
    val lastName: String,
    val email: String,
    val password: String,
    @SerialName("phone_number")
    val phoneNumber: String,
    @SerialName("member_since")
    @Serializable(with = DateSerializer::class)
    val memberSince: Date,
    @SerialName("settings")
    val settings: UserSettings? = null
)
