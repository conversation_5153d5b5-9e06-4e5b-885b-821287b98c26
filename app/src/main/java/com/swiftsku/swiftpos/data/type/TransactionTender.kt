package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = TransactionTenderSerializer::class)
sealed class TransactionTender(val tender: String) {
    object Cash : TransactionTender("CASH")
    object Credit : TransactionTender("CREDIT")
    object Debit : TransactionTender("DEBIT")
    object Card : TransactionTender("CARD")
    object Other : TransactionTender("OTHER")
    object Hybrid : TransactionTender("HYBRID")
}

object TransactionTenderSerializer : KSerializer<TransactionTender> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("TransactionTender", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TransactionTender) {
        encoder.encodeString(value.tender)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): TransactionTender {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            "CASH" -> TransactionTender.Cash
            "CREDIT" -> TransactionTender.Credit
            "DEBIT" -> TransactionTender.Debit
            "CARD" -> TransactionTender.Card
            "OTHER" -> TransactionTender.Other
            "HYBRID" -> TransactionTender.Hybrid
            else -> throw IllegalArgumentException("Unknown TransactionTender: $format")
        }
    }
}