package com.swiftsku.swiftpos.data.remote.repositories.app

import com.apollographql.apollo3.ApolloClient
import com.swiftsku.swiftpos.data.model.PricebookSearchResponse
import com.swiftsku.swiftpos.data.model.extractResponse
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.ReportApolloClient
import com.swiftsku.swiftpos.payment.FetchPricebookQuery
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject


class AppRepository @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    @ReportApolloClient private val apolloClient: ApolloClient
) {

    suspend fun searchPricebook(
        storeCode: String, searchQuery: String
    ): Result<PricebookSearchResponse>? =
        withContext(dispatcher) {
            try {
                val response = apolloClient.query(
                    FetchPricebookQuery(storeCode, searchQuery.lowercase())
                ).execute()

                if (!response.hasErrors()) {
                    val fetchPricebookResponse = response.data?.pricebook
                    fetchPricebookResponse?.let {
                        return@withContext Result.Success(it.extractResponse())
                    } ?: run {
                        return@withContext Result.Error("Received null response")
                    }
                } else {
                    return@withContext Result.Error(response.errors.toString())
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext Result.Error(e.message ?: "Couldn't search Pricebook")
            }
        }
}