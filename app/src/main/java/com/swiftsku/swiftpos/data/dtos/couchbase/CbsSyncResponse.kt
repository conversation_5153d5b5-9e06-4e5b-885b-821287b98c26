package com.swiftsku.swiftpos.data.dtos.couchbase

import com.swiftsku.swiftpos.payment.InitCbsSyncMutation
import kotlinx.serialization.Serializable

@Serializable
data class CbsSyncResponse(
    val id: String?,
    val ok: Boolean = false,
    val rev: String?,
)


fun InitCbsSyncMutation.Data.extractCbsSyncResponse(): List<CbsSyncResponse> {
    val response = this.initCbsSync

    val result = mutableListOf<CbsSyncResponse>()

    if (response != null) {
        response.responseMessage?.data?.filterNotNull()?.forEach {
            result.add(
                CbsSyncResponse(
                    id = it.id,
                    ok = it.ok ?: false,
                    rev = it.rev
                )
            )
        }
    }
    return result
}