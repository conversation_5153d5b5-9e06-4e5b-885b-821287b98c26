package com.swiftsku.swiftpos.data.remote.repositories.report

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import com.swiftsku.swiftpos.data.couchbase.report.ReportRepository
import com.swiftsku.swiftpos.data.dtos.couchbase.CbsSyncResponse
import com.swiftsku.swiftpos.data.dtos.couchbase.extractCbsSyncResponse
import com.swiftsku.swiftpos.data.model.AdHocReport
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EOMReport
import com.swiftsku.swiftpos.data.model.EOYReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReportList
import com.swiftsku.swiftpos.data.model.HistoricalShiftReport
import com.swiftsku.swiftpos.data.model.HistoricalShiftReportList
import com.swiftsku.swiftpos.data.model.PresetReport
import com.swiftsku.swiftpos.data.model.ShiftReport
import com.swiftsku.swiftpos.data.model.extractAdHocReportResponse
import com.swiftsku.swiftpos.data.model.extractEOYResponse
import com.swiftsku.swiftpos.data.model.extractHistoricalBatchReportListResponse
import com.swiftsku.swiftpos.data.model.extractHistoricalBatchReportResponse
import com.swiftsku.swiftpos.data.model.extractHistoricalShiftReportListResponse
import com.swiftsku.swiftpos.data.model.extractHistoricalShiftReportResponse
import com.swiftsku.swiftpos.data.model.extractPresetReportResponse
import com.swiftsku.swiftpos.data.model.extractShiftReportResponse
import com.swiftsku.swiftpos.data.type.ReportType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.ReportApolloClient
import com.swiftsku.swiftpos.payment.HistoricalBatchReportsQuery
import com.swiftsku.swiftpos.payment.HistoricalShiftReportsQuery
import com.swiftsku.swiftpos.payment.InitCbsSyncMutation
import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

class GraphQLReportRepository @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    @ReportApolloClient private val apolloClient: ApolloClient,
    private val json: Json
) : ReportRepository {
    override suspend fun getEODReport(storeCode: String, date: String): EODReport? {
        return null
    }

    override suspend fun getEOMReport(storeCode: String, date: String): EOMReport? {
        return null

    }

    override suspend fun getEOYReport(storeCode: String, year: Int): EOYReport? =
        withContext(dispatcher) {
            try {
                val response = apolloClient.query(
                    SalesSummaryReportQuery(
                        year = Optional.present(year),
                        storeCode = storeCode,
                    )
                ).execute()

                val eoyReport = response.data?.extractEOYResponse()

                eoyReport
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }

    override suspend fun getAdHocReport(
        storeCode: String,
        startDate: String,
        endDate: String
    ): AdHocReport? = withContext(dispatcher) {
        try {
            val response = apolloClient.query(
                SalesSummaryReportQuery(
                    startStamp = Optional.present(startDate),
                    endStamp = Optional.present(endDate),
                    storeCode = storeCode,
                )
            ).execute()

            val adHocReport = response.data?.extractAdHocReportResponse()
            adHocReport
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }

    override suspend fun getShiftReport(
        storeCode: String,
        terminalId: String,
        drawerAmount: Float?
    ): ShiftReport? =
        withContext(dispatcher) {
            try {
                val amount =
                    if (drawerAmount != null) Optional.present(drawerAmount.toDouble()) else Optional.absent()
                val response = apolloClient.query(
                    SalesSummaryReportQuery(
                        terminal = Optional.present(terminalId),
                        storeCode = storeCode,
                        drawerAmount = amount
                    )
                ).execute()

                val adHocReport = response.data?.extractShiftReportResponse()
                adHocReport
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }

    override suspend fun getPresetReport(storeCode: String, preset: String): PresetReport? =
        withContext(dispatcher) {
            try {
                val response = apolloClient.query(
                    SalesSummaryReportQuery(
                        preset = Optional.present(preset),
                        storeCode = storeCode,
                    )
                ).execute()

                response.data?.extractPresetReportResponse()
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }

    override suspend fun getHistoricalShiftReport(storeCode: String, shiftId: String): HistoricalShiftReport? =
        withContext(dispatcher) {
            try {
                val response = apolloClient.query(
                    SalesSummaryReportQuery(
                        shiftId = Optional.present(shiftId),
                        storeCode = storeCode,
                    )
                ).execute()

                response.data?.extractHistoricalShiftReportResponse()
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }

    override suspend fun getHistoricalShiftReportList(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?,
        cashierId: String?
    ): HistoricalShiftReportList? = withContext(dispatcher) {
        try {
            val response = apolloClient.query(
                HistoricalShiftReportsQuery(
                    storeCode = storeCode,
                    startDate = Optional.present(startDate),
                    endDate = Optional.present(endDate),
                    terminalId = Optional.present(terminal),
                    cashierId = Optional.present(cashierId)
                )
            ).execute()

            response.data?.extractHistoricalShiftReportListResponse()
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }

    override suspend fun getHistoricalBatchReport(
        storeCode: String,
        batchId: String
    ): HistoricalBatchReport? = withContext(dispatcher) {
        try {
            val response = apolloClient.query(
                SalesSummaryReportQuery(
                    batchId = Optional.present(batchId),
                    storeCode = storeCode,
                )
            ).execute()
            response.data?.extractHistoricalBatchReportResponse()
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }

    override suspend fun getHistoricalBatchReportList(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?
    ): HistoricalBatchReportList? = withContext(dispatcher) {
        try {
            val response = apolloClient.query(
                HistoricalBatchReportsQuery(
                    storeCode = storeCode,
                    startDate = Optional.present(startDate),
                    endDate = Optional.present(endDate),
                    terminalId = Optional.present(terminal)
                )
            ).execute()
            response.data?.extractHistoricalBatchReportListResponse()
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }

    override suspend fun initCbSync(
        docIds: List<String>,
        scopeCollection: String
    ): List<CbsSyncResponse> = withContext(dispatcher) {
        val result = mutableListOf<CbsSyncResponse>()
        try {
            val response = apolloClient.mutation(
                InitCbsSyncMutation(
                    docids = docIds,
                    scopeCollection = scopeCollection
                )
            ).execute()

            val syncResponse = response.data?.extractCbsSyncResponse()

            if (syncResponse != null) {
                result.addAll(syncResponse)
            }
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
        result

    }
}