package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.LoyaltyAccountStatus
import kotlinx.serialization.Serializable

@Serializable
data class LoyaltyAccountInfo(
    val loyaltyId: String
)

data class LoyaltyState(
    val loyaltyId: String? = null,
    val status: LoyaltyAccountStatus? = null
)


fun LoyaltyState.title(): String {
    if (status == LoyaltyAccountStatus.Calculating) {
        return "Calculating loyalty discount"
    }

    if (status == LoyaltyAccountStatus.WaitingForInput) {
        return "Waiting for customer’s input"
    }

    if (loyaltyId != null) {
        return "Loyalty ID"
    }
    return ""
}

fun LoyaltyState.maskedId(): String {
    if (loyaltyId != null && loyaltyId.length > 4) {
        return "xxxx" + loyaltyId.substring(loyaltyId.length - 4)
    }
    return ""
}