package com.swiftsku.swiftpos.data.couchbase.payment

import com.couchbase.lite.Collection
import com.couchbase.lite.MutableDocument
import com.pax.poslinkadmin.constant.TransactionType
import com.pax.poslinksemiintegration.constant.EbtCountType
import com.swiftsku.swiftpos.data.model.PaymentRecord
import com.swiftsku.swiftpos.data.model.RecordData
import com.swiftsku.swiftpos.data.model.RecordDetails
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PaymentRecordsCollection
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditRequestDto
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditResponseDto
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import java.util.Date
import javax.inject.Inject


class CbPayRecordsRepository @Inject constructor(
    @PaymentRecordsCollection private val collection: Collection,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val json: Json
) {
    /**
     * Payload is the start of the request, so there won't be any existing data.
     */
    suspend fun savePayloadInPaymentRecord(
        docId: String,
        storeCode: String,
        creditRequestDto: DoCreditRequestDto
    ): Boolean {
        val recordDetails = RecordDetails(payload = creditRequestDto, null)
        val recordDetailsMap = mutableMapOf("${creditRequestDto.epoch}" to recordDetails)
        // recordDataNew = gen/ebt -> epoch -> payload/res
        val ebtType = creditRequestDto.accountInformation.ebtType
        val recordDataNew = if (ebtType != null) {
            RecordData(null, recordDetailsMap)
        } else {
            RecordData(recordDetailsMap, null)
        }
        var paymentRecord: PaymentRecord? = null
        val doc = collection.getDocument(docId)
        if (doc != null) {
            try {
                val txnMap =
                    doc.toJSON()?.let { json.decodeFromString<JsonObject>(it).jsonObject }
                paymentRecord = json.decodeFromString<PaymentRecord>(txnMap.toString())
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
        } else {
            paymentRecord = when (creditRequestDto.transactionType) {
                TransactionType.SALE, TransactionType.POST_AUTHORIZATION -> {
                    PaymentRecord(recordDataNew, null, creditRequestDto.epoch)
                }

                TransactionType.RETURN -> {
                    PaymentRecord(null, recordDataNew, creditRequestDto.epoch)
                }

                TransactionType.AUTHORIZATION -> {
                    PaymentRecord(null, null, creditRequestDto.epoch, recordDataNew)
                }

                TransactionType.INQUIRY -> {
                    PaymentRecord(null, null, creditRequestDto.epoch, null, recordDataNew)
                }

                else -> null
            }
        }
        paymentRecord?.let {
            // payment/refund/auth -> gen/ebt -> epoch -> payload/res
            when (creditRequestDto.transactionType) {
                TransactionType.SALE, TransactionType.POST_AUTHORIZATION -> {
                    // payment key
                    it.payment?.let {
                        updateRecordData(
                            existingRecordData = it,
                            ebtType = ebtType,
                            epoch = creditRequestDto.epoch,
                            recordDetailsMap,
                            recordDetails
                        )
                    } ?: run {
                        it.payment = recordDataNew
                    }
                }

                TransactionType.RETURN -> {
                    // refund key
                    it.refund?.let {
                        updateRecordData(
                            existingRecordData = it,
                            ebtType = ebtType,
                            epoch = creditRequestDto.epoch,
                            recordDetailsMap,
                            recordDetails
                        )
                    } ?: run {
                        it.refund = recordDataNew
                    }
                }

                TransactionType.AUTHORIZATION -> {
                    // auth key
                    it.auth?.let {
                        updateRecordData(
                            existingRecordData = it,
                            ebtType = ebtType,
                            epoch = creditRequestDto.epoch,
                            recordDetailsMap,
                            recordDetails
                        )
                    } ?: run {
                        it.auth = recordDataNew
                    }
                }

                TransactionType.INQUIRY -> {
                    // inquiry key
                    it.inquiry?.let {
                        updateRecordData(
                            existingRecordData = it,
                            ebtType = ebtType,
                            epoch = creditRequestDto.epoch,
                            recordDetailsMap,
                            recordDetails
                        )
                    } ?: run {
                        it.inquiry = recordDataNew
                    }
                }

                else -> {}
            }

            it.lastModStamp = Date().epochInSeconds()
            return saveRecord(docId, storeCode, it)
        }
        return false
    }

    suspend fun saveResInPaymentRecord(
        docId: String,
        storeCode: String,
        creditRequestDto: DoCreditRequestDto,
        responseDto: DoCreditResponseDto?,
        errorResult: Result.Error?
    ): Boolean {
        var paymentRecord: PaymentRecord? = null
        val doc = collection.getDocument(docId)
        if (doc != null) {
            try {
                val txnMap =
                    doc.toJSON()?.let { json.decodeFromString<JsonObject>(it).jsonObject }
                paymentRecord = json.decodeFromString<PaymentRecord>(txnMap.toString())
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
        }
        paymentRecord?.let {
            val recordData = when (creditRequestDto.transactionType) {
                TransactionType.SALE, TransactionType.POST_AUTHORIZATION -> it.payment

                TransactionType.RETURN -> it.refund

                TransactionType.AUTHORIZATION -> it.auth

                TransactionType.INQUIRY -> it.inquiry

                else -> null
            }
            // recordData should always be there.
            recordData?.let { data ->
                if (creditRequestDto.accountInformation.ebtType != null) {
                    data.ebt?.get("${creditRequestDto.epoch}")?.let { recordDetails ->
                        responseDto?.let { resDto ->
                            recordDetails.res = resDto
                        } ?: run {
                            recordDetails.res = DoCreditResponseDto(
                                errorMessage = errorResult?.errorMessage,
                                errorCode = errorResult?.errorCode
                            )
                        }
                    }
                } else {
                    data.gen?.get("${creditRequestDto.epoch}")?.let { recordDetails ->
                        responseDto?.let { resDto ->
                            recordDetails.res = resDto
                        } ?: run {
                            recordDetails.res = DoCreditResponseDto(
                                errorMessage = errorResult?.errorMessage,
                                errorCode = errorResult?.errorCode
                            )
                        }
                    }
                }

                it.lastModStamp = Date().epochInSeconds()
                it.originalReferenceNumber =
                    responseDto?.traceInformation?.referenceNumber
                return saveRecord(docId, storeCode, it)
            }
        }
        return false
    }

    private suspend fun saveRecord(
        docId: String,
        storeCode: String,
        records: PaymentRecord
    ): Boolean {
        return withContext(dispatcher) {
            try {
                val existingDoc = collection.getDocument(docId)
                val encodeToString = json.encodeToString(records)
                if (existingDoc == null) {
                    val document = MutableDocument(docId, encodeToString)
                    document.setString("doc_type", "payment_record")
                    document.setString("store_code", storeCode)
                    collection.save(document)
                } else {
                    val newDocument = MutableDocument(docId, encodeToString)
                    val newDocMap = newDocument.toMap().toMutableMap()
                    val newDoc = existingDoc.toMutable().setData(newDocMap)
                    newDoc.setString("doc_type", "payment_record")
                    newDoc.setString("store_code", storeCode)
                    collection.save(newDoc)
                }
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    private fun updateRecordData(
        existingRecordData: RecordData,
        ebtType: EbtCountType?,
        epoch: Int,
        recordDetailsMap: MutableMap<String, RecordDetails>,
        recordDetails: RecordDetails
    ) {
        if (ebtType != null) {
            existingRecordData.ebt?.let {
                it["$epoch"] = recordDetails
            } ?: run {
                existingRecordData.ebt = recordDetailsMap
            }
        } else {
            existingRecordData.gen?.let {
                it["$epoch"] = recordDetails
            } ?: run {
                existingRecordData.gen = recordDetailsMap
            }
        }
    }
}