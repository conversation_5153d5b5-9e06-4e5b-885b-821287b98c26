package com.swiftsku.swiftpos.data.model

import com.fdc.core.types.TranTypeEnum
import java.math.BigDecimal
import java.math.BigInteger

//<CurrentAmount>43.33</CurrentAmount>
//<CurrentVolume>21.77</CurrentVolume>
//<CurrentUnitPrice>1.99</CurrentUnitPrice>
//<CurrentNozzle>1</CurrentNozzle>
//<ReleaseToken></ReleaseToken>
//<AuthorisationApplicationSender>POSsell</AuthorisationApplicationSender>
//<POSTransData>
//<TranType>Prepay</TranType>
//<POSTransactionData>TranNo = "123"</POSTransactionData>
//<EPSSTAN>123</EPSSTAN>
//FDC_Part3-70_ImplementationGuide Page 50 of 92
//Copyright © IFSF, CONEXXUS, INC., 2022, All Rights Reserved February 9, 2022
//<MerchandiseTrxAmount>3.54</MerchandiseTrxAmount>
//</POSTransData>
data class FuelingStatus(
    val currentAmount: BigDecimal? = null,
    val currentVolume: BigDecimal? = null,
    val currentUnitPrice: BigDecimal? = null,
    val currentNozzle: String? = null,
    val releaseToken: String? = null,
    val authorisationApplicationSender: String,
    val postTransactionData: POSTransactionData,
)

data class POSTransactionData(
    val tranType: TranTypeEnum? = null,
    val posTransactionData: String? = null,
    val epsStan: BigInteger? = null,
    val merchandiseTrxAmount: BigDecimal? = null
)