package com.swiftsku.swiftpos.data.couchbase.gupc.correction

import com.couchbase.lite.Collection
import com.couchbase.lite.MutableDocument
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.data.model.ReportMissingItem
import com.swiftsku.swiftpos.data.model.ReportWrongItem
import com.swiftsku.swiftpos.di.qualifiers.GUPCReportCollection
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.json.jsonObject
import javax.inject.Inject

class CouchbaseGUPCCorrectionRepository @Inject constructor(
    @GUPCReportCollection private val collection: Collection,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val json: Json
) : GUPCCorrectionRepository {


    override suspend fun reportMissingItem(item: ReportMissingItem): Boolean {
        return withContext(dispatcher) {
            val documentId = "gupc-missing-${item.upc}"

            try {
                val document = collection.getDocument(documentId)
                if (document != null) {
                    val jsonString = json.encodeToJsonElement(item)
                    val firstRequested = jsonString.jsonObject["first_requested"]?.toString()
                    firstRequested?.let {
                        collection.save(document.toMutable().apply {
                            setString("last_requested", it)
                        })
                    }
                    Logger.d("Reported missing item: ${item.upc}")
                    return@withContext true
                }
                collection.save(MutableDocument(documentId, json.encodeToString(item)))

                Logger.d("Reported missing item: ${item.upc}")
                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }

    override suspend fun reportWrongItem(item: ReportWrongItem): Boolean {
        return withContext(dispatcher) {
            val documentId = "gupc-error-${item.current.pluId}"

            try {
                collection.save(MutableDocument(documentId, json.encodeToString(item)))
                Logger.d("Reported wrong item: $item")

                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }

        }
    }


}