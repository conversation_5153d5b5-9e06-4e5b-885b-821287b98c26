package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.RefundProcessorMutation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class RefundResponse(
    val err: Boolean? = false,
    val note: String? = null,
    @SerialName("eventid")
    val eventId: String? = null,
    val gen: GenRefundInfo? = null,
    val ebt: EbtRefundInfo? = null
)

@Serializable
data class EbtRefundInfo(
    val ok: Boolean? = false,
    val msg: String? = null,
    val ecode: String? = null
)

@Serializable
data class GenRefundInfo(
    val ok: Boolean? = false,
    val msg: String? = null,
    val ecode: String? = null
)

fun RefundProcessorMutation.ResponseMessage.extractResponse(): RefundResponse {
    return RefundResponse(
        err = this.err,
        note = this.note,
        eventId = this.eventid,
        gen = GenRefundInfo(
            ok = this.gen?.ok,
            msg = this.gen?.msg,
            ecode = this.gen?.ecode
        ),
        ebt = EbtRefundInfo(
            ok = this.ebt?.ok,
            msg = this.ebt?.msg,
            ecode = this.ebt?.ecode
        )
    )
}