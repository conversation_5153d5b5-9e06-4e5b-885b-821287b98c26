package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.RecordAction
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Serializable
data class MMTDetail(
    @Serializable(with = CustomDateSerializer::class)
    val endDate: Date,
    val endTime: String,
    val itemListID: String,
    val mixMatchDescription: String,
    val mixMatchEntry: List<MixMatchEntry> = emptyList(),
    val promotion: Promotion,
    val recordAction: RecordAction? = null,
    @Serializable(with = CustomDateSerializer::class)
    val startDate: Date,
    val startTime: String,
    val weekdayAvailability: List<WeekdayAvailability> = emptyList(),
)

object CustomDateSerializer : KSerializer<Date> {
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)

    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("Date", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: Date) {
        encoder.encodeString(dateFormat.format(value))
    }

    override fun deserialize(decoder: Decoder): Date {
        val dateString = decoder.decodeString()
        return dateFormat.parse(dateString)!!
    }
}
