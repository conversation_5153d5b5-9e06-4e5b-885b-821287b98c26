package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.KSerializer

@Serializable(with = PluFormatTypeSerializer::class)
sealed class PluFormatType(val format: String) {
    object UPCa : PluFormatType("UPCA")
    object UPCe : PluFormatType("UPCE")
    object Ean8 : PluFormatType("EAN8")
    object Ean13 : PluFormatType("EAN13")
    object Plu : PluFormatType("PLU")
    object Ean14 : PluFormatType("EAN14")
    object Rss14 : PluFormatType("RSS14")
    object Gtin : PluFormatType("GTIN")
    object None : PluFormatType("NONE")

    override fun toString(): String {
        return format
    }
}

object PluFormatTypeSerializer : KSerializer<PluFormatType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("PluFormatTypeType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: PluFormatType) {
        encoder.encodeString(value.format)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): PluFormatType {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            "UPCA" -> PluFormatType.UPCa
            "UPCE" -> PluFormatType.UPCe
            "EAN8" -> PluFormatType.Ean8
            "EAN13" -> PluFormatType.Ean13
            "PLU" -> PluFormatType.Plu
            "EAN14" -> PluFormatType.Ean14
            "RSS14" -> PluFormatType.Rss14
            "NONE" -> PluFormatType.None
            "GTIN" -> PluFormatType.Gtin
            else -> throw IllegalArgumentException("Unknown PosCodeFormatType: $format")
        }
    }
}
