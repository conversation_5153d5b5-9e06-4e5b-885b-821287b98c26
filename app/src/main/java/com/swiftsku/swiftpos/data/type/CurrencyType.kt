package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.Serializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = CurrencyTypeSerializer::class)
sealed class CurrencyType(val value: String) {
    object Usd : CurrencyType("USD")
    class ToCurrencyType(value: String) : CurrencyType(value)
}

object CurrencyTypeSerializer : KSerializer<CurrencyType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("CurrencyType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: CurrencyType) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): CurrencyType {
        val value  = decoder.decodeString()
        return when(value) {
            "USD" -> CurrencyType.Usd
            else -> CurrencyType.ToCurrencyType(value)
        }
    }
}