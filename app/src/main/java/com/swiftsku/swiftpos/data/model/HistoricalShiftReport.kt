package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.HistoricalShiftReportsQuery
import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import kotlinx.serialization.Serializable

@Serializable
data class HistoricalShiftReport(
    override val storeCode: String,
    override val stats: List<ReportDetail>
) : SalesSummaryData()


fun SalesSummaryReportQuery.Data.extractHistoricalShiftReportResponse(): HistoricalShiftReport? {
    val salesSummaryData = this.salesSummaryData
    if (salesSummaryData != null) {
        return HistoricalShiftReport(
            stats = salesSummaryData.stats?.filterNotNull()?.map { stat ->
                ReportDetail(
                    stat.terminalId,
                    stat.terminalData
                )
            } ?: listOf(),
            storeCode = salesSummaryData.storeCode
        )
    }
    return null
}

@Serializable
data class HistoricalShiftReportDetail(
    val shiftId: String?,
    val terminal: String?,
    val cashierId: String?,
    val shiftStart: String?,
    val shiftEnd: String?,
)

@Serializable
data class HistoricalShiftReportList(
    val data: List<HistoricalShiftReportDetail>,
    val terminals: List<String> = listOf("All Terminals"),
)

fun HistoricalShiftReportsQuery.Data.extractHistoricalShiftReportListResponse(): HistoricalShiftReportList? {
    val historicalShifts = this.historicalShifts
    if (historicalShifts != null) {
        return HistoricalShiftReportList(
            data = historicalShifts.data?.map { shift ->
                HistoricalShiftReportDetail(
                    shift?.shiftId,
                    shift?.terminal,
                    shift?.cashierId,
                    shift?.shiftStart,
                    shift?.shiftEnd,
                )
            } ?: listOf(),
            terminals = historicalShifts.terminals?.filterNotNull() ?: listOf("All Terminals")
        )
    }
    return null
}
