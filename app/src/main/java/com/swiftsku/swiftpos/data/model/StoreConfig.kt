package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.TransactionType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreConfig(
    @SerialName("posid")
    val posId: String,
    val token: String,
    @SerialName("store_code")
    val storeCode: String,
    val active: Boolean = false,
    val mid: String,
    val posNumber: String,
    @SerialName("store_id")
    val storeId: String,
    val receiptInfo: ReceiptInfo,
    @SerialName("enable_consumer_screen")
    val displayConsumerScreen: Boolean = true,
    @SerialName(Keys.PRINT_TXN_ID_BARCODE)
    val printTxnIdBarcode: Boolean? = false,
    @SerialName(Keys.PRINT_AFTER_CREATION)
    val printAfterCreation: List<TransactionType>? = emptyList(),
    @SerialName(Keys.PLU_ITEMS_ORDER)
    val pluOrder: List<String> = emptyList(),
    @SerialName(Keys.PINPAD_CONFIG)
    val pinpadConfig: PinpadConfig? = null,
    @SerialName(Keys.HIDE_BUTTONS)
    val hideButtons: List<String> = emptyList(),
    @SerialName(Keys.PINPAD_IDLE_IMAGE_SET)
    val pinpadIdleImageSet: Boolean = false,
    @SerialName(Keys.NO_REPORT_PRINT_ON_LOGOUT)
    val noReportPrintOnLogout: Boolean? = false
) {
    object Keys {
        const val PLU_ITEMS_ORDER = "plu_order"
        const val BATCH_ID = "batch_id"
        const val PRINT_AFTER_CREATION = "print_after_creation"
        const val PRINT_TXN_ID_BARCODE = "print_txn_id_barcode"
        const val PINPAD_CONFIG = "pinpad_config"
        const val HIDE_BUTTONS = "hide_buttons"
        const val PINPAD_IDLE_IMAGE_SET = "pinpadIdleImageSet"
        const val NO_REPORT_PRINT_ON_LOGOUT = "noReportPrintOnLogout"
    }
}

@Serializable
data class ReceiptInfo(
    val printCashReceipt: Boolean = true,
    val printCardReceipt: Boolean = true,
    val footerNote: String,
    val storeName: String,
    val streetAddress: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val phoneNumber: String
)