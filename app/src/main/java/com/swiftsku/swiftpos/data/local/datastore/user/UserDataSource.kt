package com.swiftsku.swiftpos.data.local.datastore.user

import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import kotlinx.coroutines.flow.StateFlow


interface UserDataSource {

    /**
     * Emits the current user, or null if no user is signed in.
     * Initial value is null; updates occur on login, logout, or any user data change.
     */
    val currentUserFlow: StateFlow<UserDTO?>

    suspend fun saveUser(user: UserDTO): Boolean

    suspend fun getCurrentUser(): UserDTO?

    suspend fun clearUser(): Boolean

    suspend fun isUserLoggedIn(): <PERSON><PERSON><PERSON>
}