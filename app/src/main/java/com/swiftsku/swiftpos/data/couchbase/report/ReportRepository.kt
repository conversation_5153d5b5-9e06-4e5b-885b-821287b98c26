package com.swiftsku.swiftpos.data.couchbase.report

import com.swiftsku.swiftpos.data.dtos.couchbase.CbsSyncResponse
import com.swiftsku.swiftpos.data.model.AdHocReport
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EOMReport
import com.swiftsku.swiftpos.data.model.EOYReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReportList
import com.swiftsku.swiftpos.data.model.HistoricalShiftReport
import com.swiftsku.swiftpos.data.model.HistoricalShiftReportList
import com.swiftsku.swiftpos.data.model.PresetReport
import com.swiftsku.swiftpos.data.model.ShiftReport

interface ReportRepository {
    suspend fun getEODReport(storeCode: String, date: String): EODReport?

    suspend fun getEOMReport(storeCode: String, date: String): EOMReport?

    suspend fun getEOYReport(storeCode: String, year: Int): EOYReport?

    suspend fun getAdHocReport(storeCode: String, startDate: String, endDate: String): AdHocReport?
    suspend fun getShiftReport(
        storeCode: String,
        terminalId: String,
        drawerAmount: Float? = null
    ): ShiftReport?

    suspend fun getPresetReport(storeCode: String, preset: String): PresetReport?

    suspend fun getHistoricalShiftReport(storeCode: String, shiftId: String): HistoricalShiftReport?

    suspend fun getHistoricalShiftReportList(
        storeCode: String,
        startDate: String? = null,
        endDate: String? = null,
        terminal: String? = null,
        cashierId: String? = null,
    ): HistoricalShiftReportList?

    suspend fun getHistoricalBatchReport(storeCode: String, batchId: String): HistoricalBatchReport?

    suspend fun getHistoricalBatchReportList(
        storeCode: String,
        startDate: String? = null,
        endDate: String? = null,
        terminal: String? = null
    ): HistoricalBatchReportList?

    suspend fun initCbSync(
        docIds: List<String>,
        scopeCollection: String
    ): List<CbsSyncResponse>

}