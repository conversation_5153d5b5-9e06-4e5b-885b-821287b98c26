package com.swiftsku.swiftpos.data.couchbase.promotion

import com.couchbase.lite.Collection
import com.swiftsku.swiftpos.data.model.ILTDetail
import com.swiftsku.swiftpos.data.model.MMTDetail
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PromotionsCollection
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import java.util.Date
import javax.inject.Inject

class CouchbasePromotionsRepository @Inject constructor(
    @PromotionsCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : PromotionsRepository {
    override suspend fun getMMTDetails(
        startDate: Date,
        endDate: Date,
        storeId: String
    ): List<MMTDetail> {
        return withContext(coroutineDispatcher) {
            val mmtDetailList = mutableListOf<MMTDetail>()
            try {
                val promoDoc = collection.getDocument(storeId)?.toJSON()
                if (promoDoc != null) {
                    val promoMap =
                        json.decodeFromString<JsonObject>(promoDoc)["promotions"]?.jsonObject
                    if (promoMap != null) {

                        for (key in promoMap.keys) {
                            val mmtMap = promoMap[key]?.jsonObject?.get("MMTDetail")?.jsonObject
                            val mmtDetail = json.decodeFromString<MMTDetail>(mmtMap.toString())
                            if (mmtDetail.startDate.time <= startDate.time && mmtDetail.endDate.time >= endDate.time) {
                                mmtDetailList.add(mmtDetail)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
            return@withContext mmtDetailList
        }
    }

    override suspend fun getILTDetails(
        startDate: Date,
        endDate: Date,
        storeId: String
    ): List<ILTDetail> {
        return withContext(coroutineDispatcher) {
            val iltDetailMutableList = mutableListOf<ILTDetail>()

            try {
                val promoDoc = collection.getDocument(storeId)?.toJSON()
                if (promoDoc != null) {

                    val promoMap =
                        json.decodeFromString<JsonObject>(promoDoc)["promotions"]?.jsonObject

                    if (promoMap != null) {
                        for (key in promoMap.keys) {
                            val iltMap = promoMap[key]?.jsonObject?.get("ILTDetail")?.jsonObject
                            val iltDetail = json.decodeFromString<ILTDetail>(iltMap.toString())
                            iltDetailMutableList.add(iltDetail)
                        }
                    }
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
            return@withContext iltDetailMutableList
        }
    }
}