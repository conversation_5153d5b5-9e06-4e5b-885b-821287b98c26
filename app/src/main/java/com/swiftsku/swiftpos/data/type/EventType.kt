package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.Serializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = EventTypeSerializer::class)
sealed class EventType(val type: String) {
    object Login : EventType("LOGIN")
    object Logout : EventType("LOGOUT")
    object BatchClose : EventType("BATCH_CLOSE")
    object BatchOpen : EventType("BATCH_OPEN")
}

object EventTypeSerializer : KSerializer<EventType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("EventType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: EventType) {
        encoder.encodeString(value.type)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): EventType {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            EventType.Login.type -> EventType.Login
            EventType.Logout.type -> EventType.Logout
            EventType.BatchOpen.type -> EventType.BatchOpen
            EventType.BatchClose.type -> EventType.BatchClose
            else -> throw IllegalArgumentException("Unknown EventType: $format")
        }
    }
}