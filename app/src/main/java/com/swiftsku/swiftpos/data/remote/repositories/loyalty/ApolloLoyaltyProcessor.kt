package com.swiftsku.swiftpos.data.remote.repositories.loyalty

import com.apollographql.apollo3.ApolloClient
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.data.model.LoyaltyRewards
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.extractResponse
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LoyaltyApolloClient
import com.swiftsku.swiftpos.payment.GetRewardsQuery
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject

class ApolloLoyaltyRepository @Inject constructor(
    @LoyaltyApolloClient private val apolloClient: ApolloClient,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : LoyaltyRepository {
    override suspend fun getLoyaltyRewards(
        storeCode: String,
        transaction: Transaction,
        loyaltyAccountId: String
    ): Result<LoyaltyRewards> {
        return withContext(coroutineDispatcher) {
            try {
                val response = apolloClient.query(
                    GetRewardsQuery(
                        storeCode,
                        json.encodeToString(transaction),
                        loyaltyAccountId
                    )
                ).execute()

                Logger.d("Response ${response.data} , ${response.errors}")

                if (response.hasErrors()) {
                    (
                            Result.Error(response.errors
                                ?.map { error -> error.message }
                                ?.reduce { acc, s -> acc + s }
                                ?: "Get Loyalty Rewards Failed"))
                } else {
                    val loyaltyRewards = response.data?.extractResponse()

                    if (loyaltyRewards == null || !loyaltyRewards.status) {
                        (Result.Error("Get Loyalty Rewards Failed"))
                    } else {
                        (Result.Success(loyaltyRewards.data))
                    }
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
                (Result.Error(e.message ?: "Get Loyalty Rewards Failed"))
            }
        }
    }
}
