package com.swiftsku.swiftpos.data.remote.repositories.loyalty

import com.swiftsku.swiftpos.data.model.LoyaltyRewards
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.utils.Result

interface LoyaltyRepository {
    suspend fun getLoyaltyRewards(
        storeCode: String,
        transaction: Transaction,
        loyaltyAccountId: String
    ): Result<LoyaltyRewards>
}