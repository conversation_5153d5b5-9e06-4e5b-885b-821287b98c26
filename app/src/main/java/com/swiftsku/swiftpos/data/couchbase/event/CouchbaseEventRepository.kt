package com.swiftsku.swiftpos.data.couchbase.event

import com.couchbase.lite.Collection
import com.couchbase.lite.MutableDocument
import com.swiftsku.swiftpos.data.model.Event
import com.swiftsku.swiftpos.di.qualifiers.EventCollection
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject

class CouchbaseEventRepository @Inject constructor(
    @EventCollection private val collection: Collection,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val jsonEncoder: Json,
) : EventRepository {

    override suspend fun logIn(event: Event): Boolean {
        return withContext(dispatcher) {
            try {
                collection.save(MutableDocument(event.eventId, jsonEncoder.encodeToString(event)))
                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }

    override suspend fun logOut(event: Event): Boolean {
        return withContext(dispatcher) {
            try {
                collection.save(MutableDocument(event.eventId, jsonEncoder.encodeToString(event)))
                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }

    override suspend fun saveEvent(event: Event): Boolean {
        return withContext(dispatcher) {
            try {
                collection.save(MutableDocument(event.eventId, jsonEncoder.encodeToString(event)))
                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }
}