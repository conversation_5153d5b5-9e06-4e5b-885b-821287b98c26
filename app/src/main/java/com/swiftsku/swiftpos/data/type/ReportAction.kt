package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder


@Serializable(with = ReportActionSerializer::class)
sealed class ReportAction(val action: String) {
    object Missing : ReportAction("MISSING")
    object Correction : ReportAction("CORRECTION")
}

object ReportActionSerializer : KSerializer<ReportAction> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("ReportAction", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ReportAction) {
        encoder.encodeString(value.action)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): ReportAction {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            ReportAction.Correction.action -> ReportAction.Correction
            ReportAction.Missing.action -> ReportAction.Missing
            else -> throw IllegalArgumentException("Unknown ReportAction: $format")
        }
    }
}