package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.EventType
import com.swiftsku.swiftpos.domain.payment.dto.BatchCloseResponseDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class Event(
    val eventId: String,
    val event: EventType, // [LOGIN, LOGOUT]
    @SerialName("is_integrated")
    val isIntegrated: Boolean = false, // Should always be sent as false by pos-app
    @Serializable(with = DateSerializer::class)
    val eventStartTime: Date?,  // cash drawer open time
    @Serializable(with = DateSerializer::class)
    val eventEndTime: Date?,   // cash drawer close time
    val eventData: EventData,  // User always input in cents
    val cashierId: String,
    @SerialName("store_code")
    val storeCode: String,
    @SerialName("doc_type")
    val docType: String = "event_item",
    @SerialName("event_type")
    val eventType: String = "user-session"
)

@Serializable
sealed class EventData

@Serializable
data class LoginEventData(val cashBalance: Float) : EventData()

@Serializable
data class LogOutEventData(val cashBalance: Float) : EventData()

@Serializable
data class BatchCloseEventData(val batchId: String, val res: BatchCloseResponseDto) : EventData()

@Serializable
data class BatchOpenEventData(val batchId: String) : EventData()