package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class CreditAccountDoc(
    @SerialName("doc_type")
    val docType: String = "accounts_item",
    @SerialName("store_code")
    val storeCode: String,
    val creditAccounts: Map<String, CreditAccount> = emptyMap()
)

@Serializable
data class CreditAccount(
    val id: String,
    val name: String,
    val phone: String,
    val creditLimitCents: Int,
    val createdAt: Long,
    val updatedAt: Long,
    val address: String? = "",
    val currentOutstandingCents: Int
)