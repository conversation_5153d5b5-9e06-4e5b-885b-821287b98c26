package com.swiftsku.swiftpos.data.local.datastore.notification

import androidx.datastore.core.Serializer
import com.swiftsku.swiftpos.utils.EventUtils
import java.io.InputStream
import java.io.OutputStream


object NotificationsProtoSerializer : Serializer<NotificationsProto> {

    override val defaultValue: NotificationsProto = NotificationsProto.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): NotificationsProto {
        return try {
            NotificationsProto.parseFrom(input)
        } catch (e: Exception) {
            EventUtils.recordException(e)
            defaultValue
        }
    }

    override suspend fun writeTo(t: NotificationsProto, output: OutputStream) {
        t.writeTo(output)
    }
}
