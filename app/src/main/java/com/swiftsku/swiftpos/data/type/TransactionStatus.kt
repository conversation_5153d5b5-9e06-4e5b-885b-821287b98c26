package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = TransactionStatusSerializer::class)
sealed class TransactionStatus(val status: String) {
    object Pending : TransactionStatus("PENDING")
    object Complete : TransactionStatus("COMPLETE")
    object Current : TransactionStatus("CURRENT")
    object FuelAuthorized : TransactionStatus("FUEL_AUTHORIZED")
    object FuelDispensed : TransactionStatus("FUEL_DISPENSED")
    object PartiallyPaid : TransactionStatus("PARTIALLY_PAID")

    override fun toString(): String = status
}

enum class StatusReason {
    INITIAL, PAYMENT_FAILED
}

object TransactionStatusSerializer : KSerializer<TransactionStatus> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("TransactionStatus", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TransactionStatus) {
        encoder.encodeString(value.status)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): TransactionStatus {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            TransactionStatus.Pending.status -> TransactionStatus.Pending
            TransactionStatus.Complete.status -> TransactionStatus.Complete
            TransactionStatus.Current.status -> TransactionStatus.Current
            TransactionStatus.FuelAuthorized.status -> TransactionStatus.FuelAuthorized
            TransactionStatus.FuelDispensed.status -> TransactionStatus.FuelDispensed
            TransactionStatus.PartiallyPaid.status -> TransactionStatus.PartiallyPaid
            else -> throw IllegalArgumentException("Unknown TransactionStatus: $format")
        }
    }
}