package com.swiftsku.swiftpos.data.type


sealed class DateResult

sealed class ReportDateResult : DateResult() {
    object EODDate : ReportDateResult()
    object AdHocStartDate : ReportDateResult()
    object AdHocEndDate : ReportDateResult()
    object HistoricalShiftStartDate : ReportDateResult()
    object HistoricalShiftEndDate : ReportDateResult()
    object HistoricalBatchStartDate : ReportDateResult()
    object HistoricalBatchEndDate : ReportDateResult()
}


sealed class TimeResult

sealed class ReportTimeResult : TimeResult() {
    object AdHocStartTime : ReportTimeResult()
    object AdHocEndTime : ReportTimeResult()

}
