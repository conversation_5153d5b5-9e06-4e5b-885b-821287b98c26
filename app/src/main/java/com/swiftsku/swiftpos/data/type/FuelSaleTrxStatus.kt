package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = FuelSaleTrxStatusSerializer::class)
sealed class FuelSaleTrxStatus(val type: String) {
    object Completed : FuelSaleTrxStatus("COMPLETED")
    object Pending : FuelSaleTrxStatus("PENDING")

    override fun toString(): String = type
}


object FuelSaleTrxStatusSerializer : KSerializer<FuelSaleTrxStatus> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("FuelSaleTrxStatus", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: FuelSaleTrxStatus) {
        encoder.encodeString(value.type)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): FuelSaleTrxStatus {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            FuelSaleTrxStatus.Completed.type -> FuelSaleTrxStatus.Completed
            FuelSaleTrxStatus.Pending.type -> FuelSaleTrxStatus.Pending
            else -> throw IllegalArgumentException("Unknown EventType: $format")
        }
    }
}