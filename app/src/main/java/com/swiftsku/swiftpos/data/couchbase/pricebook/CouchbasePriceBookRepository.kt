package com.swiftsku.swiftpos.data.couchbase.pricebook

import com.couchbase.lite.Collection
import com.couchbase.lite.DataSource
import com.couchbase.lite.Expression
import com.couchbase.lite.IndexBuilder
import com.couchbase.lite.Meta
import com.couchbase.lite.MutableDocument
import com.couchbase.lite.QueryBuilder
import com.couchbase.lite.SelectResult
import com.couchbase.lite.ValueIndexItem
import com.couchbase.lite.queryChangeFlow
import com.swiftsku.swiftpos.data.model.PLUItemDTO
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.PluMap
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PriceBookCollection
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import javax.inject.Inject

class CouchbasePriceBookRepository @Inject constructor(
    @PriceBookCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : PriceBookRepository {
    override fun getPriceBookOnDashboardStream(): Flow<List<PluItem>> {
        val query = QueryBuilder
            .select(
                SelectResult.property("plumap")
            )
            .from(DataSource.collection(collection))
            .where(
                Expression.property("doc_type").equalTo(Expression.string("pricebook_item"))
                    .and(
                        Expression.property("plumap.000.showInDashboard")
                            .equalTo(Expression.booleanValue(true))
                    )
            )

        val flow = query.queryChangeFlow()
            .map { queryChange ->
                val priceBookList = mutableListOf<PluItem>()
                queryChange.results?.let { results ->

                    results.forEach { result ->
                        val pluMap =
                            json.decodeFromString<JsonObject>(result.toJSON())["plumap"]?.jsonObject
                        pluMap?.forEach { (key) ->
                            pluMap[key]?.toString()
                                ?.let {
                                    try {
                                        val pluItem =
                                            json.decodeFromString<PluItem>(it)
                                        priceBookList.add(pluItem)
                                    } catch (e: Exception) {
                                        EventUtils.recordException(e)
                                    }
                                }
                        }
                    }
                }
                priceBookList.toList()
            }
            .flowOn(Dispatchers.IO)

        return flow


    }


    override suspend fun deletePriceBook(pluItem: PluItem, storeId: String) {
        withContext(coroutineDispatcher) {
            collection.getDocument("$storeId-${pluItem.pluId}")?.let {
                collection.delete(it)
            }
        }
    }

    override suspend fun upsertPriceBook(pluItem: PluItem, storeId: String): Boolean {

        return withContext(coroutineDispatcher) {
            try {
                val pluMap = PluMap(
                    plumap = mapOf(pluItem.pluModifier to pluItem),
                )

                val docId = storeId + "-" + pluItem.pluId
                val priceBookJson = json.encodeToString(pluMap)

                val document = MutableDocument(
                    docId,
                    priceBookJson
                )
                document.setJSON(priceBookJson)
                document.setString("doc_type", "pricebook_item")
                document.setString("store_code", storeId)
                collection.save(document)
                return@withContext true

            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }

    }

    override suspend fun findByBarcode(barcode: String, storeId: String): List<PluItem> {
        return withContext(coroutineDispatcher) {
            val items = mutableListOf<PluItem>()
            try {
                val document = collection.getDocument("$storeId-$barcode")
                val jsonString = document?.toJSON() ?: return@withContext emptyList()
                val dto = json.decodeFromString<PLUItemDTO>(jsonString)
                dto.pluMap.forEach { (key, value) ->
                    items.add(value.copy(pluModifier = key))
                }

                items
            } catch (e: Exception) {
                EventUtils.recordException(e)
                items
            }

        }
    }

    override suspend fun getPriceBooks(
        storeCode: String,
        compositePluIds: List<String>
    ): List<PluItem> {
        // compositePluIds will be in storeCode-pluId-pluModifier format
        val docIds = compositePluIds.map {
            Expression.string(it.split("-").take(2).joinToString("-"))
        }.toTypedArray()
        val query = QueryBuilder
            .select(SelectResult.property("plumap"))
            .from(DataSource.collection(collection))
            .where(
                Meta.id.`in`(*docIds)
                    .and(Expression.property("store_code").equalTo(Expression.string(storeCode)))
            )
        val pluList = mutableListOf<PluItem>()
        query.execute().allResults().forEach { result ->
            val pluMap =
                json.decodeFromString<JsonObject>(result.toJSON())["plumap"]?.jsonObject
            pluMap?.forEach { (key) ->
                pluMap[key]?.toString()?.let {
                    try {
                        val pluItem = json.decodeFromString<PluItem>(it)
                        val compositeKey = "$storeCode-${pluItem.pluId}-${pluItem.pluModifier}"
                        if (compositePluIds.contains(compositeKey)) {
                            pluList.add(pluItem)
                        }
                    } catch (e: Exception) {
                        EventUtils.recordException(e)
                    }
                }
            }
        }
        return pluList
    }

    override fun getPriceBookStream(docIds: List<String>): Flow<List<PluItem>> {
        val expressions = docIds.map { Expression.string(it) }.toTypedArray()
        val query = QueryBuilder
            .select(SelectResult.property("plumap"))
            .from(DataSource.collection(collection))
            .where(Meta.id.`in`(*expressions))

        val flow = query.queryChangeFlow()
            .map { queryChange ->
                val priceBookList = mutableListOf<PluItem>()
                queryChange.results?.let { results ->
                    results.forEach { result ->
                        val pluMap =
                            json.decodeFromString<JsonObject>(result.toJSON())["plumap"]?.jsonObject
                        pluMap?.forEach { (key) ->
                            pluMap[key]?.toString()?.let {
                                try {
                                    val pluItem = json.decodeFromString<PluItem>(it)
                                    priceBookList.add(pluItem)
                                } catch (e: Exception) {
                                    EventUtils.recordException(e)
                                }
                            }
                        }
                    }
                }
                priceBookList.toList()
            }.flowOn(coroutineDispatcher).catch { EventUtils.recordException(it) }

        return flow
    }

    override suspend fun getPriceBookByDeptId(deptId: String): List<PluItem> {
        val expression =
            Expression.property("doc_type").equalTo(Expression.string("pricebook_item"))
                .and(
                    Expression.property("plumap.000.merchandiseCode")
                        .equalTo(Expression.string(deptId))
                )
        val query = QueryBuilder
            .select(SelectResult.property("plumap"))
            .from(DataSource.collection(collection))
            .where(expression)
        val pluList = mutableListOf<PluItem>()
        try {
            query.execute().allResults().forEach { result ->
                val pluMap =
                    json.decodeFromString<JsonObject>(result.toJSON())["plumap"]?.jsonObject
                pluMap?.forEach { (key) ->
                    pluMap[key]?.toString()?.let {
                        try {
                            val pluItem = json.decodeFromString<PluItem>(it)
                            pluList.add(pluItem)
                        } catch (e: Exception) {
                            EventUtils.recordException(e)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
        return pluList
    }

    override suspend fun getPluItem(storeCode: String, pluId: String, pluModifier: String): PluItem? {
        return withContext(coroutineDispatcher) {
            try {
                val document = collection.getDocument("$storeCode-$pluId")
                document?.toJSON()?.let {
                    val dto = json.decodeFromString<PLUItemDTO>(it)
                    return@withContext dto.pluMap[pluModifier]
                }
                return@withContext null
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext null
            }
        }
    }

    override suspend fun addIndexes() {
        val index = IndexBuilder.valueIndex(
            ValueIndexItem.property("doc_type"),
            ValueIndexItem.property("store_code")
        )
        collection.createIndex("idx_pricebook_store", index)
    }
}
