package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.FetchPricebookQuery
import kotlinx.serialization.Serializable


@Serializable
data class PricebookSearchResponse(
    val pluItems: List<PricebookSearchItem>? = null,
)

@Serializable
data class PricebookSearchItem(
    val pluId: String,
    val pluModifier: String,
    val description: String?,
    val price: Double = 0.0
)

fun FetchPricebookQuery.Pricebook.extractResponse(): PricebookSearchResponse {
    return PricebookSearchResponse(
        pluItems = this.data?.filterNotNull()?.map {
            PricebookSearchItem(
                it.pluId, it.pluModifier, it.description, it.price ?: 0.0
            )
        }
    )
}