package com.swiftsku.swiftpos.data.local.datastore.user.proto

import androidx.datastore.core.DataStore
import com.swiftsku.swiftpos.data.local.datastore.user.User.UserProto
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import com.swiftsku.swiftpos.data.local.datastore.user.dto.toDTO
import com.swiftsku.swiftpos.data.local.datastore.user.dto.toProto
import com.swiftsku.swiftpos.di.qualifiers.IOCoroutineScope
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


class UserProtoDataSource @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    @IOCoroutineScope private val appScope: CoroutineScope,
    private val dataStore: DataStore<UserProto>
) : UserDataSource {

    private val _currentUser = MutableStateFlow<UserDTO?>(null)
    override val currentUserFlow: StateFlow<UserDTO?> get() = _currentUser

    init {
        appScope.launch {
            dataStore.data
                .map { it.toDTO() }
                .catch { EventUtils.recordException(it) }
                .collect { userDto ->
                    _currentUser.emit(userDto)
                }
        }
    }

    override suspend fun saveUser(user: UserDTO): Boolean = withContext(dispatcher) {
        try {
            dataStore.updateData { user.toProto() }
            true
        } catch (e: Exception) {
            EventUtils.recordException(e)
            false
        }
    }

    override suspend fun getCurrentUser(): UserDTO? = withContext(dispatcher) {
        currentUserFlow.value
    }

    override suspend fun clearUser(): Boolean = withContext(dispatcher) {
        try {
            dataStore.updateData {
                it.toBuilder().clear().build()
            }
            true
        } catch (e: Exception) {
            EventUtils.recordException(e)
            false
        }
    }

    override suspend fun isUserLoggedIn(): Boolean = withContext(dispatcher) {
        currentUserFlow.value?.loggedIn.isTrue()
    }
}