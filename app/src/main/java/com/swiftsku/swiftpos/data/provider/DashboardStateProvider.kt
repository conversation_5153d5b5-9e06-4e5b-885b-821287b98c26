package com.swiftsku.swiftpos.data.provider

import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.domain.departments.UpdateDepartmentTxnItemsUseCase
import com.swiftsku.swiftpos.domain.departments.dto.DepartmentAdd
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DashboardStateProvider @Inject constructor(
    private val updateDepartmentTxnItemsUseCase: UpdateDepartmentTxnItemsUseCase
) {

    private val _txnItems = MutableStateFlow<List<TransactionItem>>(emptyList())

    val txnItems = _txnItems.asStateFlow()


    suspend fun updateTxnItems(txnItems: List<TransactionItem>) {
        _txnItems.emit(txnItems)
    }

    suspend fun addTxnItem(txnItem: TransactionItem) {
        _txnItems.emit(_txnItems.value + txnItem)
    }

    suspend fun addDepartmentTxnItem(
        txnItem: TransactionItemWithDepartment,
        storeCode: String,
        transactionSummary: TransactionSummary,
        taxMap: MutableMap<String, Tax>
    ) {
        _txnItems.emit(
            updateDepartmentTxnItemsUseCase(
                DepartmentAdd(txnItem, _txnItems.value, storeCode, transactionSummary, taxMap)
            )
        )
    }

}