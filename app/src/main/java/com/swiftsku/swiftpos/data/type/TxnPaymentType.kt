package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder


@Serializable(with = TxnPaymentTypeSerializer::class)
sealed class  TxnPaymentType(val type: String) {
    object Cash : TxnPaymentType("cash")
    object Card : TxnPaymentType("card")
    object EBT : TxnPaymentType("ebt")
    object Cheque : TxnPaymentType("check")
    object Credit : TxnPaymentType("credit")

    override fun toString(): String = type
}

object TxnPaymentTypeSerializer : KSerializer<TxnPaymentType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("TxnPaymentType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TxnPaymentType) {
        encoder.encodeString(value.type)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): TxnPaymentType {
        val format = decoder.decodeString()
        return when (format.lowercase()) {
            "cash" -> TxnPaymentType.Cash
            "card" -> TxnPaymentType.Card
            "ebt" -> TxnPaymentType.EBT
            "check" -> TxnPaymentType.Cheque
            "credit" -> TxnPaymentType.Credit

            else -> throw IllegalArgumentException("Unknown TxnPaymentType: $format")
        }
    }
}
