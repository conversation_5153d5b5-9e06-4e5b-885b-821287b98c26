package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.GetRewardsQuery

data class LoyaltyGetRewardsResponse(
    val status: Boolean = false,
    val msg: String,
    val data: LoyaltyRewards
)

data class LoyaltyRewards(
    val rewards: List<Reward>,
)

data class Reward(
    val transactionItemId: String?,
    val loyaltyAccountNumber: String?,
    val originalQuantity: Int?,
    val discountQuantity: Int?,
    val discount: Discount?,
)

data class Discount(
    val discountId: String?,
    val discountText: String?,
    val discountAmount: Double?,
)

fun GetRewardsQuery.Data.extractResponse(): LoyaltyGetRewardsResponse {
    val rewards = this.getRewards?.data?.rewards?.map {
        Reward(
            transactionItemId = it?.transactionItemId,
            loyaltyAccountNumber = it?.loyaltyAccountNumber,
            originalQuantity = it?.originalQuantity,
            discountQuantity = it?.discountQuantity,
            discount = Discount(
                discountId = it?.discount?.discountId,
                discountText = it?.discount?.discountText,
                discountAmount = it?.discount?.discountAmount
            )
        )
    } ?: emptyList()
    return LoyaltyGetRewardsResponse(
        status = this.getRewards?.status ?: false,
        msg = this.getRewards?.msg ?: "",
        data = LoyaltyRewards(rewards = rewards)
    )
}
