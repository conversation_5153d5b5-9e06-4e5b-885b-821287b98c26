package com.swiftsku.swiftpos.data.type

sealed class ReportType(val type: String) {

    object Shift : ReportType("Shift Report")
    object EOD : ReportType("EOD Report")
    object EOM : ReportType("EOM Report")
    object EOY : ReportType("EOY Report")
    object AdHoc : ReportType("Custom Report")
    object Preset : ReportType("Saved Report")
    object HistoricalShift : ReportType("Historical Shift Report")
    object HistoricalBatch : ReportType("Historical Batch Report")

    override fun toString(): String {
        return this.type
    }
}

val reportDefaults = listOf(
    ReportType.Shift,
    ReportType.EOD,
    ReportType.EOM,
    ReportType.EOY,
    ReportType.AdHoc,
    ReportType.Preset,
    ReportType.HistoricalShift,
    ReportType.HistoricalBatch
)