package com.swiftsku.swiftpos.data.model

import com.fdc.core.types.State
import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.FuelSaleTrxStatus
import kotlinx.serialization.Serializable
import java.util.Date
import kotlin.math.abs

@Serializable
data class FuelSaleTrx(
    val lockedAmount: Double,
    val lockedVolume: Double,
    val txnId: String,
    val txnItemId: String,
    val linkedTxnId: String? = null,
    val deviceId: Int,
    val pumpNo: Int,
    @Serializable(with = DateSerializer::class)
    val timestamp: Date,
    val lockingApplicationSender: String? = null,
    val state: State? = null,
    val amount: Double? = null,
    val volume: Double? = null,
    val volume1: Double? = null,
    val volume2: Double? = null,
    val product1: Long? = null,
    val product2: Long? = null,
    val blendRatio: Double? = null,
    val unitPrice: Double? = null,
    val authorizationApplicationSender: String? = null,
    val posTransData: POSTransData? = null,
    val status: FuelSaleTrxStatus = FuelSaleTrxStatus.Pending
)

val FuelSaleTrx.showRecall: Boolean get() = status == FuelSaleTrxStatus.Pending && linkedTxnId != null

val FuelSaleTrx.showReprint: Boolean get() = status == FuelSaleTrxStatus.Completed
val FuelSaleTrx.isPaymentFullyDone: Boolean
    get() {
        amount?.let {
            return abs(lockedAmount - amount) < 0.01
        }
        return false
    }

