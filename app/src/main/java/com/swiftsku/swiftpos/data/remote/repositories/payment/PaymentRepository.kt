package com.swiftsku.swiftpos.data.remote.repositories.payment

import com.pax.poslinksemiintegration.batch.BatchCloseRequest
import com.pax.poslinksemiintegration.batch.BatchCloseResponse
import com.pax.poslinksemiintegration.transaction.DoCreditRequest
import com.pax.poslinksemiintegration.transaction.DoCreditResponse
import com.pax.poslinksemiintegration.transaction.DoEbtRequest
import com.pax.poslinksemiintegration.transaction.DoEbtResponse
import com.swiftsku.swiftpos.data.model.PaymentResponse
import com.swiftsku.swiftpos.data.model.RefundResponse
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.payment.type.RIM
import com.swiftsku.swiftpos.payment.type.TIM
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.flow.Flow

interface PaymentRepository {

    suspend fun securedPaymentProcessor(
        eventEpoch: Int,
        tData: TIM,
        uid: String,
        autoRetry: Boolean
    ): Flow<Result<PaymentResponse>>

    suspend fun securedPaymentRefunder(
        eventEpoch: Int,
        tData: RIM,
        uid: String,
    ): Flow<Result<PaymentResponse>>

    suspend fun refundProcessor(
        eventEpoch: Int,
        tData: RIM,
        uid: String,
    ): Flow<Result<RefundResponse>>

    suspend fun fetchSgCreds(
        posid: String
    ): Result<SgCreds>

    suspend fun sendEpxCreditRequest(request: DoCreditRequest): Result<DoCreditResponse?>

    suspend fun sendEpxEbtRequest(request: DoEbtRequest): Result<DoEbtResponse?>

    suspend fun sendEpxBatchClose(request: BatchCloseRequest): Result<BatchCloseResponse?>
}