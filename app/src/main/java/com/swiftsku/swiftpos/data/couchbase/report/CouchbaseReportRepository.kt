package com.swiftsku.swiftpos.data.couchbase.report

import com.couchbase.lite.Collection
import com.swiftsku.swiftpos.data.dtos.couchbase.CbsSyncResponse
import com.swiftsku.swiftpos.data.model.AdHocReport
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EOMReport
import com.swiftsku.swiftpos.data.model.EOYReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReport
import com.swiftsku.swiftpos.data.model.HistoricalBatchReportList
import com.swiftsku.swiftpos.data.model.HistoricalShiftReport
import com.swiftsku.swiftpos.data.model.HistoricalShiftReportList
import com.swiftsku.swiftpos.data.model.PresetReport
import com.swiftsku.swiftpos.data.model.ShiftReport
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.ReportCollection
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

class CouchbaseReportRepository @Inject constructor(
    @ReportCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : ReportRepository {

    override suspend fun getEODReport(storeCode: String, date: String): EODReport? {
        return withContext(coroutineDispatcher) {
            val documentId = "$storeCode-eod-$date"

            try {
                val document = collection.getDocument(documentId)
                val jsonString = document?.toJSON() ?: return@withContext null
                val eodReport = json.decodeFromString<EODReport>(jsonString)
                eodReport
            } catch (e: Exception) {
                EventUtils.recordException(e)
                // to be removed after my marriage to avoid tight coupling
                initCbSync(listOf(documentId), "cloud.reports")
                null
            }
        }
    }

    override suspend fun getEOMReport(storeCode: String, date: String): EOMReport? {
        return withContext(coroutineDispatcher) {
            val documentId = "$storeCode-eom-$date"
            try {

                val document = collection.getDocument(documentId)
                val jsonString = document?.toJSON() ?: return@withContext null
                val eodReport = json.decodeFromString<EOMReport>(jsonString)
                eodReport
            } catch (e: Exception) {
                EventUtils.recordException(e)
                // to be removed after my marriage to avoid tight coupling
                initCbSync(listOf(documentId), "cloud.reports")
                null
            }
        }
    }

    override suspend fun getEOYReport(storeCode: String, year: Int): EOYReport? {
        return null
    }

    override suspend fun getAdHocReport(
        storeCode: String,
        startDate: String,
        endDate: String
    ): AdHocReport? {
        return null
    }

    override suspend fun getShiftReport(
        storeCode: String,
        terminalId: String,
        drawerAmount: Float?
    ): ShiftReport? {
        return null
    }

    override suspend fun getPresetReport(storeCode: String, preset: String): PresetReport? {
        return null
    }

    override suspend fun getHistoricalShiftReport(storeCode: String, shiftId: String): HistoricalShiftReport? {
        return null
    }

    override suspend fun getHistoricalShiftReportList(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?,
        cashierId: String?
    ): HistoricalShiftReportList? {
        return null
    }

    override suspend fun getHistoricalBatchReport(
        storeCode: String,
        batchId: String
    ): HistoricalBatchReport? {
        return null
    }

    override suspend fun getHistoricalBatchReportList(
        storeCode: String,
        startDate: String?,
        endDate: String?,
        terminal: String?
    ): HistoricalBatchReportList? {
        return null
    }

    override suspend fun initCbSync(
        docIds: List<String>,
        scopeCollection: String
    ): List<CbsSyncResponse> {
        return emptyList()
    }
}