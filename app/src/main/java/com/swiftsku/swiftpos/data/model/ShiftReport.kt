package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import kotlinx.serialization.Serializable

@Serializable
data class ShiftReport(
    override val storeCode: String,
    override val stats: List<ReportDetail>
) : SalesSummaryData()


fun SalesSummaryReportQuery.Data.extractShiftReportResponse(): ShiftReport? {
    val salesSummaryData = this.salesSummaryData
    if (salesSummaryData != null) {
        return ShiftReport(
            stats = salesSummaryData.stats?.filterNotNull()?.map { stat ->
                ReportDetail(
                    stat.terminalId,
                    stat.terminalData
                )
            } ?: listOf(),
            storeCode = salesSummaryData.storeCode
        )
    }
    return null
}