package com.swiftsku.swiftpos.data.type

sealed class TransactionAction(val value: String) {
    object NoSale : TransactionAction("No Sale")
    object Redeem : TransactionAction("Coupon")
    object Save : TransactionAction("Save Txn")
    object Void : TransactionAction("Void Txn")
    object Loyalty: TransactionAction("Loyalty")
    object Payout : TransactionAction("Payout")
    object Refund : TransactionAction("Refund")
}
