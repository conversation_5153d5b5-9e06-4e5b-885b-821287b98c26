package com.swiftsku.swiftpos.data.couchbase.support

import com.couchbase.lite.Collection
import com.swiftsku.swiftpos.data.model.ReleasesDoc
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.SupportDocsCollection
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import javax.inject.Inject


class SupportRepository @Inject constructor(
    @SupportDocsCollection private val collection: Collection,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val json: Json
) {
    fun getReleaseNotes(): Flow<ReleasesDoc?> = callbackFlow {
        val docId = "releases"
        fun fetchDocumentAndEmit() {
            val doc = collection.getDocument(docId)
            val docJson = doc?.toJSON()
            if (docJson == null) {
                trySend(null)
                return
            }
            launch(dispatcher) {
                try {
                    trySend(json.decodeFromString<ReleasesDoc>(docJson))
                } catch (e: Exception) {
                    EventUtils.recordException(e)
                }
            }
        }
        // fetch for the first time
        fetchDocumentAndEmit()
        // listener for changes
        val listener = collection.addDocumentChangeListener(docId) {
            fetchDocumentAndEmit()
        }
        awaitClose { listener.remove() }
    }
        .flowOn(dispatcher)
        .distinctUntilChanged()
        .catch { EventUtils.recordException(it) }
}