package com.swiftsku.swiftpos.data.type

import android.util.Log
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = PluItemPropsSerializer::class)
sealed class PluItemProps(val prop: String) {
    object Promo : PluItemProps("promo")
    object FoodStamp : PluItemProps("food_stamp")
    object NotReturnable : PluItemProps("not_returnable")
    object NoSurcharge : PluItemProps("no_surcharge")
    object NoSurchargeOption : PluItemProps("no_surcharge_option")

    override fun toString(): String {
        return prop
    }
}

object PluItemPropsSerializer : KSerializer<PluItemProps> {
    override val descriptor: SerialDescriptor =
            PrimitiveSerialDescriptor("PluItemProps", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: PluItemProps) {
        encoder.encodeString(value.prop)
    }

    override fun deserialize(decoder: Decoder): PluItemProps {
        return when (val prop = decoder.decodeString().uppercase()) {
            "PROMO" -> PluItemProps.Promo
            "FOOD_STAMP" -> PluItemProps.FoodStamp
            "NOT_RETURNABLE" -> PluItemProps.NotReturnable
            "NO_SURCHARGE" -> PluItemProps.NoSurcharge
            "NO_SURCHARGE_OPTION" -> PluItemProps.NoSurchargeOption
            else -> {
                Log.w("Deserialize", "Unknown prop: $prop")
                PluItemProps.Promo
            }
        //            else -> throw IllegalArgumentException("Unknown prop: $prop")
        }
    }
}
