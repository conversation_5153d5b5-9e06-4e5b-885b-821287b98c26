package com.swiftsku.swiftpos.data.couchbase.user

import com.couchbase.lite.Collection
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.MenuKeyDoc
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.User
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.StoreCollection
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

class CouchbaseUserRepository @Inject constructor(
    @StoreCollection private val collection: Collection,
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val jsonEncoder: Json
) :
    UserRepository {
    override suspend fun fetchUserConfig(email: String): User? {
        return withContext(dispatcher) {
            try {
                val doc = collection.getDocument(email)?.toJSON() ?: return@withContext null
                jsonEncoder.decodeFromString<User>(doc)
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }
    }

    override suspend fun fetchStoreUsers(storeCode: String): StoreUsers? {
        return withContext(dispatcher) {
            try {
                val docJson = collection.getDocument(storeCode)?.toJSON() ?: return@withContext null
                jsonEncoder.decodeFromString<StoreUsers>(docJson)
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }
    }

    override suspend fun getStoreConfigStream(storeCode: String): Flow<StoreUsers?> {
        return callbackFlow {
            val doc = collection.getDocument(storeCode)
            doc?.toJSON()?.let {
                val storeConfigDoc = jsonEncoder.decodeFromString<StoreUsers>(it)
                trySend(storeConfigDoc)
            }

            // Add document change listener and send updates through
            val listener = collection.addDocumentChangeListener(storeCode) { _ ->
                val updatedDoc = collection.getDocument(storeCode)
                updatedDoc?.toJSON()?.let {
                    val storeConfig = jsonEncoder.decodeFromString<StoreUsers>(it)
                    trySend(storeConfig)
                }
            }

            awaitClose { listener.remove() }
        }.flowOn(dispatcher).catch { e -> EventUtils.recordException(e) }
    }
}