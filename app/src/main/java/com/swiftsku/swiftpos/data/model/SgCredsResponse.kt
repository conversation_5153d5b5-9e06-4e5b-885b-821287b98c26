package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.FetchSgCredsQuery
import kotlinx.serialization.Serializable

data class SgCredsResponseData(
    val status: Boolean = false,
    val msg: String,
    val data: SgCreds
)

@Serializable
data class SgCreds(
    val uid: String,
    val pwd: String,
)



fun FetchSgCredsQuery.Data.extractResponse(): SgCredsResponseData {
    return SgCredsResponseData(
        status = this.fetchSgCreds?.status?.toBoolean() ?: false,
        msg = this.fetchSgCreds?.msg ?: "",
        data = SgCreds(
            uid = this.fetchSgCreds?.data?.uid ?: "",
            pwd = this.fetchSgCreds?.data?.pwd ?: ""
        )
    )
}
