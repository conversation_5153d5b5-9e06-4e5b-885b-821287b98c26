package com.swiftsku.swiftpos.data.couchbase

import androidx.annotation.Nullable
import com.couchbase.lite.Collection
import com.couchbase.lite.Database
import com.couchbase.lite.databaseChangeFlow
import javax.inject.Inject

class Couchbase @Inject constructor(private val database: Database) {
    // Create a new named collection (like a SQL table)
    // in the database's default scope.
    fun getDb() = database

    fun createCollection(
        collName: String,
        scopeName: String? = null
    ): Collection = database.createCollection(collName, scopeName)




    fun getCollection(
        collectionName: String,
        scopeName: String? = null
    ) = database.getCollection(collectionName, scopeName)

    fun closeDb() = database.close()

    fun deleteCollection(
        collectionName: String,
        scopeName: String? = null
    ) = database.deleteCollection(collectionName, scopeName)

    fun deleteCollections() = database.delete()
}