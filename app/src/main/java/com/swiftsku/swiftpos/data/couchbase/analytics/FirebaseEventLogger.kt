package com.swiftsku.swiftpos.data.couchbase.analytics

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import javax.inject.Inject

class FirebaseEventLogger @Inject constructor(
    private val analytics: FirebaseAnalytics
) : EventLogger {

    override suspend fun barcodeScan(scanned: String, converted: String) {
        analytics.logEvent(AnalyticEvent.BarcodeScan.event, Bundle().apply {
            putString("scanned_barcode", scanned)
            putString("converted_barcode", converted)
        })
    }
}