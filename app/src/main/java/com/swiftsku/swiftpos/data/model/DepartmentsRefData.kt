package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class DepartmentsRefData(
    @SerialName("doc_type")
    val docType: String,
    @SerialName("store_code")
    val storeCode: String,
    @SerialName("store_id")
    val storeId: String,
    @SerialName("refdata_type")
    val refdataType: String,
    var departments: List<Department>? = null
)