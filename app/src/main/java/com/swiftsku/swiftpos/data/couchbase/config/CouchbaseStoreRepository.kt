package com.swiftsku.swiftpos.data.couchbase.config

import com.couchbase.lite.Collection
import com.couchbase.lite.DataSource
import com.couchbase.lite.DocumentChange
import com.couchbase.lite.DocumentChangeListener
import com.couchbase.lite.Expression
import com.couchbase.lite.MutableDictionary
import com.couchbase.lite.QueryBuilder
import com.couchbase.lite.SelectResult
import com.couchbase.lite.documentChangeFlow
import com.couchbase.lite.queryChangeFlow
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.data.model.PinPadConnectionType
import com.swiftsku.swiftpos.data.model.PinpadConfig
import com.swiftsku.swiftpos.data.model.PresetConfig
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.TerminalConfig
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.StoreCollection
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import java.util.concurrent.Executor
import java.util.concurrent.Executors
import javax.inject.Inject

class CouchbaseStoreRepository @Inject constructor(
    @StoreCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : StoreRepository {


    override suspend fun fetchStoreConfig(serialKey: String): StoreConfig? {
        return withContext(coroutineDispatcher) {


            val doc = collection.getDocument(serialKey)?.toJSON() ?: return@withContext null
            val storeConfigJson = json.decodeFromString<JsonObject>(doc).jsonObject
            try {
                val cachedStoreConfig =
                    json.decodeFromString<StoreConfig>(storeConfigJson.toString())
                cachedStoreConfig
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }

    }


    override suspend fun storeConfigFlow(serialKey: String): Flow<StoreConfig> = callbackFlow {
        fun fetchDocumentAndEmit(docId: String) {
            collection.getDocument(docId)?.toJSON()?.let {
                try {
                    trySend(json.decodeFromString<StoreConfig>(it))
                } catch (e: Exception) {
                    EventUtils.recordException(e)
                }
            }
        }
        // fetch for the first time
        fetchDocumentAndEmit(serialKey)
        // listener for changes
        val listener = collection.addDocumentChangeListener(serialKey) { documentChange ->
            fetchDocumentAndEmit(documentChange.documentID)
        }
        awaitClose { listener.remove() }
    }
        .flowOn(coroutineDispatcher)
        .distinctUntilChanged()
        .catch { EventUtils.recordException(it) }

    override suspend fun fetchTerminalConfig(posId: String): TerminalConfig? {

        return null
    }

    override suspend fun savePluItemsOrder(posId: String, pluOrder: List<String>): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                collection.getDocument(posId)?.toMutable()?.let {
                    it.setValue(StoreConfig.Keys.PLU_ITEMS_ORDER, pluOrder)
                    collection.save(it)
                }
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    override suspend fun presetConfigFlow(storeCode: String): Flow<PresetConfig?> {
        val documentId = "$storeCode-reportpresets"

        return callbackFlow<PresetConfig?> {

            collection.getDocument(documentId)?.toJSON()?.let { doc ->
                val presetConfig = json.decodeFromString<PresetConfig>(doc)
                trySend(presetConfig)
            }

            val token =
                collection.addDocumentChangeListener(documentId) { change ->

                    Logger.d("PresetConfig Flow: ${change.documentID}")

                    val document = collection.getDocument(change.documentID)?.toJSON()
                        ?: return@addDocumentChangeListener
                    val presetConfig = json.decodeFromString<PresetConfig>(document)
                    Logger.d("PresetConfig Flow: $presetConfig")
                    trySend(presetConfig)
                }
            awaitClose {
                token.remove()
            }
        }
            .flowOn(coroutineDispatcher)
            .distinctUntilChanged()
            .catch { EventUtils.recordException(it) }
    }

    override suspend fun getCurrentBatchId(posId: String): String? {
        return withContext(coroutineDispatcher) {
            return@withContext try {
                collection.getDocument(posId)?.getString(StoreConfig.Keys.BATCH_ID)
            } catch (e: Exception) {
                EventUtils.recordException(e)
                null
            }
        }
    }

    override suspend fun updateCurrentBatchId(posId: String, batchId: String): String {
        return withContext(coroutineDispatcher) {
            try {
                collection.getDocument(posId)?.toMutable()?.let {
                    it.setValue(StoreConfig.Keys.BATCH_ID, batchId)
                    collection.save(it)
                }
                return@withContext batchId
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext ""
            }
        }
    }

    override suspend fun updateEpxTerminalIp(posId: String, ip: String): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                collection.getDocument(posId)?.let { document ->
                    val mutableDoc = document.toMutable()
                    var pinpadConfig = document.getDictionary(StoreConfig.Keys.PINPAD_CONFIG)
                    if (pinpadConfig == null) {
                        val map = mutableMapOf<String, Any>(
                            "ip" to ip,
                            "port" to "10009",
                            "timeout" to 60000,
                            "ctype" to PinPadConnectionType.TCP.value.uppercase()
                        )
                        pinpadConfig = MutableDictionary(map)
                    } else {
                        pinpadConfig = pinpadConfig.toMutable().setString("ip", ip)
                    }
                    mutableDoc.setDictionary(StoreConfig.Keys.PINPAD_CONFIG, pinpadConfig)
                    collection.save(mutableDoc)
                }
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    override suspend fun setPinpadIdleImageSet(posId: String): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                collection.getDocument(posId)?.let { document ->
                    val mutableDoc = document.toMutable()
                    mutableDoc.setBoolean(StoreConfig.Keys.PINPAD_IDLE_IMAGE_SET, true)
                    collection.save(mutableDoc)
                    true
                }
                false
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }
}