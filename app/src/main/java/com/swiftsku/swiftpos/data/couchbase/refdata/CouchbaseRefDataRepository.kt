package com.swiftsku.swiftpos.data.couchbase.refdata

import com.couchbase.lite.Collection
import com.couchbase.lite.MutableArray
import com.couchbase.lite.MutableDocument
import com.swiftsku.swiftpos.data.dtos.couchbase.PayoutOptions
import com.swiftsku.swiftpos.data.dtos.couchbase.StoreTaxes
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.DepartmentsDoc
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.MenuKeyDoc
import com.swiftsku.swiftpos.data.model.DepartmentsRefData
import com.swiftsku.swiftpos.data.model.MenuKeysRefData
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.RefDataCollection
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import javax.inject.Inject

class CouchbaseRefDataRepository @Inject constructor(
    @RefDataCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json
) : RefDataRepository {
    override fun getDepartmentOnDashboardStream(storeCode: String): Flow<List<Department>> {
        val docId = "$storeCode-departments"
        return callbackFlow {
            val departments = mutableListOf<Department>()
            val doc = collection.getDocument(docId)
            doc?.toJSON()?.let {
                val departmentDoc = json.decodeFromString<DepartmentsDoc>(it)
                departments.addAll(departmentDoc.departments)
            }
            trySend(departments)

            // Add document change listener and send updates through
            val listener = collection.addDocumentChangeListener(docId) { _ ->
                departments.clear()
                val updatedDoc = collection.getDocument(docId)
                updatedDoc?.toJSON()?.let {
                    val departmentDoc = json.decodeFromString<DepartmentsDoc>(it)
                    departments.addAll(departmentDoc.departments)
                }
                trySend(departments)
            }
            awaitClose { listener.remove() }
        }.flowOn(coroutineDispatcher).catch { e -> EventUtils.recordException(e) }
    }

    override suspend fun getTax(taxId: String, storeId: String): Tax? {
        return withContext(coroutineDispatcher) {

            val document = collection.getDocument("$storeId-taxes")?.toJSON()
            if (document != null) {
                val element =
                    json.decodeFromString<JsonObject>(document)["taxes"]?.jsonObject?.get(taxId)
                if (element != null) {
                    return@withContext json.decodeFromJsonElement(Tax.serializer(), element)
                }
            }
            return@withContext null
        }
    }

    override fun getTaxes(storeCode: String): Flow<List<Tax>> {
        return callbackFlow {
            val docId = "$storeCode-taxes"

            fun parseAndSend(jsonDoc: String) {
                val taxes = mutableListOf<Tax>()
                val taxMap = json.decodeFromString<StoreTaxes>(jsonDoc)
                taxMap.taxes.forEach {
                    taxes.add(it.value)
                }
                trySend(taxes)
            }

            val token = collection.addDocumentChangeListener(docId) {
                try {
                    // Get the updated document
                    collection.getDocument(it.documentID)?.toJSON()?.let {
                        parseAndSend(it)
                    }
                } catch (e: Exception) {
                    EventUtils.recordException(e)
                }
            }

            collection.getDocument(docId)?.toJSON()?.let {
                parseAndSend(it)
            }
            awaitClose { token.close() }
        }
            .distinctUntilChanged()
            .flowOn(coroutineDispatcher)
            .catch { EventUtils.recordException(it) }
    }

    override fun getPayoutOptions(query: String, storeCode: String): Flow<PayoutOptions> =
        callbackFlow {
            fun parseAndSend(doc: String?) {
                if (doc == null) {
                    return
                }
                val payoutOptions =
                    json.decodeFromString<PayoutOptions>(doc)

                trySend(payoutOptions)
            }

            val docId = "$storeCode-payout"

            val token = collection
                .addDocumentChangeListener(docId) { change ->
                    val doc = collection.getDocument(change.documentID)?.toJSON()
                    parseAndSend(doc)
                }
            val doc = collection.getDocument(docId)?.toJSON()
            parseAndSend(doc)
            awaitClose { token.close() }
        }
            .distinctUntilChanged()
            .flowOn(coroutineDispatcher)
            .catch { EventUtils.recordException(it) }

    override suspend fun saveReorderedDepartments(
        storeId: String,
        departments: List<Department>
    ): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                val docId = "$storeId-departments"

                val existingDocJSON = collection.getDocument(docId)?.toJSON()
                existingDocJSON?.let {
                    val refData = json.decodeFromString<DepartmentsRefData>(it)

                    refData.departments = departments

                    val encodeToString = json.encodeToString(refData)
                    val document = MutableDocument(docId, encodeToString)
                    collection.save(document)
                }
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    override suspend fun saveReorderedMenuKeys(
        storeCode: String,
        menuKeys: List<MenuKey>
    ): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                val docId = "$storeCode-menukeys"

                val existingDocJSON = collection.getDocument(docId)?.toJSON()
                existingDocJSON?.let {
                    val refData = json.decodeFromString<MenuKeysRefData>(it)

                    refData.menukeys = menuKeys

                    val encodeToString = json.encodeToString(refData)
                    val document = MutableDocument(docId, encodeToString)
                    collection.save(document)
                }
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    override fun getMenuKeysStream(storeCode: String): Flow<List<MenuKey>> {
        val docId = "$storeCode-menukeys"
        return callbackFlow {
            val menuKeys = mutableListOf<MenuKey>()
            val doc = collection.getDocument(docId)
            doc?.toJSON()?.let {
                val menuKeyDoc = json.decodeFromString<MenuKeyDoc>(it)
                menuKeys.addAll(menuKeyDoc.menuKeys)
            }
            trySend(menuKeys)

            // Add document change listener and send updates through
            val listener = collection.addDocumentChangeListener(docId) { _ ->
                menuKeys.clear()
                val updatedDoc = collection.getDocument(docId)
                updatedDoc?.toJSON()?.let {
                    val menuKeyDoc = json.decodeFromString<MenuKeyDoc>(it)
                    menuKeys.addAll(menuKeyDoc.menuKeys)
                }
                trySend(menuKeys)
            }

            awaitClose { listener.remove() }
        }.flowOn(coroutineDispatcher).catch { e -> EventUtils.recordException(e) }
    }

    override suspend fun addVendor(storeCode: String, vendor: String): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                val docId = "$storeCode-payout"
                val document =
                    collection.getDocument(docId)?.toMutable() ?: return@withContext false

                val currentVendors = document.getArray("vendor") ?: MutableArray()
                if (!currentVendors.contains(vendor)) {
                    currentVendors.addString(vendor)
                }
                document.setArray("vendor", currentVendors)
                collection.save(document)
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }
}