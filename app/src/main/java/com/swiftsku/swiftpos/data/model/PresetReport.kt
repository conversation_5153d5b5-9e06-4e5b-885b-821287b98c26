package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import kotlinx.serialization.Serializable

@Serializable
data class PresetReport(
    override val storeCode: String,
    override val stats: List<ReportDetail> = listOf()
) : SalesSummaryData()


fun SalesSummaryReportQuery.Data.extractPresetReportResponse(): PresetReport? {
    val salesSummaryData = this.salesSummaryData
    if (salesSummaryData != null) {
        return  PresetReport(
            stats = salesSummaryData.stats?.filterNotNull()?.map { stat ->
                ReportDetail(
                    stat.terminalId,
                    stat.terminalData
                )
            } ?: listOf(),
            storeCode = salesSummaryData.storeCode
        )
    }
    return null
}