package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.PluFormatType
import com.swiftsku.swiftpos.data.type.PluFormatTypeSerializer
import com.swiftsku.swiftpos.data.type.PluItemProps
import kotlinx.serialization.Serializable

@Serializable
data class GUPCItem(
    val pluId: String,
    @Serializable(with = PluFormatTypeSerializer::class)
    val pluFormat: PluFormatType,
    val pluModifier: String,
    val description: String,
    val props: List<PluItemProps>? = null,
)
