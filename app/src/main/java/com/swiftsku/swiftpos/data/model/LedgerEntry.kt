package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.TxnPaymentType
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder


@Serializable
data class LedgerEntry(
    val txnId: String,
    val accountId: String,
    val linkedTxnId: String,
    val txnTime: Long,
    val cashierId: String,
    val ledgerReason: LedgerReason,
    val ledgerDirection: LedgerDirection,
    val fundMop: TxnPaymentType? = null,
    val notes: String? = null,
    val amountCents: Int,
    val newOutstandingCents: Int,
    @SerialName("doc_type")
    val docType: String = "ledger_item",
    @SerialName("store_code")
    val storeCode: String,
    val cardInfo: CardInfo? = null,
    val appliedFees: List<AppliedFee>? = emptyList(),
)

enum class LedgerDirection {
    CREDIT, // Money into account (e.g., Add Funds)
    DEBIT   // Money out of account (e.g., Sale)
}

@Serializable(with = LedgerReasonSerializer::class)
enum class LedgerReason(val value: String) {
    ADD_FUNDS("Add funds"),
    SALE("Credit Sale"),
    REFUND("Sale Refund"),
    ADJUSTMENT("Adjustment")
}

object LedgerReasonSerializer : KSerializer<LedgerReason> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("LedgerReason", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: LedgerReason) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): LedgerReason {
        return when (val value = decoder.decodeString()) {
            "Add funds" -> LedgerReason.ADD_FUNDS
            "Credit Sale" -> LedgerReason.SALE
            "Sale Refund" -> LedgerReason.REFUND
            "Adjustment" -> LedgerReason.ADJUSTMENT
            else -> throw IllegalArgumentException("Unknown LedgerReason: $value")
        }
    }
}
