package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.PluItemProps
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.Period

@Serializable
data class Department(
    val departmentId: String,
    val parentDepartmentId: String? = "000", // "000" is considered as unassigned
    val departmentName: String,
    val departmentPrice: Float = 0f,
    val taxes: List<String> = emptyList(),
    val props: List<PluItemProps> = emptyList(),
    val showInDashboard: Boolean = false,
    val showInMobileApp: Boolean = true,
    val ebtSupported: Boolean = true,
    val minimumCustomerAge: Int? = 0,
    val linkedPluData: List<LinkedPluData>? = null
)

fun Department.isEBT(): Boolean = props.contains(PluItemProps.FoodStamp)

fun Department.applyEBT(): Department {
    var previousProps = props
    if (!previousProps.contains(PluItemProps.FoodStamp)) {
        previousProps = previousProps.plus(PluItemProps.FoodStamp)
    }
    return this.copy(props = previousProps.plus(PluItemProps.FoodStamp))
}

fun isUnderAge(minimumAge: Int?, customerDob: String?): Boolean {
    if (minimumAge == null || minimumAge == 0) {
        return false
    }
    if (customerDob == null) {
        return true
    }
    return try {
        minimumAge > Period.between(
            LocalDate.of(
                customerDob.substring(4).toInt(),
                customerDob.substring(0, 2).toInt(),
                customerDob.substring(2, 4).toInt(),
            ), LocalDate.now()
        ).years
    } catch (e: Exception) {
        true
    }
}