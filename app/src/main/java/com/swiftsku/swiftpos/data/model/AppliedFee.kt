package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.Serializable

@Serializable data class AppliedFee(val type: FeeType, val amount: Float, val info: String? = null)

enum class FeeType {
    CARD_PROCESSING,
    SURCHARGE
}

val FeeType.label: String
    get() =
            when (this) {
                FeeType.CARD_PROCESSING -> "Card fee"
                FeeType.SURCHARGE -> "Surcharge fee"
            }
