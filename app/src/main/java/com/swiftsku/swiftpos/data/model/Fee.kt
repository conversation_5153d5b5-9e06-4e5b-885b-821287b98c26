package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.Serializable


/**
 * @param applicationType - Defined which value to consider for fee.
 * @param feePerc - Fee to be levied in percentage.
 * @param feeFixed - Fixed amount of fee in cents.
 *
 * Either feePerc or feeFixed should be present.
 */
@Serializable
data class Fee(
    val type: FeeType = FeeType.CARD_PROCESSING,
    val enabled: Boolean = false,
    val applicationType: FeeApplicationType = FeeApplicationType.PERCENT,
    val feePerc: Float? = 0f,
    val feeFixed: Int? = 0,
    val ignoreThreshold: Float? = null,
    val noFuelSurcharge: Boolean = false,
    val noFuelSurchargeOption: Boolean = false,
)

enum class FeeApplicationType {
    PERCENT, FIXED
}
