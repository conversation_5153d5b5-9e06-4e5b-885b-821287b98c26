package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PresetConfig(
    @SerialName("doc_type")
    val docType: String = "config_item",
    @SerialName("config_type")
    val configType: String = "report_preset",
    @SerialName("store_code")
    val storeCode: List<String>,
    val presets: Map<String, Preset> = emptyMap()
)

@Serializable
data class Preset(
    val description: String,
)