package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = CardPaymentStatusSerializer::class)
sealed class CardPaymentStatus(val tender: String) {
    object Approved : CardPaymentStatus("APPROVED")
    object Declined : CardPaymentStatus("DECLINED")
    object Error : CardPaymentStatus("ERROR")
    object Unknown : CardPaymentStatus("UNKNOWN")
}


object CardPaymentStatusSerializer : KSerializer<CardPaymentStatus> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("CardPaymentStatus", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: CardPaymentStatus) {
        encoder.encodeString(value.tender)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): CardPaymentStatus {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            "APPROVED" -> CardPaymentStatus.Approved
            "DECLINED" -> CardPaymentStatus.Declined
            "ERROR" -> CardPaymentStatus.Error
            "UNKNOWN" -> CardPaymentStatus.Unknown
            else -> throw IllegalArgumentException("Unknown CardPaymentStatus: $format")
        }
    }
}