package com.swiftsku.swiftpos.data.couchbase.refdata

import com.swiftsku.swiftpos.data.dtos.couchbase.PayoutOptions
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.Tax
import kotlinx.coroutines.flow.Flow

interface RefDataRepository {

    fun getDepartmentOnDashboardStream(storeCode: String): Flow<List<Department>>

    suspend fun getTax(taxId: String, storeId: String): Tax?
    fun getTaxes(storeCode: String): Flow<List<Tax>>

    fun getPayoutOptions(query: String = "", storeCode: String): Flow<PayoutOptions>

    suspend fun saveReorderedDepartments(storeId: String, departments: List<Department>): Boolean

    suspend fun saveReorderedMenuKeys(storeCode: String, menuKeys: List<MenuKey>): Boolean

    fun getMenuKeysStream(storeCode: String): Flow<List<MenuKey>>

    suspend fun addVendor(storeCode: String, vendor: String): Boolean
}