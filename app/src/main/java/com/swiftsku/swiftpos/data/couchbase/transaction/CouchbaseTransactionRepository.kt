package com.swiftsku.swiftpos.data.couchbase.transaction

import com.couchbase.lite.Collection
import com.couchbase.lite.DataSource
import com.couchbase.lite.Expression
import com.couchbase.lite.Meta
import com.couchbase.lite.MutableDocument
import com.couchbase.lite.Ordering
import com.couchbase.lite.Query
import com.couchbase.lite.QueryBuilder
import com.couchbase.lite.SelectResult
import com.couchbase.lite.queryChangeFlow
import com.swiftsku.swiftpos.data.couchbase.credit.CreditRepository
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.hasCardPayment
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.TransactionCollection
import com.swiftsku.swiftpos.extension.dateTime
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.ui.dashboard.main.state.FPLockState
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import java.time.LocalDate
import java.time.LocalTime
import java.util.Date
import javax.inject.Inject


class CouchbaseTransactionRepository @Inject constructor(
    @TransactionCollection private val collection: Collection,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val json: Json,
    private val creditRepository: CreditRepository
) : TransactionRepository {

    override suspend fun saveTransaction(transaction: Transaction): Boolean {
        return withContext(coroutineDispatcher) {

            try {
                val existingDoc = collection.getDocument(transaction.txnId)
                val encodeToString = json.encodeToString(transaction)

                if (existingDoc == null) {
                    val document = MutableDocument(
                        transaction.txnId,
                        encodeToString,
                    )
                    document.setString("doc_type", "txn_item")
                    document.setBoolean("is_integrated", false)
                    document.setString("store_code", transaction.txnId.take(5))
                    collection.save(document)
                } else {
                    val newDocument = MutableDocument(
                        transaction.txnId,
                        encodeToString,
                    )
                    val newDocMap = newDocument.toMap().toMutableMap()
                    val newDoc = existingDoc.toMutable().setData(newDocMap)
                    newDoc.setString("doc_type", "txn_item")
                    newDoc.setBoolean("is_integrated", false)
                    newDoc.setString("store_code", transaction.txnId.take(5))
                    collection.save(newDoc)
                }
                // If the SaleTransaction has "Credit" in txnPayment, we need to create a ledger entry
                if (transaction.txnStatus == TransactionStatus.Complete
                    && transaction.txnType == TransactionType.Sale
                ) {
                    (transaction as? SaleTransaction)?.let { txn ->
                        (txn.txnPayment[TxnPaymentType.Credit] as? CreditPayment)?.let {
                            creditRepository.createLedgerEntryForSale(txn, it)
                        }
                    }
                }
                return@withContext true

            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    override suspend fun completeRefundTransaction(refundTransaction: RefundTransaction): Boolean {
        return saveTransaction(
            refundTransaction.copy(
                txnStatus = TransactionStatus.Complete,
                statusHistory = HashMap(refundTransaction.statusHistory.orEmpty()).apply {
                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                },
                txnEndTime = Date()
            )
        )
    }

    override suspend fun deleteTransaction(transaction: Transaction): Boolean {
        return withContext(coroutineDispatcher) {
            try {
                collection.getDocument(transaction.txnId)?.let {
                    collection.delete(it)
                }
                true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                false
            }
        }
    }


    override suspend fun getTransaction(transactionID: String): Transaction? {
        return withContext(coroutineDispatcher) {

            val txnDocument = collection.getDocument(transactionID)
//        Log.d("txnDocument", txnDocument.toString())
            return@withContext if (txnDocument != null) {

                val txnMap =
                    txnDocument.toJSON()?.let { json.decodeFromString<JsonObject>(it).jsonObject }
                return@withContext json.decodeFromString<Transaction>(txnMap.toString())
                //            return null
            } else {
                null
            }
        }

    }

    override suspend fun getFuelTransactions(deviceId: Int): List<SaleTransaction> =
        withContext(coroutineDispatcher) {
            val query = QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .where(
                    Expression.property("txnStatus")
                        .equalTo(Expression.string(TransactionStatus.FuelDispensed.status))
                        .or(
                            Expression.property("txnStatus")
                                .equalTo(Expression.string(TransactionStatus.Complete.status))
                        )
                        .or(
                            Expression.property("txnStatus")
                                .equalTo(Expression.string(TransactionStatus.FuelAuthorized.status))
                        ).and(
                            Expression.property("fuelMeta").isValued
                        )
                )
                .orderBy(Ordering.property("txnStartTime").descending())


            val transactionsList = mutableListOf<SaleTransaction>()
            query.execute()
                .allResults().forEach { result ->
                    try {
                        result?.toJSON()?.let { jsonString ->
                            json.decodeFromString<Map<String, SaleTransaction>>(
                                jsonString
                            )["txn"]?.let {
                                transactionsList.add(it)
                            }
                        }
                    } catch (e: Exception) {
                        EventUtils.recordException(e)
                    }
                }
            return@withContext transactionsList.filter {
                it.fuelMeta
                    ?.authorizedPumps
                    ?.any { pumpState -> pumpState.deviceId == deviceId } ?: false
            }
        }

    override suspend fun getPendingFuelCardTransactions(): List<SaleTransaction> =
        withContext(coroutineDispatcher) {
            val query = QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .where(
                    Expression.property("txnStatus")
                        .equalTo(Expression.string(TransactionStatus.FuelDispensed.status))
                        .or(
                            Expression.property("txnStatus")
                                .equalTo(Expression.string(TransactionStatus.FuelAuthorized.status))
                        ).and(
                            Expression.property("fuelMeta").isValued
                        )
                )
                .orderBy(Ordering.property("txnStartTime").descending())

            val transactionsList = mutableListOf<SaleTransaction>()
            query.execute()
                .allResults().forEach { result ->
                    try {
                        result?.toJSON()?.let { jsonString ->
                            json.decodeFromString<Map<String, SaleTransaction>>(
                                jsonString
                            )["txn"]?.let {
                                if (it.hasCardPayment()) {
                                    transactionsList.add(it)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        EventUtils.recordException(e)
                    }
                }
            return@withContext transactionsList
        }

    override suspend fun getPendingFuelTransactionsFlow(): Flow<List<SaleTransaction>> {
        val query = QueryBuilder
            .select(SelectResult.all())
            .from(DataSource.collection(collection))
            .where(
                Expression.property("txnStatus")
                    .equalTo(Expression.string(TransactionStatus.FuelDispensed.status))
                    .or(
                        Expression.property("txnStatus")
                            .equalTo(Expression.string(TransactionStatus.FuelAuthorized.status))
                    ).and(
                        Expression.property("fuelMeta").isValued
                    )
            )
            .orderBy(Ordering.property("txnStartTime").descending())
        return query
            .queryChangeFlow()
            .flowOn(coroutineDispatcher)
            .map { queryChange ->
                val transactionsList = mutableListOf<SaleTransaction>()
                queryChange.results?.let { results ->
                    results.forEach { result ->
                        try {
                            result?.toJSON()?.let { jsonString ->
                                json.decodeFromString<Map<String, SaleTransaction>>(
                                    jsonString
                                )["txn"]?.let {
                                    transactionsList.add(it)
                                }
                            }
                        } catch (e: Exception) {
                            EventUtils.recordException(e)
                        }
                    }
                }
                transactionsList
            }.catch { EventUtils.recordException(it) }
    }

    override suspend fun getTransactions(
        transactionStatus: TransactionStatus?,
        txnType: TransactionType?,
        searchQuery: String?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        startTime: LocalTime?,
        endTime: LocalTime?,
        limit: Int,
        offset: Int,
        status: List<TransactionStatus>?
    ): List<Transaction> {


        var expression: Expression? = null

        if (!searchQuery.isNullOrEmpty()) {
            expression = Expression.property("txnId").regex(Expression.string(searchQuery))
        }

        if (transactionStatus != null) {
            val statusExpression =
                Expression
                    .property("txnStatus")
                    .equalTo(Expression.string(transactionStatus.status))

            expression = expression?.and(statusExpression) ?: statusExpression

        }
        if (!status.isNullOrEmpty()) {
            val statusExpression =
                Expression.property("txnStatus")
                    .`in`(*status.map { Expression.string(it.status) }.toTypedArray())

            expression = expression?.and(statusExpression) ?: statusExpression
        }
        if (txnType != null) {
            val txnTypeExpression =
                Expression.property("txnType").equalTo(Expression.string(txnType.tender))
            expression = expression?.and(txnTypeExpression) ?: txnTypeExpression
        }



        if (startDate != null && endDate != null && startTime != null && endTime != null) {

            try {

                val dateRangeExpression =
                    Expression.property("txnEndTime").between(
                        Expression.longValue(
                            dateTime(
                                startDate,
                                startTime
                            )
                        ),
                        Expression.longValue(
                            dateTime(
                                endDate,
                                endTime
                            )
                        )
                    )

                expression = expression?.and(dateRangeExpression) ?: dateRangeExpression
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }

        }


        val query: Query = if (expression != null) {
            QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .where(expression)
                .orderBy(Ordering.property("txnEndTime").descending())
                .limit(Expression.intValue(limit), Expression.intValue(offset))
        } else {
            QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .orderBy(Ordering.property("txnEndTime").descending())
                .limit(Expression.intValue(limit), Expression.intValue(offset))
        }

        val transactionsList = mutableListOf<Transaction>()
        query.execute()
            .allResults().forEach { result ->
                try {
                    result?.toJSON()?.let { jsonString ->
                        json.decodeFromString<Map<String, Transaction>>(
                            jsonString
                        )["txn"]?.let {
                            transactionsList.add(it)
                        }
                    }
                } catch (e: Exception) {
                    EventUtils.recordException(e)
                }
            }
        return transactionsList
    }

    override fun getTransactionsFlow(
        transactionStatus: TransactionStatus?,
        txnType: TransactionType?,
        searchQuery: String?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        startTime: LocalTime?,
        endTime: LocalTime?,
        limit: Int,
        offset: Int
    ): Flow<List<Transaction>> {


        var expression: Expression? =
            Expression.property("is_queue").equalTo(Expression.booleanValue(false))
                .or(Expression.property("is_queue").isNotValued)

        if (!searchQuery.isNullOrEmpty()) {
            expression = Expression.property("txnId").regex(Expression.string(searchQuery))
        }

        if (transactionStatus != null) {
            val statusExpression =
                Expression
                    .property("txnStatus")
                    .equalTo(Expression.string(transactionStatus.status))

            expression = expression?.and(statusExpression) ?: statusExpression

        }
        if (txnType != null) {
            val txnTypeExpression =
                Expression.property("txnType").equalTo(Expression.string(txnType.tender))
            expression = expression?.and(txnTypeExpression) ?: txnTypeExpression
        }



        if (startDate != null && endDate != null && startTime != null && endTime != null) {

            try {

                val dateRangeExpression =
                    Expression.property("txnEndTime").between(
                        Expression.longValue(
                            dateTime(
                                startDate,
                                startTime
                            )
                        ),
                        Expression.longValue(
                            dateTime(
                                endDate,
                                endTime
                            )
                        )
                    )

                expression = expression?.and(dateRangeExpression) ?: dateRangeExpression
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }

        }


        val query: Query = if (expression != null) {
            QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .where(expression)
                .orderBy(Ordering.property("txnEndTime").descending())
                .limit(Expression.intValue(limit), Expression.intValue(offset))
        } else {
            QueryBuilder
                .select(SelectResult.all())
                .from(DataSource.collection(collection))
                .orderBy(Ordering.property("txnEndTime").descending())
                .limit(Expression.intValue(limit), Expression.intValue(offset))
        }



        return query
            .queryChangeFlow()
            .flowOn(coroutineDispatcher)
            .map { queryChange ->
                val transactionsList = mutableListOf<Transaction>()
                queryChange.results?.let { results ->
                    results.forEach { result ->
                        try {
                            result?.toJSON()?.let { jsonString ->
                                json.decodeFromString<Map<String, Transaction>>(
                                    jsonString
                                )["txn"]?.let {
                                    transactionsList.add(it)
                                }
                            }
                        } catch (e: Exception) {
                            EventUtils.recordException(e)
                        }
                    }
                }
                transactionsList
            }.catch { EventUtils.recordException(it) }
    }

    override fun getCustomerCountAndCurrentSales(epoch: Long): Flow<Pair<Int, Float>> {


        val query = QueryBuilder
            .select(SelectResult.expression(Meta.id), SelectResult.property("txnTotalNetAmount"))
            .from(DataSource.collection(collection))
            .where(
                Expression.property("txnStatus")
                    .equalTo(Expression.string(TransactionStatus.Complete.status))
                    .and(
                        Expression.property("txnType")
                            .equalTo(Expression.string(TransactionType.Sale.tender))
                    )
                    .and(
                        Expression.property("txnStartTime")
                            .greaterThanOrEqualTo(Expression.value(epoch))
                    )
            )

        return query
            .queryChangeFlow()
            .flowOn(coroutineDispatcher)
            .map { queryChange ->
                queryChange.results?.allResults()?.let { resultSet ->
                    val totalTransactions = resultSet.size
                    val totalNetAmount = resultSet.sumOf {
                        it.getNumber("txnTotalNetAmount")!!.toDouble()
                    }.toFloat()
                    Pair(totalTransactions, totalNetAmount)
                } ?: Pair(0, 0f)
            }

    }

    override suspend fun getPendingFuelTransactionsForPump(
        deviceId: Int,
        status: List<TransactionStatus>?
    ): SaleTransaction? {
        val txn = getTransactions(
            status = listOf(
                TransactionStatus.FuelAuthorized,
                TransactionStatus.FuelDispensed
            )
        )
            .filterIsInstance<SaleTransaction>()
            .find {
                val itemWithFuel = it.txnItems.filterIsInstance<TransactionItemWithFuel>()
                    .find { itemLine -> itemLine.preFuel.deviceId == deviceId }
                itemWithFuel != null
            }
        return txn
    }
}
