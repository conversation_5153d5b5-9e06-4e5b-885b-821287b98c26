package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = TransactionTypeSerializer::class)
sealed class TransactionType(val tender: String) {
    object Sale : TransactionType("SALE")
    object Refund : TransactionType("REFUND")
    object Void : TransactionType("VOID")
    object SaleRefund : TransactionType("SALEREFUND")
    object NoSale : TransactionType("NOSALE")
    object Payout : TransactionType("PAYOUT")
    object CashWithdrawal : TransactionType("CASH_WITHDRAWAL")
    object CashDeposit : TransactionType("CASH_DEPOSIT")
    object AccountEntry : TransactionType("ACCOUNT_ENTRY")

    override fun toString(): String = tender
}

object TransactionTypeSerializer : KSerializer<TransactionType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("TransactionType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TransactionType) {
        encoder.encodeString(value.tender)
    }

    override fun deserialize(decoder: Decoder): TransactionType {
        return when (val tender = decoder.decodeString().uppercase()) {
            TransactionType.Sale.tender -> TransactionType.Sale
            TransactionType.Refund.tender -> TransactionType.Refund
            TransactionType.Void.tender -> TransactionType.Void
            TransactionType.NoSale.tender -> TransactionType.NoSale
            TransactionType.Payout.tender -> TransactionType.Payout
            TransactionType.SaleRefund.tender -> TransactionType.SaleRefund
            TransactionType.CashWithdrawal.tender -> TransactionType.CashWithdrawal
            TransactionType.CashDeposit.tender -> TransactionType.CashDeposit
            TransactionType.AccountEntry.tender -> TransactionType.AccountEntry
            else -> throw IllegalArgumentException("Unknown TransactionType: $tender")
        }
    }
}
