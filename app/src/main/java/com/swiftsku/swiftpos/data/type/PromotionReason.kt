package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = PromotionReasonSerializer::class)
sealed class PromotionReason(val reason: String) {
    object CombinationOffer : PromotionReason("COMBINATIONOFFER")
    object FuelDiscountCarWash : PromotionReason("FUELSDISCCARWASH")
    object MixAndMatchOffer : PromotionReason("MIXANDMATCHOFFER")
    object TempPriceChange : PromotionReason("TEMPPRICECHANGE")
}

object PromotionReasonSerializer : KSerializer<PromotionReason> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("PosCodeFormatType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: PromotionReason) {
        encoder.encodeString(value.reason)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): PromotionReason {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            "COMBINATIONOFFER" -> PromotionReason.CombinationOffer
            "FUELSDISCCARWASH" -> PromotionReason.FuelDiscountCarWash
            "MIXANDMATCHOFFER" -> PromotionReason.MixAndMatchOffer
            "TEMPPRICECHANGE" -> PromotionReason.TempPriceChange
            else -> throw IllegalArgumentException("Unknown PromotionReason: $format")
        }
    }
}