package com.swiftsku.swiftpos.data.remote.repositories.payment

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.orhanobut.logger.Logger
import com.pax.poslinksemiintegration.batch.BatchCloseRequest
import com.pax.poslinksemiintegration.batch.BatchCloseResponse
import com.pax.poslinksemiintegration.transaction.DoCreditRequest
import com.pax.poslinksemiintegration.transaction.DoCreditResponse
import com.pax.poslinksemiintegration.transaction.DoEbtRequest
import com.pax.poslinksemiintegration.transaction.DoEbtResponse
import com.swiftsku.swiftpos.data.model.PaymentResponse
import com.swiftsku.swiftpos.data.model.RefundResponse
import com.swiftsku.swiftpos.data.model.SgCreds
import com.swiftsku.swiftpos.data.model.extractResponse
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PaymentApolloClient
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.orNil
import com.swiftsku.swiftpos.payment.FetchSgCredsQuery
import com.swiftsku.swiftpos.payment.RefundProcessorMutation
import com.swiftsku.swiftpos.payment.SecuredPaymentProcessorMutation
import com.swiftsku.swiftpos.payment.SecuredPaymentRefunderMutation
import com.swiftsku.swiftpos.payment.type.RIM
import com.swiftsku.swiftpos.payment.type.TIM
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.MAX_ATTEMPTS_REACHED
import com.swiftsku.swiftpos.utils.Result
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import java.net.SocketTimeoutException
import javax.inject.Inject

class ApolloPaymentRepository @Inject constructor(
    @PaymentApolloClient private val apolloClient: ApolloClient,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val paxPaymentService: PaxPaymentService
) : PaymentRepository {

    override suspend fun securedPaymentProcessor(
        eventEpoch: Int,
        tData: TIM,
        uid: String,
        autoRetry: Boolean
    ): Flow<Result<PaymentResponse>> = flow {



        suspend fun execute() = apolloClient.mutation(
            SecuredPaymentProcessorMutation(
                eventEpoch,
                tData,
                uid
            )
        ).execute()


        var attemptsMade = 0

        var timeoutTries = 0

        emit(Result.Loading)

        var infoMsg = "Retries exhausted"

        do {


            val response = try {
                execute()
            } catch (e: ApolloNetworkException) {
                EventUtils.recordException(e)
                Logger.d("ERROR  inside: ${e.message}")
                if (e.cause is SocketTimeoutException) {
                    timeoutTries++
                    attemptsMade = 0
                }

                if (timeoutTries > 2) {
                    emit(Result.Error("Timeout Error", retrying = false, retryCount = attemptsMade))
                    break
                }


                continue
            }

            if (response.hasErrors()) {
                var errorMessage = (response.errors
                    ?.map { error -> error.message }
                    ?.reduce { acc, s -> acc + s }
                    ?: "Payment Failed")
                errorMessage = if (autoRetry) {
                    attemptsMade++
                    ""
                } else {
                    errorMessage
                }
                emit(
                    Result.Error(
                        errorMessage,
                        retrying = autoRetry,
                        retryCount = attemptsMade
                    )
                )
            } else {
                val paymentResponse =
                    response.data?.securedPaymentProcessor?.responseMessage?.extractResponse()
                if (paymentResponse == null || !paymentResponse.status) {
                    val errorCode = paymentResponse?.payFacInfo?.errCode
                    var errorMessage = paymentResponse?.payFacInfo?.errMsg ?: "Payment Failed"
                    infoMsg = paymentResponse?.payFacInfo?.infoMsg ?: infoMsg
                    if (errorCode != MAX_ATTEMPTS_REACHED && autoRetry) {
                        attemptsMade++
                        errorMessage = ""
                    }

                    emit(
                        Result.Error(
                            errorMessage,
                            errorCode = errorCode,
                            retrying = errorCode != MAX_ATTEMPTS_REACHED && autoRetry,
                            retryCount = attemptsMade
                        )
                    )
                    if (errorCode == MAX_ATTEMPTS_REACHED) {
                        break
                    }
                } else {
                    emit(Result.Success(paymentResponse))
                    break
                }

            }


            if (autoRetry && attemptsMade < 3) {
                delay(2000)
            }


        } while (autoRetry && attemptsMade < 3)

        if (attemptsMade >= 3) {
            emit(Result.Error(infoMsg, retrying = false, retryCount = attemptsMade))
        }


    }.flowOn(coroutineDispatcher).catch {
        EventUtils.recordException(it)
        Logger.d("ERROR : ${it.message}")
        emit(Result.Error(it.message ?: "Payment Failed"))
    }

    override suspend fun securedPaymentRefunder(
        eventEpoch: Int,
        tData: RIM,
        uid: String
    ): Flow<Result<PaymentResponse>> = flow {

        suspend fun execute() = apolloClient.mutation(
            SecuredPaymentRefunderMutation(
                eventEpoch,
                tData,
                uid
            )
        ).execute()


        emit(Result.Loading)

        val response = execute()
        if (response.hasErrors()) {
            val errorMessage = (response.errors
                ?.map { error -> error.message }
                ?.reduce { acc, s -> acc + s }
                ?: "Refund Failed")

            emit(Result.Error(errorMessage))
        } else {
            val paymentResponse =
                response.data?.securedPaymentRefunder?.responseMessage?.extractResponse()
            if (paymentResponse == null || !paymentResponse.status) {
                val errorCode = paymentResponse?.payFacInfo?.errCode
                val errorMessage = paymentResponse?.payFacInfo?.errMsg ?: "Refund Failed"
                emit(Result.Error(errorMessage, errorCode = errorCode))

            } else {
                emit(Result.Success(paymentResponse))
            }
        }


    }.flowOn(coroutineDispatcher).catch {
        EventUtils.recordException(it)
        emit(Result.Error(it.message ?: "Refund Failed"))
    }

    override suspend fun refundProcessor(
        eventEpoch: Int,
        tData: RIM,
        uid: String
    ): Flow<Result<RefundResponse>> = flow {

        suspend fun execute() = apolloClient.mutation(
            RefundProcessorMutation(
                eventEpoch,
                tData,
                uid
            )
        ).execute()

        emit(Result.Loading)

        val response = execute()
        if (response.hasErrors()) {
            val errorMessage = (response.errors
                ?.map { error -> error.message }
                ?.reduce { acc, s -> acc + s }
                ?: "Refund Failed")

            emit(Result.Error(errorMessage))
        } else {
            val res = response.data?.refundProcessor?.responseMessage?.extractResponse()
            var errorMessage = "Refund Failed"
            if (res == null || res.err.isTrue()) {
                res?.note?.let { errorMessage = it }
                emit(Result.Error(errorMessage))
            } else {
                emit(Result.Success(res))
            }
        }
    }.flowOn(coroutineDispatcher).catch {
        EventUtils.recordException(it)
        emit(Result.Error(it.message ?: "Refund Failed"))
    }

    override suspend fun fetchSgCreds(posid: String): Result<SgCreds> {
        return withContext(coroutineDispatcher) {
            try {
                val response = apolloClient.query(
                    FetchSgCredsQuery(
                        posid
                    )
                ).execute()

                if (response.hasErrors()) {
                    return@withContext Result.Error(response.errors
                        ?.map { error -> error.message }
                        ?.reduce { acc, s -> acc + s }
                        ?: "Fetch SG Failed")
                } else {
                    val sgCredsResponse = response.data?.extractResponse()

                    if (sgCredsResponse == null || !sgCredsResponse.status) {
                        return@withContext Result.Error("Fetch SG Failed")
                    } else {
                        return@withContext Result.Success(sgCredsResponse.data)
                    }
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext Result.Error(e.message ?: "Fetch SG Failed")
            }
        }
    }

    override suspend fun sendEpxCreditRequest(request: DoCreditRequest): Result<DoCreditResponse?> {
        return try {
            paxPaymentService.sendCreditRequest(request)
        } catch (e: Exception) {
            Result.Error(e.message ?: "Payment Failed")
        }
    }

    override suspend fun sendEpxEbtRequest(request: DoEbtRequest): Result<DoEbtResponse?> {
        return try {
            paxPaymentService.sendEbtRequest(request)
        } catch (e: Exception) {
            Result.Error(e.message ?: "Payment Failed")
        }
    }

    override suspend fun sendEpxBatchClose(request: BatchCloseRequest): Result<BatchCloseResponse?> {
        return try {
            paxPaymentService.sendEpxBatchClose(request)
        } catch (e: Exception) {
            Result.Error(e.message ?: "Failed to close batch")
        }
    }
}