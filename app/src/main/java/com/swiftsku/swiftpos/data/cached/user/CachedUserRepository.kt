package com.swiftsku.swiftpos.data.cached.user

import com.swiftsku.swiftpos.data.cached.ICacheManager
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.User
import com.swiftsku.swiftpos.di.qualifiers.RemoteUserRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CachedUserRepository @Inject constructor(
    @RemoteUserRepository private val userRepository: UserRepository
) : UserRepository, ICacheManager {
    private var storeUsers: StoreUsers? = null
    private var user: User? = null
    override suspend fun fetchUserConfig(email: String): User? {
        if (user == null || user!!.email != email) {
            user = userRepository.fetchUserConfig(email)
        }
        return user
    }

    override suspend fun fetchStoreUsers(storeCode: String): StoreUsers? {
        if(storeUsers == null || storeUsers!!.storeCode.isEmpty() || !storeUsers!!.storeCode.contains(storeCode)) {
            storeUsers = userRepository.fetchStoreUsers(storeCode)
        }
        return storeUsers
    }

    override suspend fun getStoreConfigStream(storeCode: String): Flow<StoreUsers?> {
        return userRepository.getStoreConfigStream(storeCode)
    }

    fun getUser(): User? = user

    fun setUser(user: User?) {
        this.user = user
    }

    fun getStoreUses(): StoreUsers? = storeUsers

    override suspend fun clear() {
        user = null
        storeUsers = null
    }

}