package com.swiftsku.swiftpos.data.couchbase.user

import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.User
import kotlinx.coroutines.flow.Flow

interface UserRepository {
    suspend fun fetchUserConfig(email: String): User?

    suspend fun fetchStoreUsers(storeCode: String): StoreUsers?

    suspend fun getStoreConfigStream(storeCode: String): Flow<StoreUsers?>
}