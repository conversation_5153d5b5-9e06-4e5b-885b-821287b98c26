package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = TransactionItemLineTypeSerializer::class)
sealed class TransactionItemLineType(val status: String) {
    object Item : TransactionItemLineType("ITEM")
    object Merchandise : TransactionItemLineType("MERCHANDISE")
    object Fuel : TransactionItemLineType("FUEL")

    override fun toString(): String {
        return status
    }
}

object TransactionItemLineTypeSerializer : KSerializer<TransactionItemLineType> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("PosCodeFormatType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TransactionItemLineType) {
        encoder.encodeString(value.status)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): TransactionItemLineType {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            TransactionItemLineType.Item.status -> TransactionItemLineType.Item
            TransactionItemLineType.Merchandise.status -> TransactionItemLineType.Merchandise
            TransactionItemLineType.Fuel.status -> TransactionItemLineType.Fuel
            else -> throw IllegalArgumentException("Unknown TransactionItemLineType: $format")
        }
    }
}