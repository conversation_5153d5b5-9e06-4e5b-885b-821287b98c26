package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.HistoricalBatchReportsQuery
import com.swiftsku.swiftpos.payment.SalesSummaryReportQuery
import kotlinx.serialization.Serializable

@Serializable
data class HistoricalBatchReport(
    override val storeCode: String,
    override val stats: List<ReportDetail>
) : SalesSummaryData()


fun SalesSummaryReportQuery.Data.extractHistoricalBatchReportResponse(): HistoricalBatchReport? {
    val salesSummaryData = this.salesSummaryData
    if (salesSummaryData != null) {
        return HistoricalBatchReport(
            stats = salesSummaryData.stats?.filterNotNull()?.map { stat ->
                ReportDetail(
                    stat.terminalId,
                    stat.terminalData
                )
            } ?: listOf(),
            storeCode = salesSummaryData.storeCode
        )
    }
    return null
}

@Serializable
data class HistoricalBatchReportDetail(
    val batchId: String?,
    val terminal: String?,
    val batchStart: String?,
    val batchEnd: String?,
    val batchEndCashier: String?
)

@Serializable
data class HistoricalBatchReportList(
    val data: List<HistoricalBatchReportDetail>,
    val terminals: List<String> = listOf("All Terminals"),
)

fun HistoricalBatchReportsQuery.Data.extractHistoricalBatchReportListResponse(): HistoricalBatchReportList? {
    val historicalBatches = this.historicalBatches
    if (historicalBatches != null) {
        return HistoricalBatchReportList(
            data = historicalBatches.data?.filterNotNull()?.map { batch ->
                HistoricalBatchReportDetail(
                    batch.batchId,
                    batch.terminal,
                    batch.batchStart,
                    batch.batchEnd,
                    batch.batchEndCashier,
                )
            } ?: listOf(),
            terminals = historicalBatches.terminals?.filterNotNull() ?: listOf("All Terminals")
        )
    }
    return null
}
