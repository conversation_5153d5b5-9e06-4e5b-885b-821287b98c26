package com.swiftsku.swiftpos.data.local.datastore.notification

import androidx.datastore.core.DataStore
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext


class NotificationsProtoDataSource(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val dataStore: DataStore<NotificationsProto>
) : NotificationsDataSource {

    override fun getReadNotifications(userId: String): Flow<List<String>> =
        dataStore.data.map { proto ->
            proto.userNotificationsMap[userId]?.notificationsList ?: emptyList()
        }

    override suspend fun addReadNotification(userId: String, notificationId: String) {
        withContext(dispatcher) {
            dataStore.updateData { current ->
                val existing =
                    current.userNotificationsMap[userId]?.notificationsList ?: emptyList()
                if (notificationId in existing) return@updateData current

                val updatedUser = UserNotifications.newBuilder()
                    .addAllNotifications(existing + notificationId)
                    .build()

                current.toBuilder()
                    .putUserNotifications(userId, updatedUser)
                    .build()
            }
        }
    }
}
