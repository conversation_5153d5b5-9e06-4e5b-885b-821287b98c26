package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class VoidTransaction(
    override val txnId: String,
    override val txnItems: List<TransactionItem>,
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date?,
    override val txnStatus: TransactionStatus,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    override val cashierId: String,
    override val txnType: TransactionType = TransactionType.Void,
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date = Date(),
    val txnTotalGrandAmount: Float = 0f,
    val txnTotalTaxNetAmount: Float = 0f,
    val txnTotalGrossAmount: Float = 0f,
    val txnTotalNetAmount: Float = 0f,
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    override var paymentRecord: PaymentRecord? = null
) : Transaction()
