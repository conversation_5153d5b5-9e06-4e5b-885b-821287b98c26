package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


enum class EbtType(val value: String) {
    FOOD_STAMP("Food Stamp"), CASH_BENEFIT("Cash Benefit"),
    INQUIRY_CASH("Cash Benefit Bal."), INQUIRY_FOOD("Food Stamp Bal.")
}

@Serializable
data class PinpadConfig(
    val ctype: PinPadConnectionType = PinPadConnectionType.USB,
    val ip: String = "",
    val port: String? = PaxPaymentService.DEFAULT_PORT,
    @SerialName("batch_close_time")
    val batchCloseTime: String? = "21:30",
    @SerialName("batch_close_enabled")
    val batchCloseEnabled: Boolean? = null,
    @SerialName("batch_close_on_logout")
    val batchCloseOnLogout: Boolean? = null,
    @SerialName("auto_batch_close_on_logout")
    val autoBatchCloseOnLogout: Boolean? = null
)

enum class PinPadConnectionType(val value: String) {
    USB("usb"), TCP("tcp")
}

fun String?.toPinPadConnectionType(): PinPadConnectionType {
    return try {
        PinPadConnectionType.valueOf(this ?: PinPadConnectionType.USB.value)
    } catch (ex: Exception) {
        EventUtils.recordException(ex)
        PinPadConnectionType.USB
    }
}