package com.swiftsku.swiftpos.data.couchbase.promotion

import com.swiftsku.swiftpos.data.model.ILTDetail
import com.swiftsku.swiftpos.data.model.MMTDetail
import java.util.Date

interface PromotionsRepository {
    suspend fun getMMTDetails(startDate: Date, endDate: Date, storeId: String): List<MMTDetail>
    suspend fun getILTDetails(startDate: Date, endDate: Date, storeId: String): List<ILTDetail>
}