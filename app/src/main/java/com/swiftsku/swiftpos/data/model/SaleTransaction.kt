package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.StatusReason
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class SaleTransaction(
    override val txnId: String,
    override val txnItems: List<TransactionItem> = emptyList(),
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date?,
    val txnTotalGrandAmount: Float = 0f,
    val txnTotalTaxNetAmount: Float = 0f,
    val txnTotalGrossAmount: Float = 0f,
    val txnTotalNetAmount: Float = 0f,
    override val txnType: TransactionType = TransactionType.Sale,
    override val txnStatus: TransactionStatus,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    val dob: String? = null,
    override val cashierId: String,
    val txnPayment: Map<TxnPaymentType, TxnPayment> = emptyMap(),
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date = Date(),
    val accountInfo: LoyaltyAccountInfo? = null,
    val txnDiscount: TxnDiscount? = null,
    val lotteryPayout: LotteryPayout? = null,
    val lotteryPayouts: List<LotteryPayout>? = null,
    val cardInfo: CardInfo? = null,
    val fuelMeta: FuelMeta? = null,
    val ebtInfo: EbtInfo? = null,
    val statusReason: StatusReason? = StatusReason.INITIAL,
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    val refundRes: RefundResponse? = null,
    override var paymentRecord: PaymentRecord? = null,
    val appliedFees: List<AppliedFee>? = emptyList(),
    val txnLabel: String = "",
) : Transaction()


fun SaleTransaction.coupon(): Float =
    this.txnDiscount?.coupon?.sumOfFloats { it.amount } ?: 0f

fun SaleTransaction.lottery(): Float = this.lotteryPayouts?.sumOfFloats { it.amount } ?: 0f

fun SaleTransaction.promotion(): Float =
    this.txnItems.sumOfFloats { itemLine ->
        itemLine.promotion?.sumOfFloats { promotion -> promotion.promotionAmount } ?: 0f
    }


fun SaleTransaction.fuelAmount(): Double {
    return if (this.txnStatus == TransactionStatus.FuelDispensed || this.txnStatus == TransactionStatus.Complete) {
        postFuelAmount()
    } else {
        preFuelAmount()
    }
}

val SaleTransaction.fuelVolume
    get() : Double = this.txnItems
        .filterIsInstance<TransactionItemWithFuel>()
        .filter { it.status == TransactionItemStatus.Normal }
        .sumOf { it.postFuel?.volume ?: 0.0 }

fun SaleTransaction.paymentMode(): String {
    val cashPayment = (txnPayment[TxnPaymentType.Cash] as? CashPayment)
    val cardPayment = (txnPayment[TxnPaymentType.Card] as? CardPayment)
    val chequePayment = (txnPayment[TxnPaymentType.EBT] as? ChequePayment)
    val ebtPayment = (txnPayment[TxnPaymentType.EBT] as? EBTPayment)
    val creditPayment = (txnPayment[TxnPaymentType.Credit] as? CreditPayment)
    val sb = StringBuilder()
    cashPayment?.let { sb.append("Cash") }
    cardPayment?.let { if (sb.isNotEmpty()) sb.append(", Card") else sb.append("Card") }
    chequePayment?.let { if (sb.isNotEmpty()) sb.append(", Cheque") else sb.append("Cheque") }
    ebtPayment?.let { if (sb.isNotEmpty()) sb.append(", EBT") else sb.append("EBT") }
    creditPayment?.let { if (sb.isNotEmpty()) sb.append(", Credit") else sb.append("Credit") }
    if (sb.isEmpty()) {
        if (hasPostFuel) {
            sb.append("Payment pending")
        } else {
            sb.append("Fuel pending")
        }
    }
    return sb.toString()
}

fun SaleTransaction.saleOrigin(): String {
    return if (this.fuelMeta?.outsideSale.isTrue()) "Pump" else "POS"
}

val SaleTransaction.showRecall: Boolean get() = txnStatus == TransactionStatus.FuelDispensed && allFuelDispensed()

val SaleTransaction.showReprint: Boolean get() = txnStatus == TransactionStatus.Complete

fun SaleTransaction.postFuelAmount() = this.txnItems.filterIsInstance<TransactionItemWithFuel>()
    .filter { it.status == TransactionItemStatus.Normal }.sumOf { it.postFuel?.amount ?: 0.0 }

fun SaleTransaction.preFuelAmount() = this.txnItems.filterIsInstance<TransactionItemWithFuel>()
    .filter { it.status == TransactionItemStatus.Normal }.sumOf { it.preFuel.amount }

fun SaleTransaction.allFuelDispensed(): Boolean {
    return txnItems
        .filterIsInstance<TransactionItemWithFuel>()
        .filter { it.status == TransactionItemStatus.Normal }
        .all { it.postFuel != null }
}

fun convertToSaleTransaction(
    transaction: Transaction,
    txnSummary: TransactionSummary,
): SaleTransaction {
    if (transaction is SaleTransaction) return transaction
    return SaleTransaction(
        txnEndTime = transaction.txnEndTime,
        txnStartTime = transaction.txnStartTime,
        txnTotalGrandAmount = txnSummary.transactionTotalGrandAmount,
        txnTotalGrossAmount = txnSummary.transactionTotalGrossAmount,
        txnTotalNetAmount = txnSummary.transactionTotalNetAmount,
        txnTotalTaxNetAmount = txnSummary.transactionTotalTaxNetAmount,
        cashierId = transaction.cashierId,
        txnStatus = transaction.txnStatus,
        txnItems = transaction.txnItems,
        txnId = transaction.txnId,
        statusHistory = transaction.statusHistory
    )
}

fun SaleTransaction.convertToRefundTransaction(
    refundReason: String, linkedTxnId: String
): RefundTransaction {
    return RefundTransaction(
        txnEndTime = txnEndTime,
        txnStartTime = txnStartTime,
        txnTotalGrandAmount = txnTotalGrandAmount,
        txnTotalGrossAmount = txnTotalGrossAmount,
        txnTotalNetAmount = txnTotalNetAmount,
        txnTotalTaxNetAmount = txnTotalTaxNetAmount,
        cashierId = cashierId,
        txnStatus = txnStatus,
        txnItems = txnItems,
        txnId = txnId,
        refundInfo = RefundInfo(linkedTxnId = linkedTxnId, reason = refundReason),
        txnPayment = txnPayment,
        statusHistory = statusHistory
    )
}

data class AmountsToBePaid(
    val cardAmount: Float,
    val creditAmount: Float,
    val cashAmount: Float,
    val cashChange: Float,
)

fun getAmountsToBePaidByMop(
    card: CardPayment?,
    credit: CreditPayment?,
    cash: CashPayment?,
    amountToPay: Float
): AmountsToBePaid {
    var remainingToPay = amountToPay.to2Decimal()

    val cardUsed = minOf(card?.amount ?: 0f, remainingToPay)
    remainingToPay -= cardUsed

    val creditUsed = minOf(credit?.amount ?: 0f, remainingToPay)
    remainingToPay -= creditUsed

    val cashTendered = cash?.tender ?: 0f
    val cashUsed = minOf(cashTendered, remainingToPay)
    val cashChange = (cashTendered - cashUsed).coerceAtLeast(0f)

    return AmountsToBePaid(
        cardAmount = cardUsed,
        creditAmount = creditUsed,
        cashAmount = cashUsed,
        cashChange = cashChange
    )
}

fun getUpdatedTxnPayment(txn: SaleTransaction, amountsToBePaid: AmountsToBePaid): SaleTransaction {
    val cashPayment = (txn.txnPayment[TxnPaymentType.Cash] as? CashPayment)
    val creditPayment = (txn.txnPayment[TxnPaymentType.Credit] as? CreditPayment)
    return txn.copy(
        txnPayment = txn.txnPayment.toMutableMap().apply {
            cashPayment?.let {
                this[TxnPaymentType.Cash] = it.copy(change = amountsToBePaid.cashChange)
            }
            creditPayment?.let {
                if (amountsToBePaid.creditAmount == 0f) {
                    this.remove(TxnPaymentType.Credit)
                } else {
                    this[TxnPaymentType.Credit] = it.copy(amount = amountsToBePaid.creditAmount)
                }
            }
        }
    )
}

fun SaleTransaction.hasOnlyCreditPayment(): Boolean {
    val cashPayment = (txnPayment[TxnPaymentType.Cash] as? CashPayment)
    val cardPayment = (txnPayment[TxnPaymentType.Card] as? CardPayment)
    val chequePayment = (txnPayment[TxnPaymentType.EBT] as? ChequePayment)
    val ebtPayment = (txnPayment[TxnPaymentType.EBT] as? EBTPayment)
    val creditPayment = (txnPayment[TxnPaymentType.Credit] as? CreditPayment)
    return creditPayment != null && cashPayment == null && cardPayment == null && chequePayment == null && ebtPayment == null
}

fun SaleTransaction.hasCardPayment(): Boolean {
    return (txnPayment[TxnPaymentType.Card] as? CardPayment) != null
}