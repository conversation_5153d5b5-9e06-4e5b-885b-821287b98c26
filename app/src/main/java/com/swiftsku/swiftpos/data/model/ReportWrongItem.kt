package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.ReportAction
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ReportWrongItem(
    @SerialName("doc_type")
    val docType: String = "gupc_correction_item",
    val action: ReportAction = ReportAction.Correction,
    val processed: Boolean = false,
    val current: PluItem,
)
