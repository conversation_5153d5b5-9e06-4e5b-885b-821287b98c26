package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
sealed class Report {
    abstract val store: String

    @SerialName("doc_type")
    val docType: String = "report_item"

    @SerialName("store_code")
    abstract val storeCode: String
}

@Serializable
data class EODReport(
    val bmp: Map<String, String> = emptyMap(),
    override val store: String,
    @SerialName("store_code")
    override val storeCode: String,
) : Report()

