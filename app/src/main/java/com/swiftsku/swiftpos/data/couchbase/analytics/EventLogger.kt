package com.swiftsku.swiftpos.data.couchbase.analytics

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder


interface EventLogger {

    suspend fun barcodeScan(scanned: String, converted: String)
}


@Serializable(with = AnalyticEventSerializer::class)
sealed class AnalyticEvent(val event: String) {
    object BarcodeScan : AnalyticEvent("barcode_scan")
}

object AnalyticEventSerializer : KSerializer<AnalyticEvent> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("AnalyticEvent", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: AnalyticEvent) {
        encoder.encodeString(value.event)
    }

    override fun deserialize(decoder: Decoder): AnalyticEvent {
        return when (val format = decoder.decodeString()) {
            AnalyticEvent.BarcodeScan.event -> AnalyticEvent.BarcodeScan
            else -> throw IllegalArgumentException("Unknown AnalyticEvent: $format")
        }
    }
}