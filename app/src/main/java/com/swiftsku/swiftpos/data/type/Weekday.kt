package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = WeekdaySerializer::class)
sealed class Weekday(val day: String) {
    object Sunday : Weekday("SUNDAY")
    object Monday : Weekday("MONDAY")
    object Tuesday : Weekday("TUESDAY")
    object Wednesday : Weekday("WEDNESDAY")
    object Thursday : Weekday("THURSDAY")
    object Friday : Weekday("FRIDAY")
    object Saturday : Weekday("SATURDAY")
}




object WeekdaySerializer : KSerializer<Weekday> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("Weekday", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: Weekday) {
        encoder.encodeString(value.day)
    }

    override fun deserialize(decoder: Decoder): Weekday {
        return when (val day = decoder.decodeString().uppercase()) {
            "SUNDAY" -> Weekday.Sunday
            "MONDAY" -> Weekday.Monday
            "TUESDAY" -> Weekday.Tuesday
            "WEDNESDAY" -> Weekday.Wednesday
            "THURSDAY" -> Weekday.Thursday
            "FRIDAY" -> Weekday.Friday
            "SATURDAY" -> Weekday.Saturday
            else -> throw IllegalArgumentException("$day is not a valid Weekday")
        }
    }
}
