package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = PosCodeFormatCheckDigitSerializer::class)
sealed class PosCodeFormatCheckDigit(val checkDigit: String) {
    object Present : PosCodeFormatCheckDigit("PRESENT")
    object Absent : PosCodeFormatCheckDigit("ABSENT")
}


object PosCodeFormatCheckDigitSerializer : KSerializer<PosCodeFormatCheckDigit> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("PosCodeFormatCheckDigit", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: PosCodeFormatCheckDigit) {
        encoder.encodeString(value.checkDigit)
    }

    override fun deserialize(decoder: Decoder): PosCodeFormatCheckDigit {
        return when (val checkDigit = decoder.decodeString()) {
            "PRESENT" -> PosCodeFormatCheckDigit.Present
            "ABSENT" -> PosCodeFormatCheckDigit.Absent
            else -> throw IllegalArgumentException("$checkDigit is not a valid PosCodeFormatCheckDigit")
        }
    }
}