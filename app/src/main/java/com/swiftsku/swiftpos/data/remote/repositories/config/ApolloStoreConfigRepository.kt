package com.swiftsku.swiftpos.data.remote.repositories.config

import com.apollographql.apollo3.ApolloClient
import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.data.model.PresetConfig
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.TerminalConfig
import com.swiftsku.swiftpos.data.model.extractResponse
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PaymentApolloClient
import com.swiftsku.swiftpos.payment.FetchTerminalConfigQuery
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ApolloStoreConfigRepository @Inject constructor(
    @PaymentApolloClient private val apolloClient: ApolloClient,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
) : StoreRepository {
    override suspend fun fetchStoreConfig(serialKey: String): StoreConfig? {
        return null
    }

    override suspend fun storeConfigFlow(serialKey: String): Flow<StoreConfig> = flow { }

    override suspend fun presetConfigFlow(storeCode: String): Flow<PresetConfig> = flow { }

    override suspend fun fetchTerminalConfig(posId: String): TerminalConfig? =
        withContext(coroutineDispatcher) {
            try {
                val response = apolloClient.query(
                    FetchTerminalConfigQuery(
                        posId
                    )
                ).execute()

                val fetchTerminalConfig =
                    response.data?.fetchTerminalConfig ?: return@withContext null
                if (!response.hasErrors()) {
                    return@withContext fetchTerminalConfig.extractResponse()
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
            null
        }
}