package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.PluFormatType
import com.swiftsku.swiftpos.data.type.PluFormatTypeSerializer
import com.swiftsku.swiftpos.data.type.PluItemProps
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PluMap(
    val plumap: Map<String, PluItem>,
)

@Serializable
data class PluItem(
    val pluId: String,
    @Serializable(with = PluFormatTypeSerializer::class)
    val pluFormat: PluFormatType,
    val pluModifier: String,
    val description: String,
    val taxIds: List<String> = emptyList(),
    val minimumCustomerAge: Int? = 0,
    val merchandiseCode: String = "000",
    val price: Float = 0f,
    val sellingUnits: Float = 1f,
    val showInDashboard: Boolean = false,
    val props: List<PluItemProps> = emptyList(),
    val imgUrl: String? = null,
    val department: String? = null,
    val linkedPluData: List<LinkedPluData>? = null
)

fun PluItem.UID(): String = "${this.pluId}-${this.pluModifier}"

fun PluItem.isEBT(): Boolean = props.contains(PluItemProps.FoodStamp)

fun PluItem.applyEBT(): PluItem {
    var previousProps = props
    if (!previousProps.contains(PluItemProps.FoodStamp)) {
        previousProps = previousProps.plus(PluItemProps.FoodStamp)
    }
    return this.copy(props = previousProps.plus(PluItemProps.FoodStamp))
}

@Serializable
data class PLUItemDTO(
    @SerialName("plumap")
    val pluMap: Map<String, PluItem>,
)

