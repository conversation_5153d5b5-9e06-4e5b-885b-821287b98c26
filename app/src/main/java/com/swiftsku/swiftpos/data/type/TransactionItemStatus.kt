package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = TransactionItemStatusSerializer::class)
sealed class TransactionItemStatus(val status: String) {
    object Deleted : TransactionItemStatus("DELETED")
    object Normal : TransactionItemStatus("NORMAL")

    override fun toString(): String {
        return status
    }
}

object TransactionItemStatusSerializer : KSerializer<TransactionItemStatus> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("PosCodeFormatType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: TransactionItemStatus) {
        encoder.encodeString(value.status)  // Use the 'format' property
    }

    override fun deserialize(decoder: Decoder): TransactionItemStatus {
        val format = decoder.decodeString()
        return when (format.uppercase()) {
            "DELETED" -> TransactionItemStatus.Deleted
            "NORMAL" -> TransactionItemStatus.Normal
            else -> throw IllegalArgumentException("Unknown TransactionItemStatus: $format")
        }
    }
}