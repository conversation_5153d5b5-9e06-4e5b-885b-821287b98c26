package com.swiftsku.swiftpos.data.type

sealed class NumPadResult(val value: String) {
    object EditPluQuantity : NumPadResult("EditPluQuantity")
    object AddDepartmentItem : NumPadResult("AddDepartmentItem")
    object EditDepartmentPrice : NumPadResult("EditDepartmentPrice")
    object AgeValidation : NumPadResult("AgeValidation")
    object CashTransaction : NumPadResult("CashTransaction")
    object CashCountInDrawer : NumPadResult("CashCountInDrawer")
    object VendorPayout : NumPadResult("VendorPayout")
    object LotteryPayout : NumPadResult("LotteryPayout")
    object FuelPrepay : NumPadResult("FuelPrepay")
    object FuelPreset : NumPadResult("FuelPreset")
    object RestInGas : NumPadResult("RestInGas")
    object MovePrepay : NumPadResult("MovePrepay")
    object LoyaltyNumber : NumPadResult("LoyaltyNumber")
    object CashWithdrawal : NumPadResult("CashWithdrawal")
    object CashDeposit : NumPadResult("CashDeposit")
}

fun NumPadResult.showDollar(): Boolean {
    return when (this) {
        NumPadResult.AgeValidation -> false
        NumPadResult.EditPluQuantity -> false
        NumPadResult.FuelPreset -> false
        NumPadResult.MovePrepay -> false
        NumPadResult.LoyaltyNumber -> false
        else -> true
    }
}

fun NumPadResult.title(): String {
    return when (this) {
        NumPadResult.CashCountInDrawer -> "Cash in Drawer"
        NumPadResult.LotteryPayout -> "Lottery Payout"
        NumPadResult.VendorPayout -> "Vendor Payout"
        NumPadResult.AddDepartmentItem -> "Department Price"
        NumPadResult.AgeValidation -> "Enter Age"
        NumPadResult.CashTransaction -> "Enter Cash"
        NumPadResult.EditDepartmentPrice -> "Department Price"
        NumPadResult.EditPluQuantity -> "Item Quantity"
        NumPadResult.FuelPrepay -> "Fuel Prepay"
        NumPadResult.FuelPreset -> "Enter Volume"
        NumPadResult.RestInGas -> "Rest in Gas"
        NumPadResult.MovePrepay -> "Enter different pump number"
        NumPadResult.LoyaltyNumber -> "Enter Loyalty Number"
        NumPadResult.CashWithdrawal -> "Safe Drop Amount"
        NumPadResult.CashDeposit -> "Safe Loan Amount"
    }
}