package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.payment.FetchTerminalConfigQuery
import kotlinx.serialization.Serializable


@Serializable
data class TerminalConfig(val sgCreds: SgCreds)

enum class LoyaltyInput {
    CONSUMER, CASHIER, PINPAD
}

fun FetchTerminalConfigQuery.FetchTerminalConfig.extractResponse(): TerminalConfig {
    return TerminalConfig(
        sgCreds = SgCreds(
            uid = this.data?.sgCreds?.uid ?: "",
            pwd = this.data?.sgCreds?.pwd ?: ""
        )
    )
}