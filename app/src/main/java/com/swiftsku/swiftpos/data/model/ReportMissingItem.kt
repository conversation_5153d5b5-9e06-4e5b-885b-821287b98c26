package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.ReportAction
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date


@Serializable
data class ReportMissingItem(
    @SerialName("doc_type")
    val docType: String = "gupc_correction_item",
    val action: ReportAction = ReportAction.Missing,
    val processed: Boolean = false,
    val upc: String,
    @Serializable(DateSerializer::class)
    @SerialName("first_requested")
    val firstRequested: Date,
    @Serializable(DateSerializer::class)
    @SerialName("last_requested")
    val lastRequested: Date? = null,
)
