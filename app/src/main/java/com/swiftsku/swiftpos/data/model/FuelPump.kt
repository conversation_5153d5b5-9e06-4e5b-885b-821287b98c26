package com.swiftsku.swiftpos.data.model

import com.fdc.core.types.DeviceState
import com.fdc.core.types.TranTypeEnum
import com.fdc.core.types.TrueFalse
import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class FuelPump(
    val deviceId: Int,
    val pumpNo: Int,
    val deviceState: DeviceState,
    @Serializable(with = DateSerializer::class)
    val timestamp: Date,
    val lockingApplicationSender: String? = null,
    val consentNeeded: Boolean? = null,
    val customerActionNeedsConsent: Boolean? = null,
    val posTransData: POSTransData? = null
)


@Serializable
data class POSTransData(
    val tranType: TranTypeEnum? = null,
    val posTransactionData: String? = null,
    var epsStan: Double? = null,
    var merchandiseTrxAmount: Double? = null
)

val TrueFalse.value
    get() = when (this) {
        TrueFalse.TRUE -> true
        TrueFalse.FALSE -> false
    }