package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.extension.epochInSeconds
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
sealed class Fuel {
    abstract val pumpNo: Int
    abstract val deviceId: Int
    abstract val amount: Double
    abstract val epoch: Int
}

@Serializable
data class PreFuel(
    override val pumpNo: Int,
    override val amount: Double,
    override val epoch: Int = Date().epochInSeconds(),
    override val deviceId: Int,
    val volume: Double? = null,
    val unitPrice: Double? = null,
    val productNo: Long? = null
) : Fuel()

@Serializable
data class PostFuel(
    override val pumpNo: Int,
    override val amount: Double,
    override val epoch: Int = Date().epochInSeconds(),
    override val deviceId: Int,
    val volume1: Double? = null,
    val volume2: Double? = null,
    val product1: String? = null,
    val product2: String? = null,
    val productNo1: Long? = null,
    val productNo2: Long? = null,
    val blendRatio: Double? = null,
    val volume: Double,
    val unitPrice: Double? = null
) : Fuel()