package com.swiftsku.swiftpos.data.type

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = MixMatchEntryTypeSerializer::class)
sealed class MixMatchEntryType(val type: String) {
    object Amount : MixMatchEntryType("AMOUNT")
    object Percent : MixMatchEntryType("PERCENT")
    object Total : MixMatchEntryType("TOTAL")
}

object MixMatchEntryTypeSerializer : KSerializer<MixMatchEntryType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("MixMatchEntryType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: MixMatchEntryType) {
        encoder.encodeString(value.type)
    }

    override fun deserialize(decoder: Decoder): MixMatchEntryType {
        return when(val type = decoder.decodeString()){
            "AMOUNT" -> MixMatchEntryType.Amount
            "PERCENT" -> MixMatchEntryType.Percent
            "TOTAL" -> MixMatchEntryType.Total
            else -> throw IllegalArgumentException("$type is not a valid MixMatchEntryType")
        }
    }
}