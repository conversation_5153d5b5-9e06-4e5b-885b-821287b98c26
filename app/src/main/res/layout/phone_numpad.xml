<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white">


    <EditText
        android:id="@+id/etPhone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="@+id/gridLayout"
        app:layout_constraintStart_toStartOf="@+id/gridLayout"
        app:layout_constraintTop_toTopOf="parent" />

    <GridLayout
        android:id="@+id/gridLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:backgroundTint="@android:color/white"
        android:columnCount="3"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etPhone">

        <Button
            android:id="@+id/bt7"
            android:backgroundTint="@color/gray"
            android:text="7"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt8"

            android:backgroundTint="@color/gray"
            android:text="8"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt9"

            android:backgroundTint="@color/gray"
            android:text="9"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt4"

            android:backgroundTint="@color/gray"
            android:text="4"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt5"

            android:backgroundTint="@color/gray"
            android:text="5"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt6"

            android:backgroundTint="@color/gray"
            android:text="6"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt1"

            android:backgroundTint="@color/gray"
            android:text="1"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt2"

            android:backgroundTint="@color/gray"
            android:text="2"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt3"

            android:backgroundTint="@color/gray"
            android:text="3"
            android:textColor="@android:color/white" />





        <Button
            android:id="@+id/btClear"
            android:backgroundTint="@color/gray"
            android:text="Clear"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/bt0"

            android:layout_column="1"
            android:backgroundTint="@color/gray"
            android:text="0"
            android:textColor="@android:color/white" />
        <Button
            android:id="@+id/btBack"
            android:backgroundTint="@color/gray"
            android:text="Back"
            android:textColor="@android:color/white" />
        <Button
            android:id="@+id/btOk"

            android:layout_columnSpan="3"
            android:layout_gravity="fill"
            android:backgroundTint="@color/teal_button"
            android:text="OK"
            android:textColor="@android:color/white" />
    </GridLayout>
</androidx.constraintlayout.widget.ConstraintLayout>