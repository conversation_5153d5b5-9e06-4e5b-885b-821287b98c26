<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_ss_blue_border_1dp_corner_8dp"
    android:padding="12dp">

    <FrameLayout
        android:id="@+id/flProduct"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_corner_8dp"
        android:padding="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivProduct"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/ic_loyalty_24dp" />

    </FrameLayout>

    <TextView
        android:id="@+id/tvOfferText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="SwiftSku Rewards"
        android:textColor="@color/ss_blue"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/flProduct"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvProductDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="Become a member and save today"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvOfferText"
        app:layout_constraintTop_toBottomOf="@id/tvOfferText" />

</androidx.constraintlayout.widget.ConstraintLayout>