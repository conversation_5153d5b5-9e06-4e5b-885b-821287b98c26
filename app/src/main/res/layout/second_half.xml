<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvAvailableOffers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:drawableEnd="@drawable/ic_percent_discount_24dp"
        android:drawablePadding="4dp"
        android:drawableTint="@color/ss_blue"
        android:text="Special Offers"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvOffers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toTopOf="@id/ivLogo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableOffers"
        tools:listitem="@layout/offer_list_item" />

    <TextView
        android:id="@+id/tvNoOffers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:drawableEnd="@drawable/ic_percent_discount_24dp"
        android:drawablePadding="4dp"
        android:drawableTint="@color/ss_blue"
        android:text="Special Offers"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSingleOfferView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/ivLogo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableOffers"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_product"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="96dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_item_placeholder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSavingAmount" />

        <TextView
            android:id="@+id/tvOfferDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/ss_blue"
            android:textSize="42sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="BUY 2 \nFor \n$5" />

        <TextView
            android:id="@+id/tvSavingAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_green_corner_6dp"
            android:paddingHorizontal="6dp"
            android:paddingVertical="4dp"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="You Save $1.58" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/cl_loyalty_card"
        layout="@layout/loyalty_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableOffers" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogo"
        android:layout_width="120dp"
        android:layout_height="32dp"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_app_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include
        android:id="@+id/phonePad"
        layout="@layout/phone_numpad"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>