<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginTop="8dp"
    android:background="@drawable/bg_ss_blue_border_1dp_corner_8dp"
    android:padding="12dp">

    <FrameLayout
        android:id="@+id/flProduct"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_corner_8dp"
        android:padding="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivProduct"
            android:layout_width="50dp"
            android:layout_height="50dp" />

    </FrameLayout>

    <TextView
        android:id="@+id/tvOfferText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/ss_blue"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/tvSavingAmount"
        app:layout_constraintStart_toEndOf="@id/flProduct"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2 for $5" />

    <TextView
        android:id="@+id/tvProductDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@id/tvSavingAmount"
        app:layout_constraintStart_toStartOf="@id/tvOfferText"
        app:layout_constraintTop_toBottomOf="@id/tvOfferText"
        tools:text="Gatorade Cool Blue" />

    <TextView
        android:id="@+id/tvSavingAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ss_blue_corner_6dp"
        android:paddingHorizontal="6dp"
        android:paddingVertical="4dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Save $1.58" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_new_ui"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="flProduct, tvProductDescription, tvSavingAmount" />

</androidx.constraintlayout.widget.ConstraintLayout>