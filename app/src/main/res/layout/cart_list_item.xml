<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center">

    <TextView
        android:id="@+id/tvDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        android:lines="1"
        android:ellipsize="end"
        android:text="Description"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvQty"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:text="QTY"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:gravity="end"
        android:text="Amount"
        android:textColor="@color/black" />
</LinearLayout>