<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="50dp"
    android:height="50dp"
    android:viewportWidth="50"
    android:viewportHeight="50">
  <group>
    <clip-path
        android:pathData="M0,0h50v50h-50z"/>
    <path
        android:pathData="M6.688,7.97C6.831,7.97 6.974,7.969 7.117,7.968C7.508,7.967 7.899,7.967 8.291,7.968C8.713,7.969 9.136,7.967 9.558,7.966C10.386,7.965 11.213,7.965 12.04,7.965C12.712,7.965 13.385,7.965 14.058,7.965C14.153,7.965 14.249,7.965 14.348,7.965C14.542,7.964 14.737,7.964 14.932,7.964C16.756,7.963 18.58,7.963 20.404,7.964C22.072,7.965 23.74,7.964 25.408,7.962C27.122,7.959 28.836,7.958 30.549,7.959C31.511,7.959 32.473,7.959 33.434,7.958C34.253,7.956 35.072,7.956 35.89,7.958C36.308,7.959 36.725,7.959 37.143,7.957C37.525,7.956 37.908,7.956 38.291,7.958C38.429,7.959 38.566,7.958 38.704,7.957C39.969,7.949 40.87,8.167 41.788,9.061C42.646,9.97 42.72,11.043 42.712,12.245C42.712,12.372 42.713,12.498 42.713,12.628C42.714,12.971 42.713,13.314 42.712,13.656C42.711,14.016 42.711,14.376 42.711,14.736C42.711,15.34 42.71,15.943 42.709,16.546C42.707,17.243 42.706,17.941 42.707,18.638C42.707,19.31 42.707,19.983 42.706,20.655C42.705,20.941 42.705,21.226 42.705,21.512C42.706,21.91 42.704,22.308 42.702,22.706C42.703,22.884 42.703,22.884 42.703,23.065C42.703,23.173 42.702,23.281 42.701,23.393C42.701,23.487 42.701,23.581 42.701,23.678C42.674,23.939 42.622,24.097 42.48,24.316C42.183,24.465 41.929,24.437 41.602,24.414C41.346,24.235 41.312,24.13 41.211,23.828C41.202,23.654 41.198,23.479 41.198,23.305C41.197,23.142 41.197,23.142 41.197,22.976C41.197,22.857 41.197,22.738 41.197,22.615C41.196,22.422 41.196,22.23 41.195,22.037C41.195,21.83 41.195,21.623 41.194,21.416C41.194,20.983 41.192,20.551 41.19,20.119C41.19,19.971 41.189,19.823 41.188,19.675C41.188,19.601 41.188,19.527 41.188,19.45C41.183,18.444 41.18,17.438 41.179,16.432C41.178,15.753 41.176,15.074 41.172,14.396C41.17,14.037 41.169,13.678 41.17,13.319C41.17,12.918 41.168,12.517 41.165,12.116C41.166,11.998 41.166,11.88 41.167,11.758C41.159,11.017 41.089,10.43 40.545,9.879C40.489,9.844 40.433,9.808 40.375,9.772C40.318,9.735 40.262,9.697 40.203,9.659C39.854,9.47 39.564,9.46 39.175,9.46C39.055,9.459 38.935,9.459 38.811,9.459C38.677,9.459 38.544,9.459 38.41,9.459C38.268,9.459 38.126,9.458 37.985,9.458C37.595,9.457 37.205,9.457 36.814,9.457C36.394,9.457 35.974,9.456 35.553,9.455C34.634,9.454 33.714,9.454 32.794,9.453C32.22,9.453 31.646,9.452 31.072,9.452C29.484,9.451 27.895,9.45 26.306,9.449C26.205,9.449 26.103,9.449 25.998,9.449C25.845,9.449 25.845,9.449 25.689,9.449C25.483,9.449 25.276,9.449 25.069,9.449C24.916,9.449 24.916,9.449 24.759,9.449C23.098,9.449 21.437,9.447 19.776,9.445C18.071,9.442 16.367,9.441 14.662,9.441C13.705,9.441 12.748,9.44 11.79,9.439C10.975,9.437 10.16,9.437 9.345,9.438C8.929,9.438 8.513,9.438 8.097,9.436C7.716,9.435 7.335,9.435 6.954,9.436C6.817,9.437 6.679,9.436 6.541,9.436C6.354,9.434 6.166,9.435 5.979,9.436C5.875,9.436 5.771,9.436 5.664,9.436C5.074,9.51 4.673,9.807 4.297,10.254C3.999,10.816 3.962,11.275 3.966,11.904C3.965,12 3.965,12.096 3.964,12.194C3.963,12.514 3.964,12.834 3.964,13.154C3.964,13.384 3.963,13.614 3.962,13.843C3.961,14.467 3.961,15.09 3.961,15.714C3.962,16.3 3.961,16.887 3.96,17.473C3.959,18.705 3.959,19.938 3.96,21.17C3.961,22.297 3.96,23.424 3.958,24.55C3.955,25.708 3.955,26.866 3.955,28.024C3.956,28.674 3.955,29.323 3.954,29.973C3.952,30.584 3.952,31.194 3.954,31.805C3.955,32.029 3.954,32.254 3.953,32.478C3.952,32.784 3.953,33.09 3.955,33.396C3.954,33.484 3.953,33.573 3.952,33.664C3.96,34.352 4.128,34.882 4.614,35.382C5.294,35.799 5.878,35.883 6.658,35.877C6.804,35.877 6.804,35.877 6.953,35.878C7.278,35.879 7.603,35.878 7.928,35.877C8.162,35.878 8.395,35.878 8.629,35.879C9.196,35.88 9.764,35.88 10.332,35.88C10.993,35.879 11.653,35.881 12.314,35.882C13.493,35.885 14.672,35.887 15.851,35.888C15.957,35.888 15.957,35.888 16.065,35.888C16.637,35.888 17.209,35.889 17.781,35.889C18.282,35.889 18.783,35.889 19.284,35.89C19.354,35.89 19.425,35.89 19.498,35.89C20.673,35.891 21.848,35.893 23.024,35.896C23.683,35.897 24.343,35.898 25.003,35.897C25.622,35.897 26.242,35.898 26.862,35.899C27.09,35.9 27.318,35.9 27.545,35.899C27.855,35.898 28.165,35.899 28.475,35.901C28.612,35.9 28.612,35.9 28.752,35.898C28.834,35.899 28.917,35.9 29.002,35.901C29.109,35.901 29.109,35.901 29.219,35.901C29.395,35.938 29.395,35.938 29.59,36.23C29.632,36.517 29.632,36.517 29.59,36.816C29.383,37.049 29.213,37.187 28.902,37.243C28.65,37.257 28.401,37.259 28.149,37.256C28.052,37.257 27.955,37.257 27.856,37.258C27.531,37.259 27.207,37.257 26.883,37.256C26.651,37.256 26.419,37.257 26.186,37.258C25.621,37.259 25.055,37.258 24.489,37.257C23.83,37.255 23.17,37.255 22.511,37.256C21.334,37.256 20.157,37.255 18.98,37.253C17.839,37.25 16.698,37.25 15.557,37.251C14.314,37.252 13.07,37.252 11.827,37.25C11.695,37.25 11.562,37.25 11.429,37.25C11.364,37.25 11.298,37.25 11.231,37.25C10.772,37.249 10.313,37.25 9.854,37.25C9.235,37.25 8.616,37.249 7.998,37.247C7.771,37.247 7.544,37.247 7.317,37.247C7.007,37.248 6.696,37.247 6.386,37.245C6.252,37.246 6.252,37.246 6.116,37.247C5.015,37.236 4.205,36.912 3.423,36.14C2.662,35.319 2.524,34.519 2.525,33.429C2.525,33.333 2.525,33.238 2.524,33.139C2.524,32.818 2.523,32.496 2.523,32.175C2.523,31.944 2.522,31.714 2.522,31.483C2.52,30.792 2.52,30.101 2.519,29.409C2.519,29.083 2.519,28.757 2.518,28.432C2.517,27.348 2.516,26.264 2.516,25.18C2.516,25.11 2.516,25.041 2.516,24.97C2.516,24.9 2.516,24.831 2.516,24.759C2.516,24.618 2.516,24.477 2.516,24.337C2.516,24.267 2.516,24.197 2.515,24.125C2.515,22.994 2.513,21.862 2.511,20.731C2.509,19.569 2.508,18.406 2.507,17.244C2.507,16.591 2.507,15.939 2.505,15.287C2.503,14.673 2.503,14.059 2.504,13.446C2.504,13.221 2.504,12.996 2.503,12.771C2.502,12.463 2.502,12.155 2.503,11.847C2.502,11.759 2.502,11.671 2.501,11.58C2.509,10.535 2.818,9.67 3.516,8.887C4.467,8.062 5.47,7.964 6.688,7.97Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22.607"
            android:startY="7.956"
            android:endX="22.607"
            android:endY="37.258"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M41.211,26.855C42.538,27.852 43.491,29.304 43.75,30.957C43.888,32.702 43.642,34.125 42.773,35.645C42.709,35.709 42.645,35.773 42.578,35.84C42.598,36.235 42.843,36.43 43.116,36.694C43.161,36.739 43.207,36.784 43.254,36.83C43.353,36.926 43.452,37.023 43.551,37.119C43.709,37.271 43.865,37.425 44.021,37.579C44.466,38.016 44.911,38.451 45.359,38.885C45.632,39.151 45.904,39.418 46.176,39.686C46.279,39.788 46.383,39.889 46.488,39.989C46.634,40.13 46.779,40.273 46.923,40.416C47.006,40.497 47.089,40.577 47.174,40.66C47.408,40.979 47.444,41.21 47.461,41.602C47.327,41.885 47.228,41.966 46.942,42.09C46.487,42.09 46.18,41.778 45.855,41.481C45.739,41.362 45.739,41.362 45.621,41.241C45.532,41.151 45.444,41.061 45.352,40.968C45.306,40.92 45.26,40.872 45.212,40.823C45.065,40.673 44.918,40.523 44.771,40.374C44.352,39.95 43.934,39.524 43.518,39.097C43.263,38.836 43.006,38.575 42.749,38.315C42.651,38.216 42.554,38.117 42.457,38.017C42.322,37.877 42.186,37.74 42.049,37.602C41.972,37.523 41.895,37.444 41.815,37.363C41.617,37.183 41.617,37.183 41.391,37.223C41.171,37.323 40.979,37.445 40.778,37.579C39.456,38.395 37.728,38.6 36.211,38.312C34.568,37.906 33.171,36.962 32.227,35.547C31.285,33.931 31.01,32.238 31.449,30.411C31.914,28.767 32.921,27.386 34.418,26.532C36.632,25.401 39.117,25.466 41.211,26.855ZM33.745,28.717C32.87,29.806 32.413,31.125 32.52,32.52C32.75,33.964 33.437,35.237 34.621,36.121C35.807,36.865 37.072,37.199 38.47,36.963C39.839,36.619 40.902,35.857 41.745,34.726C42.508,33.437 42.651,32.019 42.288,30.589C41.891,29.344 41.052,28.339 39.927,27.683C37.865,26.618 35.347,26.998 33.745,28.717Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="39.353"
            android:startY="25.743"
            android:endX="39.353"
            android:endY="42.09"
            android:type="linear">
          <item android:offset="0" android:color="#FFE043C0"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M7.88,12.604C8.203,12.695 8.203,12.695 8.398,12.891C8.529,13.247 8.509,13.61 8.51,13.985C8.51,14.091 8.51,14.091 8.511,14.2C8.512,14.438 8.512,14.676 8.512,14.913C8.512,15.084 8.513,15.255 8.513,15.425C8.515,15.888 8.515,16.352 8.516,16.815C8.516,17.104 8.516,17.394 8.517,17.683C8.518,18.588 8.519,19.493 8.519,20.398C8.52,21.444 8.522,22.489 8.524,23.534C8.527,24.342 8.528,25.15 8.528,25.958C8.528,26.44 8.528,26.923 8.53,27.405C8.532,27.859 8.532,28.313 8.531,28.766C8.531,28.933 8.531,29.1 8.532,29.266C8.534,29.493 8.533,29.721 8.532,29.948C8.532,30.075 8.532,30.202 8.532,30.334C8.49,30.716 8.379,30.976 8.106,31.25C7.502,31.307 7.502,31.307 7.207,31.098C6.974,30.782 6.977,30.518 6.981,30.141C6.98,30.068 6.98,29.996 6.979,29.921C6.977,29.678 6.978,29.434 6.979,29.191C6.978,29.017 6.978,28.843 6.977,28.668C6.974,28.194 6.975,27.721 6.976,27.247C6.976,26.851 6.975,26.456 6.975,26.061C6.973,25.128 6.973,24.196 6.975,23.263C6.977,22.301 6.975,21.338 6.972,20.376C6.969,19.55 6.969,18.724 6.969,17.898C6.97,17.404 6.97,16.911 6.968,16.417C6.966,15.953 6.966,15.489 6.969,15.025C6.969,14.855 6.969,14.684 6.968,14.514C6.966,14.282 6.968,14.049 6.97,13.817C6.969,13.749 6.968,13.682 6.966,13.612C6.974,13.245 7.028,13.09 7.257,12.796C7.52,12.598 7.52,12.598 7.88,12.604Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="7.75"
            android:startY="12.602"
            android:endX="7.75"
            android:endY="31.282"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.336,12.695C19.622,12.986 19.666,13.2 19.667,13.601C19.668,13.705 19.669,13.809 19.67,13.916C19.669,14.03 19.669,14.144 19.668,14.261C19.669,14.381 19.67,14.502 19.67,14.625C19.672,14.955 19.672,15.284 19.671,15.614C19.671,15.889 19.672,16.164 19.672,16.44C19.674,17.089 19.674,17.738 19.673,18.387C19.671,19.057 19.673,19.726 19.675,20.396C19.677,20.971 19.678,21.546 19.678,22.122C19.677,22.465 19.677,22.808 19.679,23.152C19.681,23.535 19.68,23.918 19.678,24.301C19.679,24.471 19.679,24.471 19.681,24.645C19.674,25.409 19.674,25.409 19.452,25.803C19.238,25.977 19.238,25.977 18.896,26.038C18.555,25.977 18.555,25.977 18.341,25.802C18.068,25.319 18.111,24.857 18.115,24.311C18.114,24.132 18.114,24.132 18.114,23.949C18.112,23.623 18.113,23.297 18.115,22.97C18.116,22.628 18.116,22.286 18.115,21.944C18.115,21.37 18.116,20.795 18.118,20.221C18.121,19.558 18.121,18.894 18.12,18.231C18.119,17.592 18.12,16.953 18.121,16.314C18.122,16.042 18.122,15.771 18.121,15.499C18.121,15.12 18.122,14.74 18.125,14.361C18.124,14.248 18.124,14.136 18.123,14.02C18.125,13.865 18.125,13.865 18.126,13.706C18.126,13.616 18.127,13.527 18.127,13.434C18.172,13.13 18.278,12.944 18.457,12.695C18.768,12.54 19.005,12.62 19.336,12.695Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="18.895"
            android:startY="12.606"
            android:endX="18.895"
            android:endY="26.038"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22.717,12.585C23.047,12.695 23.047,12.695 23.212,12.914C23.35,13.204 23.377,13.383 23.377,13.703C23.378,13.805 23.379,13.908 23.38,14.014C23.379,14.125 23.378,14.237 23.378,14.352C23.378,14.47 23.379,14.589 23.379,14.71C23.38,15.034 23.38,15.358 23.379,15.682C23.378,16.021 23.379,16.361 23.38,16.7C23.38,17.27 23.379,17.839 23.378,18.409C23.376,19.067 23.377,19.725 23.379,20.384C23.38,20.949 23.38,21.515 23.379,22.08C23.379,22.418 23.379,22.756 23.38,23.093C23.381,23.47 23.379,23.846 23.378,24.222C23.379,24.39 23.379,24.39 23.38,24.561C23.378,24.715 23.378,24.715 23.377,24.872C23.377,24.961 23.377,25.05 23.377,25.141C23.328,25.474 23.188,25.738 22.949,25.977C22.626,26.013 22.626,26.013 22.266,25.977C22,25.764 21.889,25.585 21.839,25.247C21.823,24.931 21.821,24.618 21.823,24.302C21.822,24.122 21.822,24.122 21.82,23.938C21.819,23.609 21.819,23.28 21.819,22.951C21.82,22.676 21.819,22.4 21.818,22.126C21.817,21.477 21.817,20.828 21.819,20.179C21.82,19.511 21.819,18.842 21.816,18.173C21.813,17.598 21.812,17.023 21.813,16.449C21.814,16.106 21.813,15.763 21.812,15.42C21.809,15.037 21.811,14.655 21.813,14.272C21.812,14.159 21.811,14.045 21.81,13.928C21.819,13.165 21.819,13.165 22.101,12.768C22.363,12.598 22.363,12.598 22.717,12.585Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22.595"
            android:startY="12.585"
            android:endX="22.595"
            android:endY="26.004"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.283,12.61C15.625,12.695 15.625,12.695 15.79,12.864C15.944,13.131 15.955,13.294 15.956,13.601C15.957,13.705 15.958,13.809 15.959,13.916C15.958,14.03 15.958,14.144 15.957,14.261C15.958,14.381 15.959,14.502 15.96,14.625C15.961,14.955 15.961,15.284 15.96,15.614C15.96,15.889 15.961,16.164 15.961,16.44C15.963,17.089 15.963,17.738 15.962,18.387C15.96,19.057 15.962,19.726 15.964,20.396C15.967,20.971 15.967,21.546 15.967,22.122C15.966,22.465 15.967,22.808 15.968,23.152C15.97,23.535 15.969,23.918 15.967,24.301C15.968,24.471 15.968,24.471 15.97,24.645C15.963,25.408 15.963,25.408 15.741,25.804C15.527,25.977 15.527,25.977 15.179,26.025C14.844,25.977 14.844,25.977 14.661,25.854C14.446,25.521 14.441,25.182 14.44,24.793C14.44,24.692 14.439,24.591 14.439,24.487C14.439,24.377 14.439,24.267 14.439,24.154C14.439,24.037 14.438,23.921 14.438,23.801C14.437,23.482 14.437,23.163 14.437,22.844C14.437,22.645 14.436,22.445 14.436,22.246C14.435,21.55 14.435,20.854 14.435,20.158C14.435,19.51 14.434,18.862 14.432,18.214C14.431,17.657 14.431,17.1 14.431,16.543C14.431,16.21 14.431,15.878 14.429,15.545C14.429,15.175 14.429,14.804 14.429,14.433C14.429,14.323 14.428,14.213 14.428,14.1C14.428,13.999 14.429,13.898 14.429,13.793C14.429,13.706 14.429,13.618 14.429,13.528C14.456,13.248 14.521,13.044 14.648,12.793C14.941,12.598 14.941,12.598 15.283,12.61Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="15.199"
            android:startY="12.605"
            android:endX="15.199"
            android:endY="26.025"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M11.591,12.598C11.914,12.695 11.914,12.695 12.109,12.988C12.134,13.222 12.134,13.222 12.135,13.503C12.136,13.61 12.136,13.717 12.137,13.827C12.137,13.944 12.137,14.061 12.136,14.182C12.137,14.306 12.137,14.429 12.138,14.556C12.139,14.895 12.139,15.233 12.139,15.572C12.139,15.854 12.139,16.137 12.14,16.419C12.141,17.086 12.141,17.752 12.14,18.418C12.14,19.106 12.141,19.794 12.143,20.482C12.145,21.072 12.145,21.662 12.145,22.253C12.145,22.605 12.145,22.958 12.146,23.31C12.148,23.704 12.147,24.097 12.146,24.49C12.146,24.607 12.147,24.724 12.148,24.845C12.147,24.951 12.147,25.058 12.146,25.168C12.146,25.261 12.146,25.354 12.146,25.45C12.109,25.684 12.109,25.684 11.982,25.86C11.701,26.058 11.464,26.017 11.133,25.977C10.878,25.78 10.758,25.663 10.693,25.344C10.693,25.253 10.692,25.162 10.692,25.068C10.691,24.963 10.689,24.858 10.688,24.75C10.689,24.635 10.689,24.52 10.69,24.402C10.689,24.281 10.688,24.16 10.688,24.035C10.686,23.702 10.686,23.37 10.686,23.038C10.687,22.761 10.686,22.483 10.686,22.206C10.684,21.552 10.684,20.897 10.686,20.243C10.688,19.568 10.686,18.893 10.683,18.218C10.68,17.638 10.68,17.059 10.68,16.479C10.681,16.133 10.681,15.787 10.679,15.441C10.677,15.055 10.678,14.669 10.681,14.283C10.679,14.168 10.678,14.054 10.677,13.936C10.686,13.165 10.686,13.165 10.968,12.767C11.231,12.598 11.231,12.598 11.591,12.598Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="11.413"
            android:startY="12.598"
            android:endX="11.413"
            android:endY="26.01"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M26.434,12.604C26.758,12.695 26.758,12.695 26.953,12.891C26.978,13.114 26.978,13.114 26.979,13.398C26.979,13.505 26.98,13.613 26.981,13.724C26.98,13.842 26.98,13.96 26.98,14.083C26.98,14.207 26.981,14.331 26.982,14.459C26.983,14.801 26.983,15.142 26.983,15.484C26.983,15.769 26.983,16.054 26.983,16.339C26.985,17.011 26.985,17.683 26.984,18.355C26.983,19.049 26.985,19.743 26.987,20.437C26.988,21.032 26.989,21.627 26.989,22.222C26.989,22.578 26.989,22.934 26.99,23.289C26.992,23.686 26.991,24.083 26.99,24.479C26.99,24.597 26.991,24.716 26.992,24.837C26.991,24.945 26.99,25.053 26.99,25.164C26.99,25.258 26.99,25.352 26.99,25.448C26.953,25.684 26.953,25.684 26.825,25.86C26.541,26.061 26.311,26.024 25.977,25.977C25.713,25.763 25.6,25.584 25.55,25.247C25.534,24.931 25.532,24.618 25.534,24.302C25.533,24.122 25.533,24.122 25.531,23.938C25.529,23.609 25.529,23.28 25.53,22.951C25.531,22.676 25.53,22.4 25.529,22.126C25.528,21.477 25.528,20.828 25.53,20.179C25.531,19.511 25.53,18.842 25.527,18.173C25.524,17.598 25.523,17.023 25.524,16.449C25.525,16.106 25.524,15.763 25.522,15.42C25.52,15.037 25.522,14.655 25.524,14.272C25.523,14.159 25.522,14.045 25.521,13.928C25.53,13.165 25.53,13.165 25.812,12.767C26.074,12.598 26.074,12.598 26.434,12.604Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="26.256"
            android:startY="12.602"
            android:endX="26.256"
            android:endY="26.014"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M34.18,12.695C34.445,12.97 34.509,13.164 34.511,13.541C34.512,13.632 34.513,13.723 34.514,13.817C34.514,13.916 34.514,14.015 34.514,14.118C34.514,14.223 34.515,14.328 34.516,14.436C34.519,14.783 34.519,15.131 34.518,15.478C34.519,15.719 34.52,15.961 34.521,16.202C34.522,16.708 34.522,17.214 34.522,17.72C34.521,18.369 34.524,19.017 34.528,19.666C34.53,20.164 34.531,20.663 34.53,21.161C34.531,21.4 34.532,21.639 34.533,21.879C34.535,22.213 34.535,22.547 34.533,22.882C34.535,23.03 34.535,23.03 34.537,23.181C34.531,23.638 34.523,23.857 34.247,24.234C33.984,24.414 33.984,24.414 33.63,24.426C33.301,24.316 33.301,24.316 33.136,24.129C32.948,23.688 32.967,23.279 32.97,22.803C32.969,22.7 32.969,22.597 32.968,22.491C32.967,22.15 32.968,21.81 32.969,21.469C32.969,21.232 32.968,20.996 32.968,20.759C32.968,20.263 32.968,19.766 32.97,19.27C32.971,18.634 32.97,17.998 32.969,17.362C32.967,16.873 32.968,16.384 32.969,15.895C32.969,15.661 32.969,15.426 32.968,15.192C32.967,14.864 32.968,14.536 32.97,14.208C32.969,14.111 32.969,14.014 32.968,13.914C32.973,13.432 32.976,13.082 33.301,12.695C33.612,12.54 33.849,12.62 34.18,12.695Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="33.752"
            android:startY="12.606"
            android:endX="33.752"
            android:endY="24.426"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M30.273,12.598C30.516,12.791 30.647,12.909 30.713,13.217C30.713,13.302 30.713,13.387 30.713,13.474C30.714,13.621 30.714,13.621 30.716,13.771C30.715,13.878 30.714,13.985 30.713,14.095C30.714,14.208 30.714,14.321 30.715,14.437C30.716,14.811 30.714,15.184 30.712,15.558C30.712,15.818 30.713,16.077 30.713,16.336C30.713,16.88 30.712,17.423 30.71,17.967C30.707,18.595 30.707,19.224 30.708,19.852C30.709,20.457 30.708,21.062 30.707,21.666C30.707,21.924 30.707,22.181 30.707,22.438C30.707,22.797 30.706,23.156 30.704,23.515C30.704,23.622 30.704,23.729 30.705,23.84C30.704,23.937 30.703,24.035 30.702,24.136C30.702,24.221 30.701,24.305 30.701,24.393C30.656,24.656 30.58,24.742 30.371,24.902C30.182,24.945 30.182,24.945 29.98,24.945C29.914,24.947 29.847,24.949 29.779,24.951C29.506,24.881 29.442,24.748 29.297,24.512C29.26,24.285 29.26,24.285 29.259,24.03C29.258,23.933 29.257,23.836 29.256,23.736C29.257,23.63 29.257,23.525 29.257,23.416C29.256,23.248 29.256,23.248 29.255,23.077C29.254,22.707 29.254,22.337 29.254,21.966C29.254,21.71 29.253,21.453 29.253,21.196C29.252,20.657 29.252,20.119 29.253,19.58C29.254,18.958 29.253,18.336 29.25,17.713C29.248,17.114 29.248,16.515 29.248,15.916C29.248,15.661 29.248,15.406 29.247,15.152C29.245,14.796 29.246,14.44 29.248,14.084C29.246,13.926 29.246,13.926 29.245,13.764C29.252,13.053 29.252,13.053 29.477,12.727C29.767,12.549 29.939,12.542 30.273,12.598Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29.98"
            android:startY="12.567"
            android:endX="29.98"
            android:endY="24.951"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M37.561,12.585C37.891,12.695 37.891,12.695 38.055,12.885C38.239,13.313 38.224,13.702 38.221,14.164C38.222,14.262 38.222,14.36 38.223,14.462C38.224,14.786 38.223,15.111 38.222,15.436C38.223,15.662 38.223,15.887 38.223,16.113C38.224,16.587 38.223,17.06 38.222,17.533C38.22,18.139 38.221,18.746 38.223,19.352C38.224,19.819 38.223,20.285 38.223,20.751C38.222,20.975 38.223,21.199 38.223,21.422C38.224,21.735 38.223,22.047 38.221,22.36C38.222,22.499 38.222,22.499 38.223,22.64C38.219,23.111 38.211,23.454 37.891,23.828C37.536,23.951 37.374,23.957 37.03,23.804C36.816,23.633 36.816,23.633 36.719,23.34C36.709,23.2 36.705,23.059 36.704,22.919C36.703,22.833 36.702,22.747 36.701,22.658C36.701,22.564 36.7,22.47 36.7,22.373C36.699,22.274 36.698,22.174 36.697,22.072C36.695,21.743 36.693,21.414 36.692,21.085C36.692,20.973 36.691,20.861 36.69,20.745C36.688,20.151 36.686,19.558 36.685,18.964C36.683,18.35 36.679,17.736 36.674,17.122C36.67,16.65 36.669,16.178 36.669,15.707C36.668,15.48 36.666,15.254 36.664,15.028C36.661,14.711 36.661,14.395 36.662,14.078C36.66,13.984 36.659,13.891 36.657,13.794C36.661,13.349 36.673,13.148 36.944,12.781C37.207,12.598 37.207,12.598 37.561,12.585Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="37.44"
            android:startY="12.585"
            android:endX="37.44"
            android:endY="23.92"
            android:type="linear">
          <item android:offset="0" android:color="#FF4A4CFF"/>
          <item android:offset="1" android:color="#FFF642B7"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
