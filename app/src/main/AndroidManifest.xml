<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-feature android:name="android.hardware.usb.host" />


    <application
        android:name=".app.SwiftPOSApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.SwiftPOS"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.SwiftPOS">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data
            android:name="io.sentry.dsn"
            android:value="@string/SENTRY_DSN" />

        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />
        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
        <meta-data
            android:name="io.sentry.attach-screenshot"
            android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data
            android:name="io.sentry.attach-view-hierarchy"
            android:value="true" />

        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="1.0" />
        <!-- enable profiling when starting transactions, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.profiling.sample-rate"
            android:value="1.0" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />
    </application>

</manifest>