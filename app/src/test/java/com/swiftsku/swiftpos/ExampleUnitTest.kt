package com.swiftsku.swiftpos

import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import org.junit.Test

import org.junit.Assert.*

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun testDollarsToCentsLogic() {
        for (dollar in 0..999) {
            for (cent in 0..99) {
                val amount: Float = dollar + cent / 100.0f
                val cents: Int = amount.toDouble().convertDollarToCentPrecisely()
                val expectedCents: Int = dollar * 100 + cent
                assertEquals(expectedCents, cents)
            }
        }
    }
}