package com.swiftsku.swiftpos.modules.printer

import android.util.Log
import com.orhanobut.logger.Logger
import com.swiftsku.swiftpos.data.model.EODReport
import kotlinx.serialization.json.Json
import org.junit.Test
import org.junit.jupiter.api.Assertions.*


class EODReportTemplatePrinterKtTest {

    private val eR = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }.decodeFromString<EODReport>(
        "{\n" +
                "    \"doc_type\": \"report_item\",\n" +
                "    \"store_code\": \"StoreCode\",\n" +
                "    \"store_name\": \"Manju Store\",\n" +
                "    \"report_type\": \"eod\",\n" +
                "    \"businessdate\": \"2023-05-16\",\n" +
                "    \"last_mod_stamp\": \"epoch\",\n" +
                "    \"stats\": {\n" +
                "        \"Sale\": {\n" +
                "            \"Count\": \"1\",\n" +
                "            \"Cash\": \"8.55\",\n" +
                "            \"Debit Card\": \"0\",\n" +
                "            \"Credit Card\": \"0\",\n" +
                "            \"Other Card\": \"0\",\n" +
                "            \"Total\": \"8.55\"\n" +
                "        },\n" +
                "        \"Merchandise\": {\n" +
                "            \"Count\": \"1\",\n" +
                "            \"Cash\": \"8.55\",\n" +
                "            \"Debit Card\": \"0\",\n" +
                "            \"Credit Card\": \"0\",\n" +
                "            \"Other Card\": \"0\",\n" +
                "            \"Total\": \"8.55\"\n" +
                "        },\n" +
                "        \"Fuel\": {\n" +
                "            \"Count\": \"1\",\n" +
                "            \"Cash\": \"8.55\",\n" +
                "            \"Debit Card\": \"0\",\n" +
                "            \"Credit Card\": \"0\",\n" +
                "            \"Other Card\": \"0\",\n" +
                "            \"Total\": \"8.55\"\n" +
                "        },\n" +
                "        \"Payout\": {},\n" +
                "        \"Other\": {\n" +
                "            \"Count\": \"1\",\n" +
                "            \"Cash\": \"8.55\",\n" +
                "            \"Debit Card\": \"0\",\n" +
                "            \"Credit Card\": \"0\",\n" +
                "            \"Other Card\": \"0\",\n" +
                "            \"Total\": \"8.55\"\n" +
                "        }\n" +
                "    }\n" +
                "}"
    )

    @Test
    fun testSVG() {
        val svg = getSVG(eR)
        println(svg)
    }
}